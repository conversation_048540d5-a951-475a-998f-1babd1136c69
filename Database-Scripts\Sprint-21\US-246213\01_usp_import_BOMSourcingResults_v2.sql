﻿SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

/* 
===========================================================================================
TASK      		UPDATED BY     	DATE         	ACTION 		DESCRIPTION
[US-214760]		An.TranTan		14-Jan-2025		CREATE		Import BOM Sourcing Results
[US-232568]		An.TranTan		17-Feb-2025		Update		Allow import for multiple req in single file
[US-232568]		An.TranTan		19-Feb-2025		Update		Update customer ref no for multiple req
[US-232568]		An.TranTan		28-Feb-2025		Update		Price of sourcing result should be Sell Price in template
[US-232569]		An.TranTan		21-Mar-2025		Update		Update logic: import data from tbBomImportSourcingTemp
[US-246213]		Trang.Pham		30-Jul-2025		Create		Create v2: Add parameter BomNo and using TRY_PARSE to parse datetime
===========================================================================================
*/
CREATE OR ALTER PROCEDURE [dbo].[usp_Import_BOMSourcingResults_v2]
    @UserID INT
    ,@BomNo INT
    ,@ImportCount INT OUTPUT
    ,@ImportMessage NVARCHAR(2000) OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    DECLARE @OriginalFilename NVARCHAR(200)
            ,@GeneratedFilename NVARCHAR(200)

    DECLARE @InsertedOffers TABLE (OfferId INT);
    DECLARE @tbTargetRequirement TABLE(
        CustomerRequirementId INT
        , CustomerRequirementNumber INT
        , BOMNo INT
        , ClientNo INT
        , ProductNo INT
        , ManufacturerNo INT
        , GlobalProductNo INT
        , OfferProductNo INT
        , ClientCompanyNo INT	--represent company of DMCC in client side
    );

    BEGIN TRY
    BEGIN TRANSACTION
    IF EXISTS
    (
        SELECT TOP 1 1
        FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
        WHERE CreatedBy = @UserID
            AND BOMNo = @BomNo
    )
    BEGIN

        IF OBJECT_ID('tempdb..#tbBOMSourcing_ToBeImported') IS NOT NULL
            DROP TABLE #tbBOMSourcing_ToBeImported
        CREATE TABLE #tbBOMSourcing_ToBeImported
        (
            [BOMSourcingImportId] [int] IDENTITY(1,1) NOT NULL PRIMARY KEY,
            [CustomerRequirementNo] [int] NOT NULL,
            [SupplierNo] [int] NULL,
            [SupplierName] [nvarchar](256) NULL,
            [ManufacturerNo] [int] NULL,
            [ManufacturerName] [nvarchar](256) NULL,
            [SupplierPart] [nvarchar](30) NULL,
            [Quantity] [int] NOT NULL,
            [SupplierCost] [float] NULL,
            [SPQ] [nvarchar](100) NULL,
            [MOQ] [nvarchar](100) NULL,
            [MSLLevelNo] [int] NULL,
            [MSL] [nvarchar](100) NULL,
            [Notes] [nvarchar](500) NULL,
            [DateCode] [nvarchar](5) NULL,
            [QtyInStock] [int] NULL,
            [OfferStatusNo] [int] NULL,
            [BuyPrice] [float] NULL,
            [SellPrice] [float] NULL,
            [ShippingCost] [float] NULL,
            [PackageNo] [int] NULL,
            [ROHSStatus] [nvarchar](10) NULL,
            [CurrencyNo] [int] NULL,
            [FactorySealed] [nvarchar](10) NULL,
            [RegionNo] [int] NULL,
            [LeadTime] [nvarchar](10) NULL,
            [LastTimeBuy] [nvarchar](10) NULL,
            [DeliveryDate] [datetime] NULL,
            [CustomerRefNo] [nvarchar](200) NULL,
            [OriginalFilename] [nvarchar](200) NULL,
            [GeneratedFilename] [nvarchar](200) NULL,
        );
        --get mfr from temp table after correction
        ;WITH cteManufacturer AS(
            SELECT DISTINCT Manufacturer 
            FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
            WHERE CreatedBy = @UserID
                AND BomNo = @BomNo
        )SELECT 
            MIN(m.ManufacturerId) AS ManufacturerId
            ,m.ManufacturerName
        INTO #tempImportedManufacturer
        FROM cteManufacturer cte
        JOIN tbManufacturer m WITH(NOLOCK) ON m.ManufacturerName = cte.Manufacturer
            AND m.Inactive = 0
        GROUP BY m.ManufacturerName;
        ------------------------------------------------------------------

        --get supplier from temp table after correction
        ;WITH cteSupplier AS(
            SELECT DISTINCT SupplierName
            FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp
            WHERE CreatedBy = @UserID
                AND BOMNo = @BomNo
        )SELECT
            MIN(co.CompanyId) AS SupplierId
            ,co.CompanyName AS SupplierName
        INTO #tempImportedSupplier
        FROM cteSupplier cte
        JOIN tbCompany co WITH(NOLOCK) ON co.CompanyName = cte.SupplierName
        WHERE co.Inactive = 0 
            AND co.ClientNo = 114
        GROUP BY co.CompanyName;
        ------------------------------------------------------------------
        --get package from temp table after correction
        ;WITH ctePackage AS(
            SELECT DISTINCT Package 
            FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp 
            WHERE CreatedBy = @UserID
                AND BOMNo = @BomNo
                AND ISNULL(Package,'') <> ''
        )
        SELECT 
            MIN(p.PackageId) AS PackageId,
            p.PackageDescription AS PackageName
        INTO #tempImportedPackage
        FROM ctePackage cte
        JOIN tbPackage p WITH(NOLOCK) on p.PackageDescription = cte.Package
        GROUP BY p.PackageDescription;
        ------------------------------------------------------------------
        --get msl from temp table after correction
        DECLARE @tbMSLLevel TABLE(MSLLevelId INT, MSL NVARCHAR(200));
        ;WITH cteMSL AS(
            SELECT DISTINCT MSL 
            FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp  
            WHERE CreatedBy = @UserID
                AND BOMNo = @BomNo
                AND ISNULL(MSL,'') <> ''
        )INSERT INTO @tbMSLLevel (MSLLevelId, MSL)
        SELECT 
            msl.MSLLevelId, cte.MSL
        FROM cteMSL cte
        JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = cte.MSL
        UNION ALL
        SELECT 
            msl.MSLLevelId, cte.MSL
        FROM cteMSL cte
        JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevel = CONCAT('MSL ', cte.MSL)
        ------------------------------------------------------------------
        INSERT INTO #tbBOMSourcing_ToBeImported
        (
            [CustomerRequirementNo]
            ,[SupplierNo]
            ,[SupplierName]
            ,[ManufacturerNo]
            ,[ManufacturerName]
            ,[SupplierPart]
            ,[Quantity]
            ,[SupplierCost]
            ,[SPQ]
            ,[MOQ]
            ,[MSLLevelNo]
            ,[MSL]
            ,[Notes]
            ,[DateCode]
            ,[QtyInStock]
            ,[OfferStatusNo]
            ,[BuyPrice]
            ,[SellPrice]
            ,[ShippingCost]
            ,[PackageNo]
            ,[ROHSStatus]
            ,[CurrencyNo]
            ,[FactorySealed]
            ,[RegionNo]
            ,[LeadTime]
            ,[LastTimeBuy]
            ,[DeliveryDate]
            ,[CustomerRefNo]
            ,[OriginalFilename]
            ,[GeneratedFilename]
        )
        SELECT 
            cr.CustomerRequirementId--[CustomerRequirementNo]
            ,s.SupplierId--[SupplierNo]
            ,s.SupplierName--[SupplierName]
            ,m.ManufacturerId--[ManufacturerNo]
            ,m.ManufacturerName--[ManufacturerName]
            ,temp.SupplierPart--[SupplierPart]
            ,CAST(temp.OfferedQuantity AS INT)--[Quantity]
            ,CAST(temp.SupplierCost AS FLOAT)--[SupplierCost]
            ,temp.SPQ--[SPQ]
            ,temp.MOQ--[MOQ]
            ,(SELECT TOP 1 MSLLevelId FROM @tbMSLLevel m WHERE m.MSL = temp.MSL)--[MSLLevelNo]
            ,temp.MSL--[MSL]
            ,temp.Notes--[Notes]
            ,temp.DateCode--[DateCode]
            ,CAST(temp.QtyInStock AS INT)--[QtyInStock]
            ,os.OfferStatusId--[OfferStatusNo]
            ,CAST(temp.BuyPrice AS FLOAT)--[BuyPrice]
            ,CAST(temp.SellPrice AS FLOAT)--[SellPrice]
            ,CAST(temp.ShippingCost AS FLOAT)--[ShippingCost]
            ,pack.PackageId--[PackageNo]
            ,case when ISNULL(temp.[ROHS], '') = 'Y' then 'Y' else 'N' end--[ROHSStatus]
            ,case when temp.Currency = ISNULL(pocr.CurrencyCode,'') then pocr.CurrencyId 
                else hubcr.CurrencyId
            end--[CurrencyNo]
            ,temp.FactorySealed--[FactorySealed]
            ,r.RegionId--[RegionNo]
            ,temp.LeadTime--[LeadTime]
            ,temp.LastTimeBuy--[LastTimeBuy]
            ,TRY_PARSE(temp.DeliveryDate AS DATE USING 'en-GB')--[DeliveryDate]
            ,temp.CustomerRefNo--[CustomerRefNo]
            ,temp.OriginalFilename--[OriginalFilename]
            ,temp.GeneratedFilename--[GeneratedFilename]
        FROM BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp temp
        JOIN tbCustomerRequirement cr WITH(NOLOCK) 
            ON cr.CustomerRequirementNumber = temp.CustomerRequirementNumber AND cr.BOMNo = temp.BOMNo
        JOIN #tempImportedSupplier s ON s.SupplierName = temp.SupplierName
        JOIN #tempImportedManufacturer m ON m.ManufacturerName = temp.Manufacturer
        JOIN tbCompany c WITH(NOLOCK) on c.CompanyId = s.SupplierId
        LEFT JOIN tbCurrency pocr WITH(NOLOCK) on pocr.CurrencyId = c.POCurrencyNo
        LEFT JOIN tbCurrency hubcr WITH(NOLOCK) on hubcr.CurrencyCode = temp.Currency
            AND hubcr.ClientNo = 114 AND hubcr.Buy = 1 and hubcr.Inactive = 0
        LEFT JOIN tbOfferStatus os WITH(NOLOCK) on os.[Name] = temp.OfferStatus
        LEFT JOIN #tempImportedPackage pack WITH(NOLOCK) on pack.PackageName = temp.Package
        LEFT JOIN tbRegion r WITH(NOLOCK) on r.RegionName = temp.Region
        WHERE temp.CreatedBy = @UserID
            AND temp.BOMNo = @BomNo;

        /*================ Get unique data from table tbBOMSourcing_ToBeImported*/
        INSERT INTO @tbTargetRequirement
        (
            CustomerRequirementId
            , CustomerRequirementNumber
            , BOMNo 
            , ClientNo 
            , ProductNo 
            , ManufacturerNo 
            , GlobalProductNo 
            , OfferProductNo 
            , ClientCompanyNo
        )
        SELECT DISTINCT 
            cr.CustomerRequirementId
            ,cr.CustomerRequirementNumber
            ,cr.BOMNo
            ,cr.ClientNo
            ,cr.ProductNo
            ,cr.ManufacturerNo
            ,p.GlobalProductNo
            ,p1.ProductId
            ,com.CompanyId
        --FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported s WITH(NOLOCK)
        FROM #tbBOMSourcing_ToBeImported s
        JOIN tbCustomerRequirement cr WITH(NOLOCK) ON cr.CustomerRequirementId = s.CustomerRequirementNo
        LEFT JOIN tbProduct p WITH(NOLOCK) ON p.ProductId = cr.ProductNo
        LEFT JOIN tbProduct p1 WITH(NOLOCK) ON p1.GlobalProductNo = p.GlobalProductNo
            AND p1.ClientNo = cr.ClientNo
            AND p1.Inactive = 0
        LEFT JOIN tbCompany com WITH(NOLOCK) on com.ClientNo = cr.ClientNo AND com.IsPOHub = 1
        --WHERE s.CreatedBy = @UserID; 

        SELECT TOP 1 
            @OriginalFilename = OriginalFilename
            ,@GeneratedFilename = GeneratedFilename
        --FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;
        FROM #tbBOMSourcing_ToBeImported;
        /*==================================================================================*/

        --insert into offer, re-use logic from usp_insert_SourcingResultWithOffer
        INSERT INTO [BorisGlobalTraderImports].dbo.tboffer
        (
            [FullPart]
            ,[Part]
            ,[SupplierNo]
            ,[SupplierName]
            ,[CurrencyNo]
            ,[ManufacturerNo]
            ,[ManufacturerName]
            ,[ProductNo]
            ,[PackageNo]
            ,[DateCode]
            ,[Quantity]
            ,[Price]
            ,[OriginalEntryDate]
            ,[UpdatedBy]
            ,[DLUP]
            ,[OfferStatusNo]
            ,[OfferStatusChangeDate]
            ,[OfferStatusChangeLoginNo]
            ,[Notes]
            ,[SPQ]
            ,[LeadTime]
            ,[ROHSStatus]
            ,[FactorySealed]
            ,[MSL]
            ,[SupplierManufacturerName]
            ,[SupplierDateCode]
            ,[SupplierPackageType]
            ,[SupplierMOQ]
            ,[SupplierTotalQSA]
            ,[SupplierLTB]
            ,[SupplierNotes]
            ,[IsPoHub]
            ,[MSLLevelNo]
            ,[SellPrice]
            ,[ShippingCost]
            ,[RegionNo]
            ,[DeliveryDate]
            ,[Salesman]
            ,[ReferenceRequirementNo]
        )
        OUTPUT Inserted.OfferId INTO @InsertedOffers(OfferId)
        SELECT 
            dbo.Ufn_get_fullpart(s.SupplierPart)
            ,s.SupplierPart
            ,s.SupplierNo
            ,s.SupplierName
            ,s.CurrencyNo
            ,s.ManufacturerNo
            ,s.ManufacturerName
            ,tr.OfferProductNo--@ReqProductNo
            ,s.PackageNo
            ,s.DateCode
            ,s.Quantity
            ,s.BuyPrice
            ,GETDATE()		--OriginalEntryDate
            ,@UserId		--updated by
            ,GETDATE()		--dlup
            ,s.OfferStatusNo
            ,GETDATE()		--OfferStatusChangeDate
            ,@UserId		--OfferStatusChangeLoginNo
            ,s.Notes
            ,s.SPQ
            ,s.LeadTime
            ,s.ROHSStatus
            ,s.FactorySealed
            ,msl.MSLLevel
            ,s.ManufacturerName
            ,s.DateCode
            ,p.PackageName
            ,s.MOQ
            ,s.QtyInStock
            ,s.LastTimeBuy
            ,s.Notes
            ,1 --IsPOHub
            ,s.MSLLevelNo
            ,s.SellPrice
            ,s.ShippingCost
            ,s.RegionNo
            ,s.DeliveryDate
            ,@UserId --salesman
            ,s.CustomerRequirementNo	--reference requirement no
        --FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported s
        FROM #tbBOMSourcing_ToBeImported s
        JOIN @tbTargetRequirement tr on tr.CustomerRequirementId = s.CustomerRequirementNo
        LEFT JOIN tbPackage p WITH(NOLOCK) ON p.PackageId = s.PackageNo
        LEFT JOIN tbMSLLevel msl WITH(NOLOCK) ON msl.MSLLevelId = s.MSLLevelNo
        --WHERE s.CreatedBy = @UserID;

        --INSERT to tbSourcingResult, re-use logic: usp_insert_SourcingResult_From_QuotesToClient
        --get offer currencies-------------
        SELECT 
            o.OfferId
            ,ISNULL(o.CurrencyNo,0) AS OfferCurrencyNo
            ,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, 0), GETDATE()) AS BuyExchangeRate
            ,l.LinkMultiCurrencyId AS LinkMultiCurrencyNo
            ,l.SupplierCurrencyNo AS ClientCurrencyNo
        INTO #tempOfferCurrency
        FROM [BorisGlobalTraderImports].dbo.tboffer o WITH(NOLOCK)
        JOIN @InsertedOffers i on i.OfferId = o.OfferId
        JOIN @tbTargetRequirement req on o.ReferenceRequirementNo = req.CustomerRequirementId
        LEFT JOIN tbCurrency c WITH(NOLOCK) ON c.CurrencyId = o.CurrencyNo 
        LEFT JOIN tbLinkMultiCurrency l WITH(NOLOCK) on l.GlobalCurrencyNo = c.GlobalCurrencyNo
        WHERE l.ClientNo = req.ClientNo AND c.ClientNo = 114;

        -----------------------------------------
        INSERT INTO dbo.tbSourcingResult
        (
            CustomerRequirementNo,
            SourcingTable,
            SourcingTableItemNo,
            FullPart,
            Part,
            ManufacturerNo,
            DateCode,
            ProductNo,
            PackageNo,
            Quantity,
            Price,
            CurrencyNo,
            OriginalEntryDate,
            Salesman,
            OfferStatusNo,
            OfferStatusChangeDate,
            OfferStatusChangeLoginNo,
            SupplierNo,
            UpdatedBy,
            DLUP,
            TypeName,
            Notes,
            ROHS,
            POHubCompanyNo,
            SupplierPrice,
            ClientCompanyNo,
            EstimatedShippingCost,
            ClientCurrencyNo,
            SupplierManufacturerName,
            SupplierDateCode,
            SupplierPackageType,
            SupplierProductType,
            SupplierMOQ,
            SupplierTotalQSA,
            SupplierLTB,
            SupplierNotes,
            SPQ,
            LeadTime,
            ROHSStatus,
            FactorySealed,
            MSLLevelNo,
            MSL,
            Buyer,
            ActualPrice,
            ActualCurrencyNo,
            ExchangeRate,
            LinkMultiCurrencyNo,
            DeliveryDate,
            RegionNo
        )
        SELECT 
            req.CustomerRequirementId
            ,'OFPH'
            ,o.OfferId
            ,o.FullPart
            ,o.Part
            ,ISNULL(o.ManufacturerNo,0)
            ,o.DateCode
            ,ISNULL(o.ProductNo, req.ProductNo)
            ,ISNULL(o.PackageNo,0)
            ,ISNULL(o.Quantity,0)
            ,ISNULL(o.SellPrice,0)
            ,dbo.ufn_get_HUB_DefaultCurrencyNo(isnull(o.CurrencyNo, company.POCurrencyNo), req.ClientNo, 114)
            ,o.OriginalEntryDate
            ,o.Salesman
            ,o.OfferStatusNo
            ,o.OfferStatusChangeDate
            ,o.OfferStatusChangeLoginNo
            ,o.SupplierNo
            ,@UserID		--updated by
            ,GETDATE()		--dlup
            ,''	--type name
            ,o.Notes
            ,o.ROHS
            ,o.SupplierNo		--POHubCompanyNo
            ,((isnull(o.Price, 0) / oc.BuyExchangeRate))	--SupplierPrice
            ,req.ClientCompanyNo
            ,ISNULL(o.ShippingCost,0)
            ,oc.ClientCurrencyNo
            ,o.ManufacturerName
            ,o.DateCode
            ,o.SupplierPackageType
            ,pd.ProductDescription
            ,o.SupplierMOQ
            ,ISNULL(TRY_CAST(o.SupplierTotalQSA AS INT),0)
            ,o.SupplierLTB
            ,o.Notes
            ,o.SPQ
            ,o.LeadTime
            ,o.ROHSStatus
            ,o.FactorySealed
            ,o.MSLLevelNo
            ,o.MSL
            ,@UserId				--buyer
            ,ISNULL(o.Price,0)		--ActualPrice
            ,ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0))		--ActualCurrencyNo
            ,dbo.ufn_get_exchange_rate(ISNULL(o.CurrencyNo, isnull(company.POCurrencyNo, 0)), GETDATE())		--ExchangeRate
            ,oc.LinkMultiCurrencyNo
            ,o.DeliveryDate
            ,o.RegionNo
        FROM [BorisGlobalTraderImports].dbo.tbOffer o
        JOIN @InsertedOffers i on i.OfferId = o.OfferId
        LEFT JOIN @tbTargetRequirement req on req.CustomerRequirementId = o.ReferenceRequirementNo
        LEFT JOIN #tempOfferCurrency oc ON oc.OfferId = o.OfferId
        LEFT JOIN dbo.tbCompany company on o.SupplierNo = company.CompanyId
        LEFT JOIN dbo.tbProduct pd on pd.ProductId = req.ProductNo
        
        --update flags for requirement
        ;with cteCustomerRef as(
            SELECT  CustomerRequirementNo
                    , CustomerRefNo
                    , ROW_NUMBER() OVER (PARTITION BY CustomerRequirementNo ORDER BY CustomerRefNo) AS row_num
            --FROM BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported
            --WHERE CreatedBy = @UserID
            FROM #tbBOMSourcing_ToBeImported
        )
        UPDATE cr
        SET cr.REQStatus = 3
            , cr.CustomerRefNo = cte.CustomerRefNo
            , cr.HasHubSourcingResult = 1
        FROM tbCustomerRequirement cr
        JOIN cteCustomerRef cte on cte.CustomerRequirementNo = cr.CustomerRequirementId
        WHERE cte.row_num = 1;

        --------------------------------------------------------------------
        SELECT @ImportCount = COUNT(1) FROM @InsertedOffers;
        DECLARE @CsvLogMessage NVARCHAR(1000);
        /********* Save imported file to HUBRFQ Uploaded Documents *********/
        INSERT INTO [tbBOMCSV] 
        (
            [BOMNo]
            ,[Caption]
            ,[FileName]
            ,[UpdatedBy]
            ,[DLUP]
            ,[ImportType]
        )VALUES(@BOMNo, @OriginalFilename, @GeneratedFilename, @UserId, GETDATE(), 'SRCIMPORT')
        
        SET @CsvLogMessage = CONCAT('Import sourcing results succeed: ' , CAST(@ImportCount AS NVARCHAR(10)), ' row(s).')
        INSERT INTO dbo.[tbBomCsvLog]
        (
            [BOMNo]
            ,[FileName]
            ,[Status]
            ,[Message]
            ,[DLUP]
        )VALUES(@BOMNo, @OriginalFilename, 1, @CsvLogMessage, GETDATE())
        --------------------------------------------------------------------
        /*============== Clear all temp data ================*/
        --DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_tempData WHERE CreatedBy = @UserID;
        --DELETE BorisGlobalTraderImports.dbo.tbBOMSourcing_ToBeImported WHERE CreatedBy = @UserID;
        DELETE BorisGlobalTraderImports.dbo.tbBomImportSourcingTemp WHERE CreatedBy = @UserID;
        DROP TABLE #tbBOMSourcing_ToBeImported
        DROP TABLE #tempImportedManufacturer
        DROP TABLE #tempImportedSupplier
        DROP TABLE #tempImportedPackage
        DROP TABLE #tempOfferCurrency;

        --Set return message
        SET @ImportMessage = @CsvLogMessage;
    END
    COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION
        SET @ImportCount = -1;
        SET @ImportMessage = Error_message();
    END CATCH
END
