﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Core.Domain.Entities
{
    public class QuoteListReadModel
    {
        public int? QuoteId { get; set; }
        public int? QuoteNumber { get; set; }
        public string? Part { get; set; }
        public double? Price { get; set; }
        public string? CurrencyCode { get; set; }
        public int? Quantity { get; set; }
        public DateTime? DateQuoted { get; set; }
        public string? CompanyName { get; set; }
        public int? CompanyNo { get; set; }
        public string? ContactName { get; set; }
        public int? ContactNo { get; set; }
        public byte? ROHS { get; set; }
        public double? TotalInBase { get; set; }
        public double? TotalValue { get; set; }
        public int? CurrencyNo { get; set; }
        public int? Salesman { get; set; }
        public string? SalesmanName { get; set; }
        public string? QuoteStatusName { get; set; }
        public double? OfferProfit { get; set; }
        public long? RowNum { get; set; }
        public bool? AS6081 { get; set; }
        public DateTime? QuoteOfferedDate { get; set; }
        public string? DateOfferStatus { get; set; }
        public int? TaskCount { get; set; }
        public int? HasUnFinishedTask { get; set; }
        public int? RowCnt { get; set; }
    }
}
