﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Cheque" xml:space="preserve">
    <value>Cheque</value>
  </data>
  <data name="Comment" xml:space="preserve">
    <value>Comment</value>
  </data>
  <data name="CreditCard" xml:space="preserve">
    <value>Credit Card</value>
  </data>
  <data name="Days" xml:space="preserve">
    <value>Days</value>
  </data>
  <data name="DeliveryDate" xml:space="preserve">
    <value>Delivery Date</value>
  </data>
  <data name="EarlyPaymentRequest" xml:space="preserve">
    <value>Early Payment Request</value>
  </data>
  <data name="InAdvance" xml:space="preserve">
    <value>In Advance</value>
  </data>
  <data name="Name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="Net" xml:space="preserve">
    <value>Net</value>
  </data>
  <data name="New" xml:space="preserve">
    <value>New?</value>
  </data>
  <data name="OrderDetails" xml:space="preserve">
    <value>ORDER DETAILS</value>
  </data>
  <data name="Other" xml:space="preserve">
    <value>Other</value>
  </data>
  <data name="PaymentMethod" xml:space="preserve">
    <value>PAYMENT METHOD</value>
  </data>
  <data name="PaymentTerms" xml:space="preserve">
    <value>PAYMENT TERMS</value>
  </data>
  <data name="PoNumber" xml:space="preserve">
    <value>PO Number</value>
  </data>
  <data name="References" xml:space="preserve">
    <value>REFERENCES</value>
  </data>
  <data name="Specify" xml:space="preserve">
    <value>Specify</value>
  </data>
  <data name="SubDayPayment" xml:space="preserve">
    <value>Sub-30 Day Payment</value>
  </data>
  <data name="Supplier" xml:space="preserve">
    <value>Supplier</value>
  </data>
  <data name="TelegraphicTransfer" xml:space="preserve">
    <value>TT</value>
  </data>
  <data name="UponReceipt" xml:space="preserve">
    <value>Upon Receipt</value>
  </data>
  <data name="ValueAndCurrency" xml:space="preserve">
    <value>Value &amp; Currency</value>
  </data>
  <data name="Address" xml:space="preserve">
    <value>Address</value>
  </data>
  <data name="Tel" xml:space="preserve">
    <value>Tel</value>
  </data>
  <data name="Fax" xml:space="preserve">
    <value>Fax</value>
  </data>
  <data name="Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Sales" xml:space="preserve">
    <value>SALES</value>
  </data>
  <data name="ProFormaAttached" xml:space="preserve">
    <value>Pro Forma Attached?</value>
  </data>
  <data name="RaisedBy" xml:space="preserve">
    <value>Raised by</value>
  </data>
  <data name="Date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="Manager" xml:space="preserve">
    <value>MANAGER</value>
  </data>
  <data name="SORSigned" xml:space="preserve">
    <value>SOR Signed?</value>
  </data>
  <data name="FORStock" xml:space="preserve">
    <value>FOR Stock?</value>
  </data>
  <data name="ValuesCorrect" xml:space="preserve">
    <value>Values Correct?</value>
  </data>
  <data name="Authorized" xml:space="preserve">
    <value>Authorized</value>
  </data>
  <data name="Authorise" xml:space="preserve">
    <value>Authorise</value>
  </data>
  <data name="AccountsOnly" xml:space="preserve">
    <value>ACCOUNTS ONLY</value>
  </data>
  <data name="ERAIMember" xml:space="preserve">
    <value>ERAI Member?</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="ERAIReported" xml:space="preserve">
    <value>ERAI Reported?</value>
  </data>
  <data name="OutStandingDebitNotes" xml:space="preserve">
    <value>Out-standing Debit notes on P/L?</value>
  </data>
  <data name="TotalValue" xml:space="preserve">
    <value>Total Value</value>
  </data>
  <data name="AdvancePayment" xml:space="preserve">
    <value>Advance Payment on Open Orders?</value>
  </data>
  <data name="Comments" xml:space="preserve">
    <value>Comments</value>
  </data>
  <data name="SalesLedgerTerms" xml:space="preserve">
    <value>Sales Ledger Terms</value>
  </data>
  <data name="SalesLedgerOverdue" xml:space="preserve">
    <value>Sales Ledger Overdue?</value>
  </data>
  <data name="PaymentAuthorizedBy" xml:space="preserve">
    <value>Payment Authorized By</value>
  </data>
  <data name="Countersigned" xml:space="preserve">
    <value>Countersigned</value>
  </data>
  <data name="WhereApplicable" xml:space="preserve">
    <value>where applicable</value>
  </data>
  <data name="Complete" xml:space="preserve">
    <value>Complete</value>
  </data>
  <data name="FollowThe" xml:space="preserve">
    <value>Follow the</value>
  </data>
  <data name="ForMoreDetails" xml:space="preserve">
    <value>for more details</value>
  </data>
  <data name="RejectEPR" xml:space="preserve">
    <value>Reject EPR</value>
  </data>
  <data name="LastEPRRejectedDetails" xml:space="preserve">
    <value>Last EPR Rejected Details</value>
  </data>
  <data name="SaveAndSend" xml:space="preserve">
    <value>Save &amp; Send</value>
  </data>
  <data name="SaveAndPrint" xml:space="preserve">
    <value>Save &amp; Print</value>
  </data>
  <data name="DeleteEPR" xml:space="preserve">
    <value>Delete EPR</value>
  </data>
</root>