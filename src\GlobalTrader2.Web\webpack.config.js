const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');

const cdnUrl = '';

module.exports = {
    
    entry: {
        "components-company-quick-browse": './wwwroot/js/components/company-quick-browse.js',
        "components-manufacturer-quick-browse": './wwwroot/js/components/manufacturer-quick-browse.js',
        "components-quick-jump": './wwwroot/js/components/quick-jump.js',

		"contact-home": './wwwroot/js/modules/contact/index.js',
        "contact-all-companies": './wwwroot/js/modules/contact/all-companies/all-companies.js',
        "contact-add-company": './wwwroot/js/modules/contact/all-companies/add-company/add-company.js',
        "contact-company-details": './wwwroot/js/modules/contact/all-companies/details/company-details.js',
        "contact-company-details-credit-limit": './wwwroot/js/modules/contact/all-companies/details/credit-limit/credit-limit.js',
        "contact-contacts": './wwwroot/js/modules/contact/contacts/contacts.js',
        "contact-contact-details": './wwwroot/js/modules/contact/contacts/details/contact-details.js',
        "contact-customer-group-code": './wwwroot/js/modules/contact/customer-group-code/customer-group-code.js',
        "contact-manufacturers": './wwwroot/js/modules/contact/manufacturers/manufacturers.js',
        "contact-manufacturers-add-in-group-code": './wwwroot/js/modules/contact/manufacturers/add-in-group-code/add-in-group-code.js',
        "contact-manufacturers-add-new-manufacturer": './wwwroot/js/modules/contact/manufacturers/add-new-manufacturer/add-new-manufacturer.js',
        "contact-manufacturer-details": './wwwroot/js/modules/contact/manufacturers/details/manufacturer-details.js',
        "contact-supplier-manufacturer-search": './wwwroot/js/modules/contact/manufacturers/details/supplier-manufacturer-search/supplier-manufacturer-search.js',
        
        "contact-components-contact-quick-browse": './wwwroot/js/modules/contact/contacts/details/components/contact-quick-browse.js',
        "orders-requirements": './wwwroot/js/modules/orders/requirements/requirements.js',
        "orders-add-new-requirement": './wwwroot/js/modules/orders/requirements/containers/add-new-requirement.js',
        "orders-requirement-details": './wwwroot/js/modules/orders/requirements/details/details.js',
        "orders-sourcing": './wwwroot/js/modules/orders/sourcing/sourcing.js',
        "orders-hubrfq": './wwwroot/js/modules/orders/hubrfq/hubrfq.js',        
        "orders-add-new-hubrfq": './wwwroot/js/modules/orders/hubrfq/containers/add-new-hubrfq.js',
        "orders-auto-sourcing": './wwwroot/js/modules/orders/sourcing/components/auto-sourcing.js',
        "orders-hubrfq-detail": './wwwroot/js/modules/orders/hubrfq/details/hubrfq-detail.js',
        "orders-assign-hubrfq": './wwwroot/js/modules/orders/hubrfq/assign/assign-hubrfq.js',
        "orders-print": './wwwroot/js/modules/orders/print/print.js',
        "orders-purchase-requisitions": './wwwroot/js/modules/orders/purchase-requisitions/purchase-requisitions.js',
        "orders-purchase-requisition-detail": './wwwroot/js/modules/orders/purchase-requisitions/details/purchase-requisition-detail.js',
        "orders-sales-orders": './wwwroot/js/modules/orders/sales-orders/sales-orders.js',
        "orders-add-sales-orders": './wwwroot/js/modules/orders/sales-orders/add/add-sales-orders.js',
        "orders-sales-orders-lines": './wwwroot/js/modules/orders/sales-orders/details/components/lines/lines.js',
        "orders-sales-orders-export-approval-status": './wwwroot/js/modules/orders/sales-orders/details/components/export-approval-status/export-approval-status.js',
        "orders-add-edit-sourcing-results": './wwwroot/js/modules/orders/sourcing/components/add-edit-sourcing-results.js',
        "orders-hubrfq-ppv-bom-qualification-detail": './wwwroot/js/modules/orders/hubrfq/containers/ppv-bom-qualification-detail.js',
        "orders-add-offer-auto-sourcing": './wwwroot/js/modules/orders/sourcing/components/add-offer-auto-sourcing.js',
        "orders-import-sourcing": './wwwroot/js/modules/orders/hubrfq/containers/import-sourcing.js',
        "orders-remove-offer-auto-sourcing": './wwwroot/js/modules/orders/sourcing/components/remove-offer-auto-sourcing.js',
        "orders-purchase-requisition-quick-browse": './wwwroot/js/modules/orders/purchase-requisitions/details/components/purchase-requisition-quick-browse.js',
        "orders-sourcing-add-to-requirement": './wwwroot/js/modules/orders/sourcing/containers/add-to-requirement.js',
        "orders-sourcing-edit-alternative-parts": './wwwroot/js/modules/orders/sourcing/components/edit-alternative-parts.js',
        "orders-sourcing-offer-add-edit-clone": './wwwroot/js/modules/orders/sourcing/components/offer-add-edit-clone.js',
        "orders-sourcing-reverse-logistic-edit": './wwwroot/js/modules/orders/sourcing/components/reverse-logistic-edit.js',
        "orders-sourcing-add-edit-sourcing-info": './wwwroot/js/modules/orders/sourcing/components/add-edit-sourcing-info.js',
        "orders-sourcing-bulk-edit": './wwwroot/js/modules/orders/sourcing/containers/bulk-edit.js',
        "orders-sourcing-bulk-edit-log": './wwwroot/js/modules/orders/sourcing/containers/bulk-edit-log.js',
        "orders-sourcing-request-for-quote": './wwwroot/js/modules/orders/sourcing/components/request-for-quote.js',
        "orders-sourcing-section-box": './wwwroot/js/modules/orders/sourcing/components/sourcing-section-box.js',
        "orders-sourcing-edit-strategic-offer": './wwwroot/js/modules/orders/sourcing/components/edit-strategic-offer.js',
        "orders-sourcing-trusted-add-edit-clone": './wwwroot/js/modules/orders/sourcing/components/trusted-add-edit-clone.js',
        "orders-sales-orders-details": './wwwroot/js/modules/orders/sales-orders/details/sales-orders-details.js',
        "orders-quotes": './wwwroot/js/modules/orders/quotes/quotes.js',
        "orders-quotes-sales-calculator": "./wwwroot/js/modules/orders/quotes/containers/sales-calculator.js",
        "orders-quote-detail": './wwwroot/js/modules/orders/quotes/containers/quote-detail.js',
        "orders-quote-details-add-new-line": "./wwwroot/js/modules/orders/quotes/containers/add-line.js",
        "orders-purchase-quote": './wwwroot/js/modules/orders/purchase-quote/purchase-quote.js',
        "orders-purchase-quote-detail": './wwwroot/js/modules/orders/purchase-quote/details/purchase-quote-detail.js',
        "orders-add-new-quote": './wwwroot/js/modules/orders/add-new-quote/add-new-quote.js',
        "orders-edit-quote": "./wwwroot/js/modules/orders/quotes/containers/edit-quote.js",
        "orders-add-new-quote-step-new-quote-select-item": "./wwwroot/js/modules/orders/add-new-quote/containers/step-new-quote-select-item.js",
        "orders-add-new-quote-step-from-requirement-select-item": "./wwwroot/js/modules/orders/add-new-quote/containers/step-from-requirement-select-item.js",
        "orders-add-new-quote-step-notify-email": "./wwwroot/js/modules/orders/add-new-quote/containers/step-notify-email.js",
       
        "purchase-order-epr": './wwwroot/js/modules/orders/purchase-orders/epr/purchase-orders-epr.js',
        "orders-purchase-order-details": './wwwroot/js/modules/orders/purchase-orders/details/purchase-order-details.js', 
        "setup-company-settings-edit-application-settings": './wwwroot/js/modules/setup/company-settings/application-settings/components/edit-application-settings.js',
        "setup-company-settings-application-settings": './wwwroot/js/modules/setup/company-settings/application-settings/application-settings.js',
        "setup-company-settings-taxes": './wwwroot/js/modules/setup/company-settings/taxes/taxes.js',
        "setup-company-settings-add-restricted-manufacturers": './wwwroot/js/modules/setup/company-settings/restricted-manufacturer/components/add-restricted-manufacturers.js',

        "setup-company-settings-add-warning-message": './wwwroot/js/modules/setup/company-settings/warnings/components/add-warning-message.js',
        "setup-company-settings-edit-warning-message": './wwwroot/js/modules/setup/company-settings/warnings/components/edit-warning-message.js',

        "user-to-do-list": './wwwroot/js/modules/user/to-do-list/to-do-list.js',
        "orders-purchase-order": './wwwroot/js/modules/orders/purchase-orders/purchase-order.js',
        'orders-purchase-order-quick-browse': './wwwroot/js/modules/orders/purchase-orders/details/components/purchase-order-quick-browse.js',
        'orders-sales-order-quick-browse': './wwwroot/js/modules/orders/sales-orders/details/components/sales-order-quick-browse.js',

    },
    output: {
        filename: 'js/[name].bundle.js',
        path: path.resolve(__dirname, 'wwwroot/dist'),
        publicPath: `${cdnUrl}/dist/`,
        clean: true
    },
    module: {
        rules: [
            {
                test: /\.js$/,
                exclude: /node_modules/,
                use: {
                    loader: 'babel-loader',
					options: { presets: ['@babel/preset-env'] }
                }
            }
        ]
    },
    mode: 'production'
};