import { QuickBrowserComponent } from '../components/quick-browser/quick-browser.component.js?v=#{BuildVersion}#';
import { QuickBrowserEvents } from '../components/quick-browser/constants/quick-browser.constants.js?v=#{BuildVersion}#';
import { FieldType } from '../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { TextFilterHelper } from "../helper/text-filter-helper.js?v=#{BuildVersion}#";

export const CompanyListTypeConstant = Object.freeze({
    AllCompanies: 0,
    Customers: 1,
    Suppliers: 2,
    Prospects: 3,
});

$(async () => {
    const quickBrowseManager = new CompanyQuickBrowseManager();
    $("#left-nugget-CompanyQuickBrowse").on("accordionbeforeactivate", async (event, ui) => {
        if (quickBrowseManager.quickBrowseFilter == null) {
            quickBrowseManager.setLoadingQuickBrowse(true);
            await quickBrowseManager.initialize();
            quickBrowseManager.setLoadingQuickBrowse(false);
        }
    });
    
});

class CompanyQuickBrowseManager {
    constructor() {
        this.isSalespersonFirstLoad = true;
        this.filterStates = JSON.parse($('#company-quick-browser-component').attr("data-states"));
        this.quickBrowseConfig = JSON.parse($('#company-quick-browser-component').attr("data-config"));
        this.companyListType = Number(getParameterByName('clt')) || CompanyListTypeConstant.AllCompanies;
        this.includeClientAllOption = this.companyListType == CompanyListTypeConstant.AllCompanies || this.companyListType == CompanyListTypeConstant.Customers;
        this.filterInputs = [
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseViewLevel}`,
                name: 'ViewLevel',
                id: 'ViewLevel',
                value: '',
                options: {
                    serverside: false,
                    endpoint: `lists/view-levels/${this.quickBrowseConfig.enmPageId}`,
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                    placeholder: null
                },
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseName}`,
                name: 'Name',
                id: 'Name',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseType}`,
                name: 'Type',
                id: 'Type',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseCity}`,
                name: 'City',
                id: 'City',
                value: '',
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseCountry}`,
                name: 'CountryNo',
                id: 'CountryNo',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'countries/dropdown-countries',
                    valueKey: 'countryId',
                    textKey: 'countryName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                }
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseTel}`,
                name: 'TelNo',
                id: 'TelNo',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseState}`,
                name: 'State',
                id: 'State',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseCounty}`,
                name: 'County',
                id: 'County',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseManufacturerSupplied}`,
                name: 'MFR',
                id: 'MFR',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseGroupCodeName}`,
                name: 'GroupCodeName',
                id: 'GroupCodeName',
                value: '',
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseCompanyType}`,
                name: 'CompanyTypeId',
                id: 'CompanyTypeId',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/company-sub-type',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseVATID}`,
                name: 'VATIDs',
                id: 'VATIDs',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseCertificateNo}`,
                name: 'InsuranceCertificateNo',
                id: 'InsuranceCertificateNo',
                value: '',
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseCertificateCategory}`,
                name: 'CertificateCategoryNo',
                id: 'CertificateCategoryNo',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/certificate-Category',
                    valueKey: 'certificateCategoryId',
                    textKey: 'certificateCategoryName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseSalesperson}`,
                name: 'Salesman',
                id: 'Salesman',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'setup/security-settings/security-users/users-client',
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    isCacheAppliedOnInit: true,
                },
            },
            {
                fieldType: FieldType.STAR_RATING,
                label: `${window.localizedStrings.quickBrowseSupplierRating}`,
                name: 'SupplierRating',
                id: 'SupplierRating',
                value: '',
            },
            {
                fieldType: FieldType.STAR_RATING,
                label: `${window.localizedStrings.quickBrowseCustomerRating}`,
                name: 'CustomerRating',
                id: 'CustomerRating',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseCustomerNo}`,
                name: 'CustomerNo',
                id: 'CustomerNo',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseZipCode}`,
                name: 'Zip',
                id: 'Zip',
                value: '',
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseClientName}`,
                name: 'ClientName',
                id: 'ClientName',
                value: '',
                options: {
                    serverside: false,
                    endpoint: (!this.quickBrowseConfig.isGlobalLogin && !this.quickBrowseConfig.isGSA) ? "" : '/user-account/clients/active',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    isCacheAppliedOnInit: true,
                    params: { includeAllOption: this.includeClientAllOption && this.quickBrowseConfig.isGlobalLogin, isIncludeClient: false },
                    onSelected: (data, selectedClient) => {
                        if (!data || !this.quickBrowseConfig.isGlobalLogin || this.isSalespersonFirstLoad) {
                            this.isSalespersonFirstLoad = false;
                            return;
                        }
                        let endpoint = 'setup/security-settings/security-users/users-client';
                        const selectedClientVal = selectedClient ? parseInt(selectedClient) : 0;
                        if (selectedClientVal == -1) {
                            endpoint = 'setup/security-settings/security-users/all-active-users';
                        } else if (selectedClientVal > 0) {
                            endpoint = `setup/security-settings/security-users/users-client?clientNo=${selectedClientVal}`;
                        }
                        $("#company-quick-browser-section #Salesman").dropdown("reset");
                        $("#company-quick-browser-section #Salesman").dropdown("reload", endpoint);
                    },
                    callbacks: {
                        invisibleCallback: () => {
                            this.resetSalesmanState(true);
                        },
                        visibleCallback: () => {
                            this.resetSalesmanState(false);
                        }
                    }
                }
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseRegion}`,
                name: 'Region',
                id: 'Region',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/regions',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseEmail}`,
                name: 'Email',
                id: 'Email',
                value: '',
            },
            {
                fieldType: FieldType.TEXT,
                label: `${window.localizedStrings.quickBrowseIndustryType}`,
                name: 'IndustryType',
                id: 'IndustryType',
                value: '',
            },
            {
                fieldType: FieldType.SELECT,
                label: `${window.localizedStrings.quickBrowseCompanyStatus}`,
                name: 'CompanyStatus',
                id: 'CompanyStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/company-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
            },
        ];

        this.pageSize = $('#company-quickBrowseTbl').data('default-page-size');
        this.defaultPageIndex = $('#company-quickBrowseTbl').data('default-page-index');
        this.quickBrowseFilter = null;
        this.quickBrowseTable = null;
        this.sortIndex = 1;
        this.sortDirection = 1;
    }

    async initialize() {
        this.initQuickBrowser();
    }

    async initQuickBrowser() {
        this.getFilterInputs();
        this.quickBrowseFilter = new QuickBrowserComponent('#company-quick-browser-component', {
            inputConfigs: this.filterInputs
        });
        this.quickBrowseFilter.init();
        this.quickBrowseFilter.setLockOrUnlockFilter(this.quickBrowseConfig.initSaveDataListState);

        Object.values(this.filterStates).forEach(input => {
            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.toggleFilterInput(true, input.name);
                if (currentInput) {
                    switch (input.fieldType) {
                        case FieldType.TEXT:
                            currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                            break;
                        case FieldType.STAR_RATING:
                            currentInput.setState(input);
                            break;

                        default:
                            currentInput.setValue(input.value);
                            break;
                    }
                }
            }
        });

        this.quickBrowseFilter.on(QuickBrowserEvents.SEARCH, (e) => {
            if (this.quickBrowseTable) {
                GlobalTrader.Helper.reloadPagingDatatableServerSide(this.quickBrowseTable, true);
            }
            else {
                $('#company-quickBrowseTbl').removeClass("d-none");
                this.initDataTable();
            }
            
        });
    }

    initDataTable() {
        let tableColumnsDefine = [
            {
                name: 'companyId',
                data: 'companyId',
                title: 'companyId',
                visible: false
            },
            {
                data: 'companyName',
                name: 'companyName',
                title: `${window.localizedStrings.quickBrowseCompanyName}`,
                className: 'text-wrap text-break',
                render: (data, type, row) => {
                    const companyNameText = GlobalTrader.StringHelper.setCleanTextValue(data);
                    const escapedCompanyName = DataTable.render.text().display(companyNameText);

                    return `
                                <div class="m-0" style="min-height: 15px;">
                                    <a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyId, null, this.companyListType)}">
                                        ${escapedCompanyName} 
                                    </a> 
                                </div>
                            `

                }
            }
        ]

        this.quickBrowseTable = $('#company-quickBrowseTbl')
            .DataTable({
                serverSide: true,
                ajax: {
                    url: '/api/contact/all-companies/list',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortDirection;
                        const filtersData = this.quickBrowseFilter.getDisplayedFilterValues().filter((input) => input.name != 'ViewLevel');
                        const viewLevelInput = this.quickBrowseFilter.getInputElementByName('ViewLevel');
                        return JSON.stringify({
                            draw: data.draw,
                            start: data.start,
                            length: data.length,
                            sortDir: sortDir,
                            viewLevel: viewLevelInput.getValue().value,
                            orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                            filters: filtersData,
                            saveStates: this.quickBrowseFilter.isLockFilter,
                            companyListType: this.companyListType
                        });
                    },
                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                order: [[this.sortIndex, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                columnDefs: [
                    
                    { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],
                bLengthChange: false,
                dom: "<'dt-layout-start text-center'r>t<'dt-layout-cell text-center'ip>",
                language: {
                    emptyTable: `<i>${window.localizedStrings.noMatches}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noMatches}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                    info: "Page _PAGE_ of _PAGES_",
                    infoEmpty: "",
                    loadingRecords: ""
                },
                processing: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columns: tableColumnsDefine,
                rowId: 'companyId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
            })
            .on('preXhr.dt', () => {
            })
            .on('draw.dt', () => {
                this.quickBrowseTable.columns.adjust();

                // Remove neutral sorting icon
                const tableId = this.quickBrowseTable.table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');
            });
    }

    toggleFilterInput(isShow, fieldName) {
        const currentInput = this.quickBrowseFilter.getInputElementByName(fieldName);
        if (currentInput) {
            currentInput.invisible = !isShow;
            isShow ? currentInput.show() : currentInput.hide();

            const currentOption = this.quickBrowseFilter.getOptionByName(fieldName);
            isShow ? currentOption.addClass('selected') : currentOption.removeClass('selected');
            currentOption.find("img").attr(
                "src",
                `/img/icons/${isShow ? 'check-green.svg' : 'xmark-red.svg'}`
            );
        }
        return currentInput;
    }

    getFilterInputs() {
        if (!this.quickBrowseConfig.canViewAllCompanyStatus) {
            this.filterInputs = this.filterInputs.filter((input) => input.name != 'CompanyStatus');
        }
    }

    setLoadingQuickBrowse(isLoading) {
        if (isLoading) {
            $('#company-quick-browser-section').prepend(`<div id="loading-company-quick-browse-content" class="text-loader m-auto"></div>`);
        } else {
            $(`#loading-company-quick-browse-content`).remove();
            $(`#company-quick-browser-component`).removeClass("d-none");
        
        }
    }

    resetSalesmanState(isHide) {
        const clientFilterInput = this.quickBrowseFilter.getInputElementByName('ClientName');
        if (clientFilterInput) {
            const selectedClientId = parseInt(clientFilterInput.getValue().value);
            if (selectedClientId != this.quickBrowseConfig.currentClientId) {
                let endpoint = `setup/security-settings/security-users/users-client`;
                if (!isHide) {
                    if (selectedClientId == -1) {
                        endpoint = 'setup/security-settings/security-users/all-active-users';
                    } else if (selectedClientId > 0) {
                        endpoint = `setup/security-settings/security-users/users-client?clientNo=${selectedClientId}`;
                    }
                }
                $("#company-quick-browser-section #Salesman").dropdown("reset");
                $("#company-quick-browser-section #Salesman").dropdown("reload", endpoint);
            }
        }
    }
} 