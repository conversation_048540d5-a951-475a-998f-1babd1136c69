import { InputElement } from './input-element.model.js?v=#{BuildVersion}#';
import { FieldType } from '../constants/field-type.constant.js?v=#{BuildVersion}#';
import '../../../widgets/drop-down.js?v=#{BuildVersion}#';

export class SelectInput extends InputElement {
    constructor(container, label, name, id, invisible = false, options) {

        // Call the parent constructor (super) before using "this"
        super((id) => {
            return $('<select>', {
                class: "form-select input-disabled",
                name: name,
                id: id,
                disabled: false
            })
        }, name, label, id, invisible);
        this.type = FieldType.SELECT;

        // Create the UI wrapper after calling super()
        this.wrapper.find(`#${this.id}ElementContent`).append(this.element);

        // Append the entire wrapper to the container
        container.append(this.wrapper);
        $(`#${this.id}`).dropdown(options).parent().addClass('w-100');
    }

    getValue() {
        return {
            value: $(this.element).dropdown("selectedValue")
        }
    }

    getInputValue() {
        return $(this.element).dropdown("selectedValue");
    }

    /**
     * @override
     */
    setValue(valueObj) {
        $(this.element).dropdown("select", valueObj);
    }

    /**
     * @override
     */
    registerEvents() {
        this.element.on('input', (e) => {
            this.trigger('change.mfi', e);
        })

        this.element.on('focus', (e) => {
            this.trigger('focus.mfi', e);
        })

        this.element.on('blur', (e) => {
            this.trigger('blur.mfi', e);
        })
    }
}