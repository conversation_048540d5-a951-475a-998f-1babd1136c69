import { EventEmitter } from "../../components/base/event-emmiter.js?v=#{BuildVersion}#";
import { MailMessageSearchComponent } from '../../modules/orders/requirements/components/search-selects/mail-message-search.js';

export class NotifyEmailDialogContainer extends EventEmitter {
    constructor({ 
        dialogId, 
        formId, 
        companyId, 
        apiEndpoints = {},
        defaultSubject = '',
        defaultTemplate = '',
        dialogOptions = {},
        validationRules = {},
        callbacks = {}
    }) {
        super();
        this.companyId = companyId;
        this.dialogId = dialogId;
        this.$dialog = $(`#${dialogId}`);
        this.$form = $(`#${formId}`);
        this.defaultSubject = defaultSubject;
        this.defaultTemplate = defaultTemplate;
        this.callbacks = callbacks;

        this.$subjectInput = this.$form.find('input[name="Subject"]');
        this.$messageInputSummernote = this.$form.find('#message-input');

        this.$sendButton = null;
        this.$cancelButton = null;

        this.mailAutoSearch = null;

        const defaultApiEndpoints = {
            autoSearchUser: '/user-account/mail-messages/auto-search',
            sendNewMessage: '/user-account/mail-messages/send-new-message',
            templateEmail: '',
        };

        this.apiEndpoints = {
            ...defaultApiEndpoints,
            ...apiEndpoints
        };

        this.dialogOptions = {
            width: "auto",
            height: "auto",
            maxHeight: 700,
            minWidth: '35vw',
            autoOpen: false,
            draggable: false,
            modal: true,
            sendButtonText: window.localizedStrings?.send,
            cancelButtonText: window.localizedStrings?.cancel,
            sendButtonIcon: '/img/icons/send.svg',
            cancelButtonIcon: '/img/icons/slash.svg',
            ...dialogOptions
        };

        this.validationRules = {
            To: {
                notEmptyArray: true,
                required: true,
                noWhiteSpace: true,
            },
            Subject: {
                required: true,
                noWhiteSpace: true,
                maxlength: 256
            },
            Message: {
                required: true,
                summernoteRequired: true,
                noWhiteSpace: true,
                noWhiteSpaceSummernote: true,
                maxlength: 60000
            },
            ...validationRules
        };
    }

    initialize() {
        this.setupDialog();
        this.setupInputForm();

        this.$dialog.find('input, select, textarea')
            .filter(':visible:enabled:not([readonly])')
            .first()
            .trigger("focus");
    }

    setupDialog(){
        this.dialog = this.$dialog.dialog({
            width: this.dialogOptions.width,
            height: this.dialogOptions.height,
            maxHeight: this.dialogOptions.maxHeight,
            minWidth: this.dialogOptions.minWidth,
            autoOpen: this.dialogOptions.autoOpen,
            draggable: this.dialogOptions.draggable,
            modal: this.dialogOptions.modal,
            open: async () => {
                await this.handleOpen();
            },
            buttons: [
                {
                    text: this.dialogOptions.sendButtonText,
                    class: 'btn btn-primary',
                    html: `<img src="${this.dialogOptions.sendButtonIcon}" alt="${this.dialogOptions.sendButtonText}">${this.dialogOptions.sendButtonText}`,
                    click: async () => {
                        await this.handleSubmitSendEmailForm();
                    }
                },
                {
                    text: this.dialogOptions.cancelButtonText,
                    id: `${this.dialogId}-cancel-btn`,
                    class: 'btn btn-danger',
                    html: `<img src="${this.dialogOptions.cancelButtonIcon}" alt="Cancel icon"/>${this.dialogOptions.cancelButtonText}`,
                    click: () => {
                        this.handleCancel();
                    }
                }
            ],
            close: () => {
                this.handleClose();
            }
        });
        this.$sendButton = this.$dialog.find(`#${this.dialogId}-send-email-btn`);
        this.$cancelButton = this.$dialog.find(`#${this.dialogId}-cancel-btn`);
    }

    setupInputForm() {
        
        if (!this.mailAutoSearch) {
            this.mailAutoSearch = new MailMessageSearchComponent(
                'to-input-search',
                'to-input',
                'multiple',
                'nameSearch',
                this.apiEndpoints.autoSearchUser,
                1
            );
        }
        
        this.$messageInputSummernote.summernote2({
            maxLength: this.validationRules.Message?.maxlength || 60000,
            characterCounter: true,
        });                
        this.setupValidation();
    }

    async bindDataToForm() {
        const dataTemplate = await this.getDefaultTemplateEmail();
        this.$messageInputSummernote.summernote2('code', dataTemplate);
    }

    async handleSubmitSendEmailForm() {
        if (!this.$form.valid()) {
            this.$dialog.find(".form-error-summary").show();
            return;
        }

        this.$sendButton?.prop("disabled", true);
        this.showLoading();
        this.$form.find('.is-invalid').removeClass("is-invalid");
        this.$dialog.find(".form-error-summary").hide();
        
        const header = {
            "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val()
        };
        
        const data = {
            to: this.mailAutoSearch.searchInputValue,
            message: this.$messageInputSummernote.summernote2('code'),
            subject: this.$subjectInput.val(),
            companyId: this.companyId
        };
        
        this.$dialog.dialog('close');
        
        try {
            const response = await GlobalTrader.ApiClient.postAsync(this.apiEndpoints.sendNewMessage, data, header);
            if(response?.success) {
                await this.handleSuccessResponse(response);
            }else{
                await this.handleFailureResponse(response);
            }
        } catch (error) {
            await this.handleFailureResponse(error);
        }
    }

    async handleSuccessResponse(response){
        if (this.callbacks.onSuccess) {
            await this.callbacks.onSuccess(response);
        }
        showToast('success', window.localizedStrings.saveChangedMessage);
        this.hideLoading();
        this.$sendButton?.prop("disabled", false);
    }

    async handleFailureResponse(response){
        if (this.callbacks.onError) {
            await this.callbacks.onError(response);
        }
        this.hideLoading();
        this.$sendButton?.prop("disabled", false);
    }
  

    async getDefaultTemplateEmail() {
        if (this.defaultTemplate) {
            return this.defaultTemplate;
        }

        if (!this.apiEndpoints.templateEmail || this.apiEndpoints.templateEmail.length === 0) {
            return "";
        }
        
        try {
            const templateResult = await GlobalTrader.ApiClient.getAsync(this.apiEndpoints.templateEmail);
            return templateResult?.data || "";
        } catch (error) {
            return "";
        }
    }

    setupValidation() {
        this.$form.validate({
            ignore: [],
            rules: this.validationRules,
            invalidHandler: (event, validator) => {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
            errorPlacement: (error, element) => {
                const inputName = element.attr("name");
                if (inputName === 'Message') {
                    error.appendTo(element.parent());
                } else {
                    error.insertAfter(element);
                }
            },
            highlight: function (element) {
                const inputName = $(element).attr("name");
                if (inputName === 'Message') {
                    $('.note-editor').addClass('is-invalid');
                }else{
                    $(element).addClass("is-invalid");
                }
            },
            unhighlight: function (element) {
                const inputName = $(element).attr("name");
                if (inputName === 'Message') {
                    $('.note-editor').removeClass('is-invalid');
                } else {
                    $(element).removeClass("is-invalid");
                }
            },
        });
    }

    async handleOpen(){
        if (this.callbacks.onOpen) {
            this.callbacks.onOpen();
        } else {
            await this.bindDataToForm();
        }
    }

    handleCancel() {
        if (this.callbacks.onCancel) {
            this.callbacks.onCancel();
        }
        this.$dialog.dialog('close');
        this.resetInput();
    }

    handleClose() {
        if (this.mailAutoSearch) {
            this.mailAutoSearch.resetSearchSelect();
            this.$dialog.find('.is-invalid').removeClass("is-invalid");
        }
        if (this.callbacks.onClose) {
            this.callbacks.onClose();
            this.resetInput();
        }
    }

    openDialog() {
        this.$dialog.dialog("open");
    }

    showLoading() {
        this.$dialog.dialog("setLoading", true);
    }

    hideLoading() {
        this.$dialog.dialog("setLoading", false);
    }

    resetInput() {
        this.$subjectInput.val("");
        this.$messageInputSummernote.val("");
    }
}