.stock-alert .dropdown-button {
    color: #ff6a00;
    background-color: #d2ffd2;
    padding: 1px;
}

.stock-alert .alert-label {
    color: #ff6a00; 
    background-color: #f1f1f1; 
    padding: 1px;
    width: fit-content;
    display: inline-block;
    margin-left: 0;
}

.stock-alert .dropdown-content {
    display: none;
    position: absolute;
    background-color: #f1f1f1;
    min-width: 160px;
    box-shadow: 0px 8px 16px 0px rgb(0 0 0 / 20%);
    z-index: 5;
    padding-left: 10px;
}

.stock-alert .dropdown-area {
    cursor: default;
}

.stock-alert .dropdown-area:hover .dropdown-content {
    display: block;
}

.stock-alert .dropdown-content:hover {
    display: block;
}

.stock-alert .notes {
    font-weight: bold;
    padding: 10px 10px 0px;
}

.stock-alert .item {
    color: #0000ff;
    padding: 10px;
}

.stock-alert .item:hover {
    background-color: #ddd;
}

.product .hazardous {
    display: inline;
    background-repeat: no-repeat;
    background-position: right bottom;
    background-size: contain;
    padding-right: 28px;
    font-size: 12px;
    line-height: normal;
    background-color: initial;
    color: inherit;
    white-space: inherit;
    background-image: url("/img/hazardous/Hazardousone.png");
}

.product .hazardous-ipo {
    display: inline;
    background-repeat: no-repeat;
    background-position: right bottom;
    background-size: contain;
    padding-right: 28px;
    font-size: 12px;
    background-color: initial;
    color: inherit;
    white-space: inherit;
    background-image: url("/img/hazardous/IPOprodone.png");
}

.product .hazardous-rh {
    display: inline;
    background-repeat: no-repeat;
    background-position: right bottom;
    background-size: contain;
    padding-right: 28px;
    font-size: 12px;
    background-color: initial;
    color: inherit;
    white-space: inherit;
    background-image: url("/img/hazardous/Restrictedprodone.png");
}

.product .ihs-part-status-doc {
    display: inline;
    background-repeat: no-repeat;
    background-position: right bottom;
    background-size: contain;
    padding-right: 28px;
    font-size: 12px;
    background-color: initial;
    color: inherit;
    white-space: inherit;
    background-image: url("/img/hazardous/ihspartstatuspng.png");
}

.nugget-message {
    background-image: url(/App_Themes/Original/images/Nuggets/standard/msg_bg.gif);
    background-repeat: repeat-x;
    background-position: bottom;
    background-color: #fffdea;
    border: solid 2px #f3e575;
    padding: 0.75rem 0.75rem;
    margin-bottom: 0.5rem;
}

.nugget-message .nugget-message-warning {
    background-image: url(/App_Themes/Original/images/Nuggets/standard/msg_warn.png);
    font-size: 12px;
    padding-left: 20px;
    background-repeat: no-repeat;
    background-position: left center;
}