﻿GlobalTrader.StringHelper = (function () {
    return {
        setCleanTextValue: function (strIn, isReplaceLineBreaks) {
            if (typeof (strIn) == "undefined" || strIn == null) strIn = "";
            strIn = (strIn + "").trim();
            strIn = strIn.replace(/(:PLUS:)/g, "+");
            strIn = strIn.replace(/(:QUOTE:)/g, '"');
            strIn = strIn.replace(/((:AND:)|(&amp;))/g, "&");
            if (isReplaceLineBreaks) strIn = strIn.replace(/(\n)/g, "<br />");
            return strIn;
        },
        stringFormat(str, ...args) {
            return str.replace(/{(\d+)}/g, (match, index) => args[index] || '');
        },
        escapeSpecialCharacters(value) {
            return value.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&');
        },
        setTextBreakLineValue: function (strIn) {
            if (typeof (strIn) == "undefined") strIn = "";
            strIn = strIn.replace(/<br\s*\/?>/gi, "\n");
            return strIn;
        },
        setSingleLineBreakValue: function (strIn) {
            if (typeof strIn === "undefined") strIn = "";
            strIn = strIn.replace(/(<br\s*\/?>\s*)+/gi, "<br />");
            strIn = strIn.replace(/<br\s*\/?>/gi, "\n");
            return strIn.trim();
        },
        replaceBRTags: function (strIn) {
            if (!strIn) strIn = "";
            strIn = strIn.replace(/((<br \/>)|(<br>)|(<br\/>))/gi, "\r\n");
            return strIn;
        },
        isNullOrWhitespace(str) {
            return !str || str.trim().length === 0;
        },
        formatURL: function (strURL) {
            if (!strURL) return "";
            if (strURL.trim().length == 0) return "";
            strURL = strURL.trim();
            if (!strURL.startsWith('http')) return `http://${strURL}`;
            return strURL;
        },
        formatAdvisoryNotes: function (notes) {
            let formattedNotes = this.setCleanTextValue(notes);
            formattedNotes = this.escapeStringHtml(formattedNotes);
            return formattedNotes;
        },
        stripScriptTags(str) {
            return str.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        },
        appendValuesToTemplateString(template, ...values) {
            //Example ("Test, {0} {1} {2}", "This is", "a", "text") -> "Test, This is a text"
            return template.replace(/\{(\d+)\}/g, (_, index) => values[index] ?? '');;
        },
        truncateString(str, maxLength) {
            if (str.length > maxLength) {
                return str.slice(0, maxLength).trim() + '...';
            }
            return str;
        },
        escapeStringHtml(str) {
            return String(str)
                .replace(/"/g, "&quot;")
                .replace(/(<br \/>)/g, "&#10;");
        },
        getPartNoAndRohsStatus(strPart, status) {
            strPart = this.setCleanTextValue(strPart);
            const rohsEntry = ROHS_STATUSES[status];

            if (!rohsEntry) {
                return strPart;
            }

            const $rohsElement = $("<div>").addClass(`rohs ${rohsEntry.className}`).attr("title", rohsEntry.tooltip);

            return $rohsElement.text(strPart).prop("outerHTML");
        },
        getTrimDefaultString(str, defaultString) {
            defaultString = defaultString || "N/A";
            return str?.trim() || defaultString;
        },
        convertDMYStringToMidnightISOString(dateStr) {
            //dateStr format dd/mm/yyyy
            if (!dateStr || typeof dateStr !== 'string') return null;

            const parts = dateStr.split('/');
            if (parts.length !== 3) return null;
            const [day, month, year] = parts.map(Number);
            const yyyy = year.toString().padStart(4, '0');
            const MM = month.toString().padStart(2, '0');
            const dd = day.toString().padStart(2, '0');
            return `${yyyy}-${MM}-${dd}T00:00:00`;
        },
        htmlAttributeEncode(str) {
            if (typeof str === "undefined" || str === null) return "";
            return (str + "").replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#39;');
        },
        arrayToSingleString(ary, strSep) {
            if (!ary) return "";
            if (!strSep) strSep = "||";
            return ary.join(strSep);
        },

        showSerialNumber(poString, lineNo) {
            let strOut = poString || "";
            if (parseInt(lineNo) > 0)
                strOut += ` (${lineNo})`;
            return strOut;
        }
    };
})();