﻿import { TableFilterComponent } from '../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#'
import { InputElement } from '../../../../components/table-filter/models/input-element.model.js?v=#{BuildVersion}#'
InputElement.prototype.createWrapper = function (id, label, requiredCheckbox) {
    return `
            <div class="col-12 form-control-wrapper">
                <div class="row g-3 align-items-center">
                    <div class="col-3">
                        <label for="${id}" class="form-label">${label}</label>
                    </div>
                    <div class="col-8">
                        <div class="d-flex gap-1" id="${id}ElementContent">
                            ${requiredCheckbox ? '<span></span> <!-- Placeholder for checkbox -->' : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;
}
export class PurchaseOrderTableFilterComponent extends TableFilterComponent {

    constructor(container, title, options) {
        super(container, title, options);

    }
    getContainer(inputConfig, isTemplate) {
        return this.container.find(`.filter-form .${inputConfig.locatedInContainerByClass}`);
    }
    generateLayout() {
        return `
            <div class="filter-section-wrapper mb-10px d-none ${this.settings.wrapperClass ?? ''}" id="${this.container.attr('id')}-content-show">
                <!-- Filter result title -->
                <div class="d-flex justify-content-between">
                    <h5 class="d-flex m-0 align-items-center filter-title"></h5>
                </div>
                <div class="line my-2"></div>

                <!-- Filter result form -->
                <div class="row common-form filter-form">
                     <div class="col-6 px-0 filter-column-1 ">
                    </div>
                    <div class="col-6 px-0 filter-column-2">
                    </div>
                </div>
                ${this.settings.showButtons ? `
                <!-- Filter result buttons -->

                <div class="line my-2"></div>

                <span class="d-flex gap-2 align-items-center">
                    <button class="btn btn-danger off-table-filter">
                        <img src="/img/icons/slash.svg" alt="off" width="18" height="18">
                        <span class="lh-base">Off</span>
                    </button>
                     <button class="btn btn-primary hide-table-filter" >
                        <span class="lh-base">Hide Filter</span>
                    </button>
                    <button class="btn btn-primary reset-table-filter" >
                        <img src="/img/icons/reset.svg" alt="reset" width="18" height="18">
                        <span class="lh-base">Reset</span>
                    </button>
                    <button class="btn btn-primary apply-table-filter">
                        <img src="/img/icons/check.svg" alt="apply" width="18" height="18">
                        <span class="lh-base">Apply</span>
                    </button>
                    <button class="btn btn-danger cancel-table-filter" style="display: none;">
                        <img src="/img/icons/slash.svg" alt="cancel" width="18" height="18">
                        <span class="lh-base">Cancel</span>
                    </button>
                </span>` : ``}
            </div>
            <div class="filter-section-wrapper mb-10px" style="display: none;" id="${this.container.attr('id')}-content-hide">
                <div class="d-flex gap-2 align-items-center">
                    <button class="btn btn-outline-primary show-table-filter">
                        <span class="lh-base">Show Filter</span>
                    </button>
                    <p class="m-0" name="count-filter-applied" id="${this.container.attr('id')}-count-filter-applied">
                    </p>
                <div>
            </div>
        `
    }
}