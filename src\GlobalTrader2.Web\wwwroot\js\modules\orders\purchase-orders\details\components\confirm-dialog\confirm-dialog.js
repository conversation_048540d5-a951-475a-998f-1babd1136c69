import { EventMediator } from '../../../../../../components/base/event-mediator.js?v=#{BuildVersion}#';

export class ConfirmDialogBase {
    constructor(id) {
        this._id = id || crypto.randomUUID();
        if ($(`#${this._id}`).length === 0) {
            $("body").append(`
                <div id="${this._id}" class="dialog-container" style="display: none;">
                    <div class="dialog-description">
                        <span></span>
                    </div>
                </div>
            `);
        }

        this._title = "";
        this._description = "";
        this._content = [];

        this._handleSubmitAsync = async () => {
            showToast("danger", "Submit action has not been override yet!");
            return true;
        };

        this._$dialog = $(`#${this._id}`);
        this.openDialog = () => this._$dialog.dialog("open");
        this.EventMediator = EventMediator.getInstance();

        this._dialogConfig = {
            width: "auto",
            height: "auto",
            autoOpen: false,
            modal: true,
            title: "Confirmation",
            buttons: {
                Yes: {
                    id: `${this._id}-yes-btn`,
                    class: 'btn btn-primary',
                    html: `<img src="/img/icons/check.svg" alt=""/>${window.localizedStrings.yes}`,
                    click: async () => {
                        let success = await this._handleSubmitAsync();
                        if (success) {
                            this._$dialog.dialog("close");
                        }
                    }
                },
                No: {
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/xmark.svg" alt=""/>${window.localizedStrings.no}`,
                    click: () => {
                        this._$dialog.dialog("close");
                    }
                },
            },
            open: () => {
                $('.ui-dialog-titlebar-close').remove();
                this._$dialog.dialog("option", "title", this._title);

                if (this._description) {
                    this._$dialog.find(".dialog-description > span").text(this._description);
                }

                if (this._content) {
                    let $info = this._$dialog.find(".dialog-information");
                    if ($info.length === 0) {
                        this._$dialog.append('<div class="d-flex flex-column gap-3 mt-3 dialog-information"></div>');
                        $info = this._$dialog.find(".dialog-information");
                    }

                    $info.html(""); // clear previous content
                    this._content.forEach(line => {
                        $info.append(this._getContentLineHTML(line));
                    });
                }
            }
        };

        this._$dialog.dialog(this._dialogConfig);
    }

    set title(value) {
        this._title = value;
    }

    set description(value) {
        this._description = value;
    }

    get content() {
        return this._content;
    }
    set content(value) {
        this._content = value;
    }

    set handleSubmitAsync(value) {
        this._handleSubmitAsync = value;
    }

    set dialogConfig(value) {
        this._dialogConfig = value;
        this._$dialog.dialog("destroy").dialog(this._dialogConfig);  // re-initialize
    }

    _getContentLineHTML(line) {
        return `
            <div class="d-flex align-items-center">
                <div style="min-width: 120px;">
                    <div class="fw-bold mb-0">
                        ${line.label}
                    </div>
                </div>
                <div class="flex-grow-1">
                    ${line.data}
                </div>
            </div>
        `;
    }

    usualSubmitHandleAsync = async (url, body, header) => {
        this._$dialog.dialog("setLoading", true);
        const $yesBtn = $(`#${this._id}-yes-btn`);
        $yesBtn.prop("disabled", true);

        try {
            const response = await GlobalTrader.ApiClient.putAsync(url, body, header);

            if (!response.success) {
                showToast("danger", "An error occured.");
                return false;
            }

            showToast("success", window.localizedStrings.saveChangedMessage);
            this._$dialog.dialog("close");
            return true;
        } 
        finally {
            this._$dialog.dialog("setLoading", false);
            $yesBtn.prop("disabled", false);
        }
    };

}
