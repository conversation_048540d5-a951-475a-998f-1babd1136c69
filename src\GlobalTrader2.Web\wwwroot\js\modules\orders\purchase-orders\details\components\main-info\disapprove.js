import { ConfirmDialogBase } from "../confirm-dialog/confirm-dialog.js?v=#{BuildVersion}";

export class DisapprovePO extends ConfirmDialogBase {
    constructor(){
        super("disapprove-po-dialog");
        this._initialize();
        this._maininfo = null;
    }

    _initialize(){
        this.title = this.localizer.title;
        this.description = this.localizer.message;
        this.handleSubmitAsync = async () => {
            const url = `/orders/purchase-orders/details/approve-disapprove`;
            const data = {
                purchaseOrderId: this._maininfo.purchaseOrderId,
                isApprove: false
            };
            const success = await this.usualSubmitHandleAsync(url, data)
            if (success) {
                this._maininfo.approvedBy = null;
                this._maininfo.dateApproved = null;
                this.EventMediator.trigger("mainInfo-changed.poDetail", this._maininfo)
            }
            return success
        }

        this.EventMediator.on("mainInfo-changed.poDetail", this._update);
        this._$button = $("#disapprove-po-button").on('click', this.openDialog);
    }

    _update = (maininfo) => {
        const enable = maininfo?.approvedBy && !maininfo?.closed;
        this._$button?.prop('disabled', !enable);
        this._maininfo = maininfo;
        this.content = [
            {
                label: this.localizer.purchaseOrder,
                data: this._maininfo?.purchaseOrderNumber
            },
            {
                label: this.localizer.supplier,
                data: this._maininfo?.supplierName
            }
        ];
    }
    
    localizer = {
        title: disapproveLocalizer.title,
        message: disapproveLocalizer.message,
        purchaseOrder : disapproveLocalizer.purchaseOrder,
        supplier : disapproveLocalizer.supplier
    }
}
