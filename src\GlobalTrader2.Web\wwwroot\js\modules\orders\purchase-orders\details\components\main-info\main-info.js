﻿import { EventEmitter } from '../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { EventMediator } from '../../../../../../components/base/event-mediator.js?v=#{BuildVersion}#';
import { ButtonHelper } from '../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { SystemDocument } from "../../../../../../config/system-document-enum.js?v=#{BuildVersion}#";
import { DataTablesManager } from './data-tables.js?v=#{BuildVersion}#';
import { EprDropdownMenu } from '../epr-dropdown-menu.js?v=#{BuildVersion}#';
import { ClosePOManager } from './close-po.js?v=#{BuildVersion}#';
import { PurchaseOrderStatus } from '../../enums/purchase-order-status-enum.js?v=#{BuildVersion}#'
import { PurchaseOrderDetailsService } from '../../purchase-order-details-services.js?v=#{BuildVersion}#';
import { openPopup } from '../../../../../../../js/helper/url-helper.js?v=#{BuildVersion}#';
import { DisapprovePO } from './disapprove.js?v=#{BuildVersion}#';

export class PurchaseOrderMainInfoManager extends EventEmitter {
    constructor(purchaseOrderId) {
        super();
        this.purchaseOrderId = purchaseOrderId;
        this.$purchaseOrdersMainInfoWrapper = $('#purchase-orders-main-info-wrapper');
        this.$purchaseOrdersMainInfoSectionBox = $('#purchase-orders-main-information-box');
        this.poMainInfo = null;
        this.$expediteNotesTable = null;
        this.dataTablesManager = new DataTablesManager(this.purchaseOrderId);
        this.$eprDropdownMenu = null;
        this.closePOManager = new ClosePOManager();
        this.IsPoComplete = null;
        this.$closeButton = $("#close-main-info-button");

        this.unexpectedError = window.localizedStrings.unexpectedError;
        this.eventMediator = EventMediator.getInstance();
        this.disapprovePO = new DisapprovePO();
    }

    async initialize() {
        this.setupSectionBox();
        this.closePOManager.setupDialog();
        this.setupDialogEvents();
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", true);
        await this.getMainInfo();
        this.bindMainInfo();

        this.updateButtonStates()
        this.dataTablesManager.initialize();
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", false);
        const eprData = this._preProcessEprData();
        this.$eprDropdownMenu = new EprDropdownMenu(
            this.purchaseOrderId,
            "po-main-info-epr-menu",
            "view-epr-list-button",
            "po-main-info-epr-dropdown-container",
            null,
            "desc",
            eprData
        );
        this.$eprDropdownMenu.initialize();

        $('#view-tree-main-info-button').on("click", () => {
            openPopup(
                ButtonHelper.URL_All_Document(this.poMainInfo.purchaseOrderId, "PO", this.poMainInfo.purchaseOrderNumber),
                "winTreeView",
                450
            );
        });
    }

    setupSectionBox() {
        this.$purchaseOrdersMainInfoSectionBox.section_box({
            onRefreshClick: async () => {
                await this.refreshSectionBox();
            }
        });
    }

    setupDialogEvents() {
        const dialogs = [
            this.closePOManager,
        ];

        dialogs.forEach(dialog => {
            dialog.on('saveSuccess', async () => {
                await this.refreshSectionBox();
            });
        });

        this.closePOManager.on("closeSuccess", () => {
            window.location.reload();
        })
    }

    async getMainInfo() {
        const response = await PurchaseOrderDetailsService.getPurchaseOrderMainInfoAsync(this.purchaseOrderId);

        if (response.success) {
            this.poMainInfo = response.data
            this.eventMediator.trigger("mainInfo-changed.poDetail", this.poMainInfo);
            this.closePOManager.updateMainInfo(this.poMainInfo);
        } else {
            window.showToast('danger', response.error);
        }
    }

    updateButtonStates() {
        this.$closeButton.prop("disabled", this.poMainInfo.statusNo === PurchaseOrderStatus.Complete);
    }

    bindMainInfo() {
        const htmlRawFieldNames = [
            "notes",
            "instructions",
            "expediteNotes",
        ];
        let supplierButtonHtml = ButtonHelper.nubButton_Company(this.poMainInfo.supplierNo, this.poMainInfo.supplierName, this.poMainInfo.supplierAdvisoryNotes)
        if (this.poMainInfo.supplierOnSop) supplierButtonHtml += ButtonHelper.createOnStopIcon();
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-supplier-name`).html(supplierButtonHtml);
        if (this.poMainInfo.internalPurchaseOrderNo > 0) {
            $("#ipo-field").show();
            const ipoButton = ButtonHelper.createNubButton(ButtonHelper.URL_InternalPurchaseOrder(this.poMainInfo.internalPurchaseOrderNo), this.poMainInfo.internalPurchaseOrderNumber)
            $("#purchase-order-form-ipo-no").html(ipoButton)
        }

        const contactButtonHtml = ButtonHelper.nubButton_Contact(this.poMainInfo.contactNo, this.poMainInfo.contactName);
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-contact`).html(contactButtonHtml);
        this.$purchaseOrdersMainInfoSectionBox.find(`#purchase-order-form-supplier-website`)
            .attr("href", GlobalTrader.StringHelper.formatURL(this.poMainInfo.websiteAddress))
            .text(this.poMainInfo.websiteAddress)
        const as6081text = (this.poMainInfo.aS6081 ? window.localizedStrings.yes : window.localizedStrings.no)
        this.$purchaseOrdersMainInfoSectionBox.find("#aS6081-included")
            .text(as6081text)
            .css("background-color", this.poMainInfo.aS6081 ? "yellow" : "")

        const srmaIds = this.formatSRMA()
        const debitIds = this.formatDebit()

        if (srmaIds.length > 0) {
            this.$purchaseOrdersMainInfoSectionBox.find("#srma").html(srmaIds)
            this.$purchaseOrdersMainInfoSectionBox.find("#srma-field").show()
        }
        if (debitIds.length > 0) {
            this.$purchaseOrdersMainInfoSectionBox.find("#debit").html(debitIds)
            this.$purchaseOrdersMainInfoSectionBox.find("#debit-field").show()
        }

        this.$purchaseOrdersMainInfoWrapper.find("input[type=checkbox]").toArray().forEach(input => {
            const fieldName = $(input).data("field");
            $(input).prop("checked", this.getPropertyCaseInsensitive(this.poMainInfo, fieldName))
                .trigger('change');
        });

        this.$purchaseOrdersMainInfoWrapper.find("span[data-field]").toArray().forEach(element => {
            const fieldName = $(element).data("field");
            let fieldValue = this.getPropertyCaseInsensitive(this.poMainInfo, fieldName);

            if (htmlRawFieldNames.includes(fieldName)) {
                $(element).html(GlobalTrader.StringHelper.setCleanTextValue(fieldValue, true));
            } else {
                $(element).text(GlobalTrader.StringHelper.setCleanTextValue(fieldValue));

            }
        });
        this.setWarningMessages();
    }

    async setWarningMessages() {
        if (this.poMainInfo.isHighRisk) {
            $("#risk-warning-section").removeClass("d-none").show()
                .find("*").removeClass("d-none").show();
            $("#risk-text").text(this.poMainInfo.warningMessage);
            $("#ship-from-icon")
                .show()
                .attr("title", GlobalTrader.StringHelper.setCleanTextValue(this.poMainInfo.warningMessage))
                .attr("src", "/img/icons/circle-info-yellow.svg");
        }
        else {
            $("#risk-warning-section")
                .html(``)
                .hide()
        }

        if (this.poMainInfo.isHasCountryMessage) {
            $("#ship-from-icon")
                .show()
                .attr("title", GlobalTrader.StringHelper.setCleanTextValue(this.poMainInfo.countryWarningMessage))
                .attr("src", "/img/hazardous/Hazardousone.png");
        }

        let isSanctioned = (this.poMainInfo.isPOHub) ? this.poMainInfo.isHighRisk : this.poMainInfo.isSanctioned
        if (isSanctioned) {
            $("#sanction-warning-section").removeClass("d-none").show()
                .find("*").removeClass("d-none").show()
            $("#sanction-text").text(localizedStrings.sanctionMessage)

        }
        else {
            $("#sanction-warning-section")
                .text("")
                .hide()
        }

        if (this.poMainInfo.supplierMessage) {
            // ERAI
            const message = this.poMainInfo.supplierMessage.replace("&#013;", "\n") || "";
            $("#erai-icon")
                .show()
                .attr("title", GlobalTrader.StringHelper.setCleanTextValue(message))
            $("#supplier-value-field").css("background-color", "yellow");
        }
    }

    formatSRMA() {
        let srmaId = ``;
        if ((this.poMainInfo.supplierRMAIds) && (this.poMainInfo.supplierRMANumbers)) {
            for (let i = 0; i < this.poMainInfo.supplierRMANumbers.length; i++) {
                let row = this.poMainInfo.supplierRMAIds[i];
                let row1 = this.poMainInfo.supplierRMANumbers[i];
                if (i != 0) srmaId += `, `
                srmaId += ButtonHelper.nubButton_SystemDocument(SystemDocument.SupplierRMA, row.SupplierRMAId, row1.SupplierRMANumber);
            }
        }
        return srmaId
    }

    formatDebit() {
        let debitId = ``;
        if ((this.poMainInfo.debitIds) && (this.poMainInfo.debitNumbers)) {
            for (let i = 0; i < this.poMainInfo.debitNumbers.length; i++) {
                let row = this.poMainInfo.debitIds[i];
                let row1 = this.poMainInfo.debitNumbers[i];
                if (i != 0) debitId += `, `
                debitId += ButtonHelper.nubButton_SystemDocument(SystemDocument.DebitNote, row.debitId, row1.debitNumber);
            }
        }
        return debitId
    }

    _preProcessEprData() {
        let result = [];

        this.poMainInfo.eprIds.forEach((epr) => {
            result.push({
                eprId: epr.EPRId,
                purchaseOrderNumber: this.poMainInfo.purchaseOrderNumber
            })
        })

        return result;
    }

    // Get value by property name, case insensitive
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }

    async refreshSectionBox() {
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", true);
        await this.getMainInfo();
        this.bindMainInfo()
        await this.dataTablesManager.refreshTables();
        this.updateButtonStates();
        this.$purchaseOrdersMainInfoSectionBox.section_box("option", "loading", false);
    }
}
