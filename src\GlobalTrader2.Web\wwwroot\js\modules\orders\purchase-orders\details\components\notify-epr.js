﻿import { EprAPIUrls } from '../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#'
import { NotifyEmailDialogContainer } from '../../../../../containers/notify-email/notify-email.container.dialog.js?v=#{BuildVersion}#';

export class NotifyEprDialog extends NotifyEmailDialogContainer {
    constructor(purchaseOrderId) {
        super({
            dialogId: 'po-notify-epr-dialog',
            formId: 'po-notify-epr-form',
            companyId: null,
            apiEndpoints: {
                templateEmail: `${EprAPIUrls}/${purchaseOrderId}/notification-template`,
                sendNewMessage: ``
            },
        });
    }

    async bindDataToForm() {
        const dataTemplate = await this.getDefaultTemplateEmail();
        this.$messageInputSummernote.summernote2('code', dataTemplate.message);
        this.$subjectInput.val(dataTemplate.subject);
    }
}
