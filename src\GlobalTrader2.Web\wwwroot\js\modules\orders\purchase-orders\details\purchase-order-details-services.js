﻿import { PurchaseOrderDetailsApiUrl, EprAPIUrls } from '../../../../config/api-endpoint-config.js?v=#{BuildVersion}#'

export class PurchaseOrderDetailsService {
    static #baseUrl = PurchaseOrderDetailsApiUrl;
    static #eprUrl = EprAPIUrls;

    static async getPurchaseOrderMainInfoAsync(purchaseOrderId) {
        return await GlobalTrader.ApiClient.getAsync(`${this.#baseUrl}/${purchaseOrderId}/main-info`);
    }

    static async getEprListAsync(purchaseOrderId, orderBy = null) {
        let url = `${this.#eprUrl}/list?poId=${purchaseOrderId}`

        if (orderBy !== null) {
            url += "&orderBy=" + orderBy;
        }

        return await GlobalTrader.ApiClient.getAsync(url);
    }
}
