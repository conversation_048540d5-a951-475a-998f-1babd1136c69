﻿import { EprAPIUrls } from '../../../../config/api-endpoint-config.js?v=#{BuildVersion}#'

export class EprService {
    static #baseUrl = EprAPIUrls;

    static async createNewEpr($form) {
        const url = `${this.#baseUrl}/save?purchaseOrderId=${$form.find('#PurchaseOrderId').val()}`;
        const headers = {
            'Content-Type': 'application/json',
            'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
        };

        const parseNumeric = (value) => {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 0 : parsed;
        };

        const formatDate = (dateString) => {
            if (!dateString) return null;
            return new Date(dateString).toISOString();
        };

        const payload = {
            purchaseOrderNumber: parseNumeric($form.find('#PurchaseOrderNumber').val()),
            isNew: $form.find('#ChKNewSupplier').prop("checked"),
            companyName: $form.find("#CompanyName").val() || "",
            orderValue: parseNumeric($form.find('#OrderValue').val()),
            currencyCode: "GBP",
            deliveryDate: formatDate($form.find('#DeliveryDate').val()),
            inAdvance: $form.find('#ChKInAdvance').prop("checked"),
            uponReceipt: $form.find('#ChKUponReceipt').prop("checked"),
            netSpecify: parseNumeric($form.find('#Net').val()) || null,
            otherSpecify: $form.find('#OtherSpecify').val() || "",
            tt: $form.find('#ChKTelegraphicTransfer').prop("checked"),
            cheque: $form.find('#ChKCheque').prop("checked"),
            creditCard: $form.find('#ChKCreditCard').prop("checked"),
            comments: $form.find('#Comment').val() || "",
            name: $form.find("#Name").val() || "",
            address: $form.find("#Address").val() || "",
            tel: $form.find("#Tel").val() || "",
            fax: $form.find("#Fax").val() || "",
            email: $form.find("#Email").val() || "",
            name1: $form.find("#Name1").val() || "",
            address1: $form.find("#Address1").val() || "",
            tel1: $form.find("#Tel1").val() || "",
            fax1: $form.find("#Fax1").val() || "",
            email1: $form.find("#Email1").val() || "",
            comment: $form.find("#Comment2").val() || "",
            name2: $form.find("#Name2").val() || "",
            address2: $form.find("#Address2").val() || "",
            tel2: $form.find("#Tel2").val() || "",
            fax2: $form.find("#Fax2").val() || "",
            email2: $form.find("#Email2").val() || "",
            proFormaAttached: $form.find('#ChKProFormaAttached').prop("checked"),
            raisedByNo: parseNumeric($form.find('#CmBRaisedBy').val()),
            raisedByDate: formatDate($form.find('#RaisedDate').val()),
            sorSigned: $form.find('#ChKSORSigned').prop("checked"),
            forStock: $form.find('#ChKFORStock').prop("checked"),
            valuesCorrect: $form.find('#ChKValuesCorrect').prop("checked"),
            authorized: "",
            authorizedDate: null,
            eraiMember: $form.find('#ChKERAIMemberYes').prop("checked"),
            eraiReported: $form.find('#ChKERAIReportedYes').prop("checked"),
            debitNotes: $form.find('#ChKOutStandingDebit').prop("checked"),
            apOpenOrders: $form.find('#ChKAdvancePayment').prop("checked"),
            acTotalValue: parseNumeric($form.find('#OutStandingDebitTotalValue').val()),
            acTotalValue1: parseNumeric($form.find('#AdvancePaymentTotalValue').val()),
            slComment: $form.find('#Comments').val() || "",
            slTerms: $form.find('#SalesLedgerTerms').val() || "",
            slOverdue: $form.find('#ChKSalesLedgerOverdue').prop("checked"),
            slTotalValue: parseNumeric($form.find('#SalesLedgerOverdueTotalValue').val()),
            paymentAuthorizedBy: $form.find('#PaymentAuthorizedBy').val() || "",
            countersigned: $form.find('#CounterSignedValue').val() || "",
            paymentAuthorizedDate: null,
            supplierCode: $form.find('#SupplierCode').val() || "",
            eprCompletedByNo: $form.find('#EPRCompletedByNo').val() === "" ? null : parseNumeric($form.find('#EPRCompletedByNo').val()),
            poLineIds: $form.find('#POLineIds').val() || ""
        };

        const response = await GlobalTrader.ApiClient.postAsync(url, JSON.stringify(payload), headers);

        return response;
    }
}
