﻿import { EprService } from "./purchase-order-epr.service.js?v=#{BuildVersion}#";

$(async () => {
    if (!hasPermission) {
        showToast("danger", window.localizedStrings.PageNotFound);
        return;
    }

    const purchaseOrdersEPRManager = new PurchaseOrdersEarlyPaymentRequestManager();
    purchaseOrdersEPRManager.initialize();
})

window.notifyEPRSaved = function () {
    showToast('success', window.localizedStrings.ApplicationSavedSuccessfully);
};

class PurchaseOrdersEarlyPaymentRequestManager {
    constructor() {
        this._keyword = "epr";
        this._purchaseOrderId = getParameterByName('po');
        this._eprId = getParameterByName("epr");

        this.OrderDetails = {
            $new: $("#ChKNewSupplier"),
            $orderValue: $("#OrderValue"),
            $deliveryDate: $("#DeliveryDate")
        }

        this.PaymentTerms = {
            $inAdvance: $("#ChKInAdvance"),
            $uponReceipt: $("#ChKUponReceipt"),
            $net: $("#Net"),
            $otherSpecify: $("#OtherSpecify")
        }

        this.PaymentMethod = {
            $tt: $("#ChKTelegraphicTransfer"),
            $cheque: $("#ChKCheque"),
            $creditCard: $("#ChKCreditCard"),
            $comment: $("#Comment")
        }

        this.References = {
            // Default
            $name: $("#Name"),
            $address: $("#Address"),
            $tel: $("#Tel"),
            $fax: $("#Fax"),
            $email: $("#Email"),

            $name1: $("#Name1"),
            $address1: $("#Address1"),
            $tel1: $("#Tel1"),
            $fax1: $("#Fax1"),
            $email1: $("#Email1"),

            $name2: $("#Name2"),
            $address2: $("#Address2"),
            $tel2: $("#Tel2"),
            $fax2: $("#Fax2"),
            $email2: $("#Email2"),

            $comment: $("#Comment2"),

            // Sales
            $proFormaAttached: $("#ChKProFormaAttached"),
            $raisedBy: $("#CmBRaisedBy"),
            $raisedDate: $("#RaisedDate"),

            // Manager
            $sorSigned: $("#ChKSORSigned"),
            $forStock: $("#ChKFORStock"),
            $valueCorrect: $("#ChKValuesCorrect")
        }

        this.AccountsOnly = {
            $eraiMemberYes: $("#ChKERAIMemberYes"),
            $eraiMemberNo: $("#ChKERAIMemberNo"),

            $eraiReportedYes: $("#ChKERAIReportedYes"),
            $eraiReportedNo: $("#ChKERAIReportedNo"),

            $outStandingDebitCheck: $("#ChKOutStandingDebit"),
            $outStandingDebitTotalValue: $("#OutStandingDebitTotalValue"),

            $advancePaymentCheck: $("#ChKAdvancePayment"),
            $advancePaymentTotalValue: $("#AdvancePaymentTotalValue"),

            $comments: $("#Comments"),

            $salesLedgerTerms: $("#SalesLedgerTerms"),
            $salesLedgerOverdueCheck: $("#ChKSalesLedgerOverdue"),
            $salesLedgerOverdueTotalValue: $("#SalesLedgerOverdueTotalValue"),

            $counterSigned: $("#CounterSignedValue")
        }

        this.$form = $("#epr-form");

        this.$saveAndSendButton = $("#SaveAndSend");
        this.$saveAndPrintButton = $("#SaveAndPrint");
        this.$deleteButton = $("#SaveAndDelete");

        this.hasCompletePermission = pagePermissions.hasCompletePermission.toLowerCase() === "true";
    }

    initialize() {
        this._setupDropdowns();
        this._setupDatePicker();
        this._bindOnChangeIntegerInput();
        this._initFormValidation();
        this._bindOnSubmit();
    }

    _setupDropdowns() {
        this.References.$raisedBy.dropdown({
            serverside: false,
            endpoint: `/lists/employee`,
            valueKey: 'loginId',
            textKey: 'employeeName',
            placeholderValue: "",
            selectedValue: this.References.$raisedBy.data("raised-by-no"),
        });

        if (!this.hasCompletePermission) {
            $("#RaisedBySelect")[0].style.setProperty("margin-right", "0", "important");
            $("#RaisedBySelect")[0].style.setProperty("opacity", "0.7");
            $(".select-menu-gtv2-refresh-button").hide();
        }
    }

    _setupDatePicker() {
        this.OrderDetails.$deliveryDate.datepicker2({
            dateFormat: "dd/mm/yy"
        });

        this.References.$raisedDate.datepicker2({
            dateFormat: "dd/mm/yy"
        });

        if (this.References.$raisedDate.val() === "") {
            this.References.$raisedDate.datepicker2("setToDay");
        }

        if (!this.hasCompletePermission) {
            $(".ui-datepicker-trigger").hide();
        }
    }

    _bindOnChangeIntegerInput() {
        allowPositiveDecimalInput('#ValueCurrency');
        allowPositiveDecimalInput('#Net');
        allowPositiveDecimalInput('#OutStandingDebitTotalValue');
        allowPositiveDecimalInput('#AdvancePaymentTotalValue');
        allowPositiveDecimalInput('#SalesLedgerOverdueTotalValue');
    }

    _initFormValidation() {
        this.$form.validate({
            rules: {
                OrderValue: {
                    required: {
                        depends: () => {
                            return !this.OrderDetails.$orderValue.val()
                        }
                    }
                },
                DeliveryDate: {
                    required: {
                        depends: () => {
                            return !this.OrderDetails.$deliveryDate.val();
                        }
                    }
                }
            },
            highlight: function (element) {
                $(element).addClass('invalid-field');
                if ($("#required-error-message").hasClass("d-none")) {
                    $("#required-error-message").removeClass("d-none");
                }
            },
            unhighlight: function (element) {
                $(element).removeClass('invalid-field');
            },
            errorPlacement: function (error, element) {
            },
        })

        this.$form.on('keydown', 'input', function (event) {
            if (event.key === 'Enter') {
                event.preventDefault();
            }
        });
    }

    _bindOnSubmit() {
        this.$saveAndSendButton.on('click', async (e) => {
            e.preventDefault();
            if (this._eprId !== null) return;

            if (this.$form.valid()) {
                this.$saveAndSendButton.prop("disabled", true);
                const response = await EprService.createNewEpr(this.$form);

                if (response.success) {
                    window.notifyEPRSaved();

                    if (window.opener) {
                        $(window.opener.document).find("#open-notify-epr").attr("data-epr-id", response.data);
                        $(window.opener.document).find("#open-notify-epr").trigger("click");
                        window.close();
                    }
                    else {
                        this.$saveAndSendButton.prop("disabled", false);
                    }
                }
                else {
                    this.$saveAndSendButton.prop("disabled", false);
                    return showToast("danger", response.title);
                }
            };
        });
    }
}
