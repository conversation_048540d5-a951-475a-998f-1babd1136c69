﻿import { PurchaseOrderTableFilterComponent } from '../purchase-orders/components/purchase-order-table-filter-component.js?v=#{BuildVersion}#';
import { FieldType } from '../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#';
import { TextFilterHelper } from "../../../helper/text-filter-helper.js?v=#{BuildVersion}#";
import { DebounceHelper } from '../../../helper/debounce-helper.js?v=#{BuildVersion}#';
import { NumberType } from '../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#';
import { ROHSHelper } from "../../../helper/rohs-helper.js?v=#{BuildVersion}#";
$(async () => {
    const purchaseOrderManager = new PurchaseOrderManager();
    await purchaseOrderManager.init();
});
class PurchaseOrderManager {
    constructor() {
        this.isBuyerFirstLoad = true;
        this.$filterToggleButton = $('#btn-filter');
        this.isShowFilter = true;
        this.$purchaseOrderSectionBox = $('#purchase-order-box');
        this.clientFilterState = {
            countriesEndpoint: 'countries/dropdown-countries',
            buyerEndpoint: 'setup/security-settings/security-users/users-client',
            params: {
                clientNo: null
            }
        };
        this.purchaseOrderConfig = JSON.parse($('#filter-section-wrapper').attr("data-config"));

      
        this.tableFilter = null;
        this.$purchaseOrderTable = $('#purchase-order-table');

        this.pageSize = this.$purchaseOrderTable.data('default-page-size') || 10;
        this.defaultPageIndex = this.$purchaseOrderTable.data('default-page-index');
        this.$lockUnlockButton = $('#lock-unlock-button');
        this.isLockFilter = $('#lock-unlock-button').data('init-value') === 'True';
        this.$warningSection = $('#warning-section');
        this.filterStates = JSON.parse($('#filter-section-wrapper').attr("data-states"));
        this.sortIndex = $('#purchase-order-table').data('default-order-by') || 0;
        this.sortDirection = $('#purchase-order-table').data('default-sort-dir') || window.constants.sortASC;
        this.currentTab = $('#purchase-order-table').data('default-current-tab');
        this.isGlobalLogin = this.purchaseOrderConfig.isGlobalLogin;
        this.isGSA = this.purchaseOrderConfig.isGSA;
        this.filterInputs = [
            {
                fieldType: FieldType.NUMBER,
                label: localizedTitles.purchaseOrder,
                name: 'PONo',
                id: 'PONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.dateOrderedFrom,
                name: 'DateOrderedFrom',
                id: 'DateOrderedFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.partNo,
                name: 'Part',
                id: 'Part',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.dateOrderedTo,
                name: 'DateOrderedTo',
                id: 'DateOrderedTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'

            },
            {
                fieldType: FieldType.CHECKBOX,
                label: localizedTitles.recentOnly,
                name: 'RecentOnly',
                id: 'RecentOnly',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.deliveryDateFrom,
                name: 'DeliveryDateFrom',
                id: 'DeliveryDateFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: localizedTitles.poHubOnly,
                name: 'PohubOnly',
                id: 'PohubOnly',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.CHECKBOX,
                label: localizedTitles.includeClosed,
                name: 'IncludeClosed',
                id: 'IncludeClosed',
                value: '',
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.deliveryDateTo,
                name: 'DeliveryDateTo',
                id: 'DeliveryDateTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.company,
                name: 'CMName',
                id: 'CMName',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.expediteDateFrom,
                name: 'ExpediteDateFrom',
                id: 'ExpediteDateFrom',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },

            {
                fieldType: FieldType.TEXT,
                label: localizedTitles.contact,
                name: 'Contact',
                id: 'Contact',
                value: '',
                attributes: {
                    "maxlength": 50
                },
                locatedInContainerByClass: 'filter-column-1'
            },       
            {
                fieldType: FieldType.DATE,
                label: localizedTitles.expediteDateTo,
                name: 'ExpediteDateTo',
                id: 'ExpediteDateTo',
                value: '',
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.NUMBER,
                label: localizedTitles.ipoNo,
                name: 'IPONo',
                id: 'IPONo',
                value: '',
                attributes: {
                    "data-input-type": "numeric",
                    "data-input-format": "int",
                    "data-input-min": 0,
                    "data-input-max": 2147483647,
                    "data-input-type-allow-empty": true
                },
                extraPros: {
                    numberType: NumberType.INT
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.buyerName,
                name: 'BuyerName',
                id: 'BuyerName',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.buyerEndpoint,
                    valueKey: 'loginId',
                    textKey: 'employeeName',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.client,
                name: 'ClientName',
                id: 'Client',
                value: '',
                options: {
                    serverside: false,
                    endpoint: '/user-account/clients/active',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    params: { isIncludeClient: false },
                    placeholderValue: "",
                    onSelected: (data, selectedClient) => {
                        if (!data || !this.isGlobalLogin || this.isBuyerFirstLoad) {
                            this.isBuyerFirstLoad = false;
                            return;
                        };
                        this.clientFilterState.params.clientNo = selectedClient;
                        this.reloadBuyerDropdown();
                        this.reloadCountryDropdown();
                    },
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.country,
                name: 'Country',
                id: 'Country',
                value: '',
                options: {
                    serverside: false,
                    endpoint: this.clientFilterState.countriesEndpoint,
                    valueKey: 'countryId',
                    textKey: 'countryName',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-1'

            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.status,
                name: 'PurchaseOrderStatus',
                id: 'PurchaseOrderStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/purchase-order-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.checked,
                name: 'SOStatus',
                id: 'SOStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/po-approved-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-1'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.supplierApproval,
                name: 'SupplierApprovalStatus',
                id: 'SupplierApprovalStatus',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/supplier-approval-status',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },
            {
                fieldType: FieldType.SELECT,
                label: localizedTitles.as6081,
                name: 'AS6081',
                id: 'AS6081',
                value: '',
                options: {
                    serverside: false,
                    endpoint: 'lists/counterfeit-electronic-parts',
                    valueKey: 'id',
                    textKey: 'name',
                    isHideRefresButton: false,
                    isCacheApplied: true,
                    placeholderValue: "",
                },
                locatedInContainerByClass: 'filter-column-2'
            },

        ];
    }
    async init() {
        this.setUpSwitchingTab();
        await this.setUpFilter();
        this.initDataTable();
        this.setUpPurchaseOrderSectionBox();


    }
    async setUpFilter() {
        this.tableFilter = new PurchaseOrderTableFilterComponent('#filter-section-wrapper', 'Filter Results', {
            inputConfigs: this.filterInputs
        });
        await this.tableFilter.init();
        this.setUpRuleForDateTimeFilter('DeliveryDateFrom', 'DeliveryDateTo');
        this.setUpRuleForDateTimeFilter('DateOrderedFrom', 'DateOrderedTo');
        this.setUpRuleForDateTimeFilter('ExpediteDateFrom', 'ExpediteDateTo');

        this.hideClientName(!this.isGlobalLogin && !this.isGSA);
        this.hidePOHubOnlyFilter(!this.purchaseOrderConfig.isPOHub);
        this.hideIPOFilter(!this.purchaseOrderConfig.isPOHub);
        this.hideBuyerNameFilter(this.currentTab);
        this.tableFilter.on('applied.mtf', () => {
            this.$purchaseOrderTable.DataTable().rows().deselect();
            this.isShowFilter = true;
            this.tableFilter.updateAppliedFilterText();
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseOrderTable.DataTable(), true);

        })

        this.tableFilter.on('cancel.mtf', () => {
            if (this.currentXhr) {
                this.currentXhr.abort();
                this.$warningSection.show();
                $('#purchase-order-box').section_box('option', 'loading', false);
                this.showFilterButtons(true);
                this.tableFilter.toggleApplyCancelButtons(true);
            }
        })

        this.tableFilter.on('off.mtf', () => {
            this.$filterToggleButton.prop('checked', false)
            this.$purchaseOrderTable.DataTable().rows().deselect();
            this.isShowFilter = false;
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseOrderTable.DataTable());
        });

        this.tableFilter.on('reset.mtf', () => {
            history.pushState({ additionalInformation: 'Reset filter' }, 'Reset filter', `/Orders/PurchaseOrder`);

        });
        Object.values(this.filterStates).forEach(input => {

            if (input.isOn && input.isShown && input.value && input.name) {
                const currentInput = this.tableFilter.getInputElementByName(input.name);
                switch (input.fieldType) {
                    case FieldType.TEXT:
                        currentInput.setValue(TextFilterHelper.getFilterValue(input.searchType, input.value));
                        break;
                    case FieldType.NUMBER:
                        currentInput.setState(input);
                        break;

                    default:
                        currentInput.setValue(input.value);
                        break;
                }

                currentInput.setRequiredCheckbox(true);
                currentInput.syncInputState();
                currentInput.triggerControlChanged();
            }
        });
    }
    setUpPurchaseOrderSectionBox() {
        this.$purchaseOrderSectionBox.section_box({
            loading: true,
            loadingContentId: 'purchase-order-table_wrapper',
            onRefreshClick: async (event, ui) => {
                this.refreshDatatable();
            }
        });
    }
    async refreshDatatable() {
        GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseOrderTable.DataTable());
    }
    setUpSwitchingTab() {
        $('button[data-bs-toggle="tab"]').on('shown.bs.tab', (e) => {
            this.currentTab = $(e.target).data('view-level');
            this.hideBuyerNameFilter(this.currentTab);
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseOrderTable.DataTable(), true);
        });
    }
    initDataTable() {
        this.showFilterButtons(false);
        $('#purchase-order-box-content').hide();
        let poNoTitle = `${localizedTitles.poNo}`;
        if(this.purchaseOrderConfig.isPOHub){
            poNoTitle = `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.poNo}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.ipoNo}</div>
                        </span>`
        }
        let tableColumnsDefine = [
            {
                data: (row) => (
                    {
                        purchaseOrderId: row.purchaseOrderId,
                        purchaseOrderNumber: row.purchaseOrderNumber,
                        internalPurchaseOrderId: row.internalPurchaseOrderId,
                        internalPurchaseOrderNumber: row.internalPurchaseOrderNumber,
                    }
                ),
                name: 'No',
                title: `${poNoTitle}`,
                type: 'string',
                render: (data, type, row) => {
                    const purchaseUrl = GlobalTrader.PageUrlHelper.Get_Url_PurchaseOrder(data.purchaseOrderId);
                    const ipoUrl = GlobalTrader.PageUrlHelper.Get_Url_InternalPurchaseOrder(data.internalPurchaseOrderId)
                    if(!this.purchaseOrderConfig.isPOHub){
                       return `<span><a class="dt-hyper-link" href="${purchaseUrl}">${data.purchaseOrderNumber}</a></span>`;

                    }
                    else{
                        return `<span><a class="dt-hyper-link" href="${purchaseUrl}">${data.purchaseOrderNumber}</a></span><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${ipoUrl}">${data.internalPurchaseOrderNumber}</p>`;

                    }

                },
            },
            {
                data: (row) => (
                    {
                        partNo: row.part,
                        manufacturerCode: row.manufacturerCode,
                        manufacturerNo: row.manufacturerNo,
                        rohs: row.rohs
                    }
                ),
                name: 'partNo_manuFac',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.partNo}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.manufacturer}</div>
                        </span>`,
                type: 'string',
                render: (data, type, row) => {
                    let manufacturerCode = "";
                    const escapedManufacturerCode = DataTable.render.text().display(data.manufacturerCode ?? "");
                    manufacturerCode = GlobalTrader.StringHelper.setCleanTextValue(escapedManufacturerCode);

                    return `${ROHSHelper.writePartNo(data.partNo,data.rohs)}<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Manufacturer(data.manufacturerNo)}">${manufacturerCode}</p>`;
                },
            },
            {
                data: 'price',
                name: 'unitPrice',
                title: `${localizedTitles.price}`,
                type: 'string'
            },
            {
                data: (row) => (
                    {
                        quantityOutstanding: row.quantityOutstanding,
                        quantityOrdered: row.quantityOrdered,
                    }
                ),
                name: 'quantity',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.quantityOrdered}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.quantityOutstanding}</div>
                        </span>`,
                type: 'string',
                render: (data, type, row) => {

                    return `<p class="m-0 d-flex flex-row flex-nowrap align-items-center gap-5px" style="min-height: 15px;">${data.quantityOrdered}</p><p class="m-0" style="min-height: 15px;">${data.quantityOutstanding}</p>`;
                }
            },
            {
                data: (row) => (
                    {
                        companyName: row.companyName,
                        companyNo: row.companyNo,
                        contactNo: row.contactNo,
                        contactName: row.contactName,
                        isMakeYellow: row.isMakeYellow,
                        supplierMessage: row.supplierMessage
                    }
                ),
                name: 'company_contact',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.company}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.contact}</div>
                        </span>`,
                type: 'string',
                render: (data, type, row) => {
            

                    if (data.supplierMessage) {
                        let title = String(data.supplierMessage).replace("&#013;", "");
                        let hazardous = `<img src = "/img/hazardous/Hazardousone.png" title = '${title}'>`;
                        return `<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Company(data.companyNo)}"><span class="yellow-company d-inline-flex align-items-center gap-5px">${data.companyName}${hazardous}</span></p><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Contact(data.contactNo)}">${GlobalTrader.StringHelper.setCleanTextValue(data.contactName)}</p>`;

                    }
                    if (data.isMakeYellow) {
                        return `<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Company(data.companyNo)}"><span class="yellow-company">${data.companyName}</span></p><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Contact(data.contactNo)}">${GlobalTrader.StringHelper.setCleanTextValue(data.contactName)}</p>`;
                    }
                    return `<p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Company(data.companyNo)}">${data.companyName}</p><p class="m-0" style="min-height: 15px;"><a class="dt-hyper-link" href="${GlobalTrader.PageUrlHelper.Get_URL_Contact(data.contactNo)}">${GlobalTrader.StringHelper.setCleanTextValue(data.contactName)}</p>`;

                },
            },
            {
                data: 'deliveryDate',
                name: 'deliveryDate',
                title: `${localizedTitles.deliveryDate}`,
                type: 'string'
            },
            {
                data: (row) => (
                    {
                        status: row.status,
                        deliveryStatus: row.deliveryStatus,
                        rowCSS: row.rowCSS
                    }
                ), 
                name: 'quantity',
                title: `<span class="dt-column-title d-flex flex-column gap-1">
                            <div>${localizedTitles.status}</div>
                            <div class="dt-column-line"></div>
                            <div>${localizedTitles.deliveryStatus}</div>
                        </span>`,
                type: 'string',
                render: (data, type, row) => {
                    if (data.rowCSS) {
                        return `<p class="m-0" style="min-height: 15px;">${data.status}</p><p class="m-0" style="min-height: 15px;"><span class="${data.rowCSS}">${data.deliveryStatus}</span></p>`;
                    }
                    return `<p class="m-0" style="min-height: 15px;">${data.status}</p><p class="m-0" style="min-height: 15px;">${data.deliveryStatus}</p>`;
                },
            },
            {
                data: (row) => ({
                    poClientName: row.poClientName,
                    clientName: row.clientName

                }),
                name: 'clientName',
                title: `${localizedTitles.client}`,
                type: 'string',
                render: (data, type, row) => {
                    if (!this.purchaseOrderConfig.isPOHub) {
                        return data.poClientName;
                    }
                    else {
                        return `<p class="m-0 d-flex flex-row flex-nowrap align-items-center gap-5px" style="min-height: 15px;">${GlobalTrader.StringHelper.setCleanTextValue(data.poClientName)}</p><p class="m-0" style="min-height: 15px;">${GlobalTrader.StringHelper.setCleanTextValue(data.clientName)}</p>`;
                    }
                }
            },
        ]
        this.$purchaseOrderTable
            .DataTable({
                serverSide: true,
                ajax: {
                    url: '/api/orders/purchase-order/list',
                    type: 'POST',
                    contentType: 'application/json',
                    beforeSend: (xhr) => {
                        this.currentXhr = xhr;
                        this.$warningSection.hide();
                        this.tableFilter.toggleApplyCancelButtons(false);
                    },
                    error: function (xhr, status, error) {
                        console.log("AJAX Error: ", status, error);
                    },
                    data: (data) => {
                        let sortDir = data.order.length !== 0 ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : this.sortDirection;
                       const filtersData = this.tableFilter.getDisplayedFilterValues().map((filter) => {
                            if (filter.fieldType === FieldType.CHECKBOX && filter.value == null) {
                                return { ...filter, value: false };
                            }
                            return filter;
                        });
                        if (this.isShowFilter) {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                                filters: filtersData,
                                saveStates: this.isLockFilter,

                            });
                        } else {
                            return JSON.stringify({
                                draw: data.draw,
                                start: data.start,
                                length: data.length,
                                sortDir: sortDir,
                                viewLevel: this.currentTab,
                                orderBy: data.order.length !== 0 ? data.order[0].column : this.sortIndex,
                            });
                        }
                    },


                },
                info: true,
                scrollCollapse: true,
                responsive: true,
                select: false,
                displayStart: this.defaultPageIndex * this.pageSize,
                paging: true,
                ordering: true,
                columnDefs: [
                    { "orderSequence": [window.constants.sortASCName, window.constants.sortDESCName], "targets": "_all" },
                    {
                        "orderable": false,
                        "targets": [6]
                    }
                ],
                searching: false,
                pageLength: this.pageSize,
                lengthMenu: [5, 10, 25, 50],


                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                order: [[this.sortIndex, GlobalTrader.SortHelper.getSortDirNameById(this.sortDirection)]],
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                    infoFiltered: "",
                    lengthMenu: "_MENU_ per page",
                },
                columns: tableColumnsDefine,
                rowId: 'purchaseOrderId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                },
                dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
                    '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
                    '<"dt-layout-cell dt-layout-end" p >><"clear">'
            })
            .on('preXhr.dt', () => {
                this.$purchaseOrderSectionBox.section_box("option", "loading", true);
                this.showFilterButtons(false);
                if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                    this.tableFilter.toggleApplyCancelButtons(false);
                }
            })
            .on('draw.dt', () => {
                this.$purchaseOrderSectionBox.section_box("option", "loading", false);
                $('#purchase-order-box-content').show();
                this.$purchaseOrderTable.DataTable().columns.adjust();
                this.showFilterButtons(true);

                if (this.tableFilter.state === this.tableFilter.ViewState.INVISIBLE) {
                    this.tableFilter.toggleFilterVisible(false);
                    this.tableFilter.setState(this.tableFilter.ViewState.INVISIBLE)
                } else if (this.tableFilter.state !== this.tableFilter.ViewState.VISIBLE) {
                    this.tableFilter.toggleFilterVisible(false);
                }
                this.tableFilter.toggleApplyCancelButtons(true);
                this.tableFilter.updateAppliedFilterText();

                // Remove neutral sorting icon
                const tableId = this.$purchaseOrderTable.DataTable().table().node().id;
                $(`#${tableId} thead th`)
                    .removeClass('dt-orderable-asc dt-orderable-desc')
                    .addClass('position-relative');

                $(`#${tableId} thead th:not(.dt-orderable-none)`)
                    .attr('role', 'button');

                $(`#${tableId} thead th .dt-column-order`).addClass('dt-column-order-custom');

                $(`#${tableId} .badge-hover-text`).each((index, element) => {
                    const handleOnStopMouseEnterDebounce = DebounceHelper.debounce(this.handleOnStopMouseEnter, 100)
                    const handleOnStopMouseLeaveDebounce = DebounceHelper.debounce(this.handleOnStopMouseLeave, 2000)

                    let mouseLeaveTimeoutTask;

                    $(element)
                        .on("mouseenter", (event) => {
                            // Show on stop label 
                            handleOnStopMouseEnterDebounce(event, element);

                            // Cancel hide on stop label
                            if (mouseLeaveTimeoutTask) clearTimeout(mouseLeaveTimeoutTask);
                        })
                        .on("mouseleave", (event) => {

                            // Hide on stop label 
                            mouseLeaveTimeoutTask = handleOnStopMouseLeaveDebounce(event, element);
                        });
                })
            })
            ;

        this.$filterToggleButton.on("change", () => {
            this.isShowFilter = this.$filterToggleButton.is(":checked");
            this.$purchaseOrderTable.DataTable().rows().deselect();
            this.showFilterSection(this.isShowFilter)
            GlobalTrader.Helper.reloadPagingDatatableServerSide(this.$purchaseOrderTable.DataTable());
        });

        this.$lockUnlockButton.on("click", () => {
            this.isLockFilter = !this.isLockFilter;
            this.setLockOrUnlockFilter(this.isLockFilter);
        })

    }
    showFilterSection(show) {
        show ? $('#filter-section-wrapper').show() : $('#filter-section-wrapper').hide();
    }
    hideBuyerNameFilter(viewLevel) {
        const inputs = this.tableFilter.getInputs();
        viewLevel == 0 ? inputs["BuyerName"].instance.wrapper.hide() : inputs["BuyerName"].instance.wrapper.show();
    }
    hidePOHubOnlyFilter(isHide) {
        const inputs = this.tableFilter.getInputs();
        if(isHide){
            inputs["PohubOnly"].instance.wrapper.hide()
        }
        else { 
            inputs["PohubOnly"].instance.wrapper.show();
            $('#PohubOnly').prop('checked',true);
        }
    }
    hideIPOFilter(isHide) {
        const inputs = this.tableFilter.getInputs();
        isHide ? inputs["IPONo"].instance.wrapper.hide() : inputs["IPONo"].instance.wrapper.show();
    }
    setLockOrUnlockFilter(isLock) {
        const innerHtml = isLock ? '<img alt="lock-button" src="/img/icons/lock.svg">' : '<img alt="lock-button" src="/img/icons/lock-open.svg">';
        this.$lockUnlockButton.html(innerHtml);
    }
    showFilterButtons(isShow) {
        if (isShow) {
            this.$lockUnlockButton.removeClass('force-disabled-content');
            this.tableFilter.enableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        } else {
            this.$lockUnlockButton.addClass('force-disabled-content');
            this.tableFilter.disableButtons(['apply', 'off', 'hide', 'reset', 'show']);
        }
    }
    hideClientName(isHide) {
        const inputs = this.tableFilter.getInputs();
        isHide ? inputs["ClientName"].instance.wrapper.hide() : inputs["ClientName"].instance.wrapper.show();
    }
    reloadBuyerDropdown() {
        let params = {
            clientNo: this.clientFilterState.params.clientNo
        }
        const $salesmanDropdown = $("#purchase-order-box-content #BuyerName");
        $salesmanDropdown.dropdown("reset");
        $salesmanDropdown.dropdown("reload", this.clientFilterState.salespersonEndpoint, params);
    }
    reloadCountryDropdown() {
        let params = {
            clientId: this.clientFilterState.params.clientNo
        }
        const $countryDropdown = $("#purchase-order-box-content #Country");
        $countryDropdown.dropdown("reset");
        $countryDropdown.dropdown("reload", this.clientFilterState.countriesEndpoint, params);
    }
    setUpRuleForDateTimeFilter(fromDateId, toDateId) {
        $(`#${fromDateId}`).datepicker2({
            dateFormat: "dd/mm/yy",
            beforeShow: function () {
                const date = $(`#${toDateId}`).datepicker("getDate");
                if (!date) {
                    $(`#${fromDateId}`).datepicker2("option", "maxDate", null);
                }
                else {
                    const dateValue = new Date(date);
                    $(`#${fromDateId}`).datepicker2("option", "maxDate", dateValue);

                }
            }
        });

        $(`#${toDateId}`).datepicker2({
            dateFormat: "dd/mm/yy",
            beforeShow: function () {
                const date = $(`#${fromDateId}`).datepicker("getDate");
                if (!date) {
                    $(`#${toDateId}`).datepicker2("option", "minDate", null);

                }
                else {
                    const dateValue = new Date(date);
                    $(`#${toDateId}`).datepicker2("option", "minDate", dateValue);
                }
            }
        });
       
    }
}
