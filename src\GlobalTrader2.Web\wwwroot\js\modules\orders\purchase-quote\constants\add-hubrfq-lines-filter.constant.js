import { FieldType } from "../../../../components/table-filter/constants/field-type.constant.js?v=#{BuildVersion}#";
import { NumberType } from "../../../../components/table-filter/constants/number-type.constant.js?v=#{BuildVersion}#";

const pageId = $("#add-hubrfq-line-dialog [name='pageId']").val();

export const ADD_HUBRFQ_LINES_FILTER_INPUTS = [
  {
    fieldType: FieldType.NUMBER,
    label: addHubrfqLinesLocalize.reqNo,
    name: "reqNoSearch",
    id: "addHubrfqLinesReqNoSearch",
    attributes: {
      "data-input-type": "numeric",
      "data-input-format": "int",
      "data-input-min": 0,
      "data-input-max": 2147483647,
      "data-input-type-allow-empty": true,
    },
    extraPros: {
      numberType: NumberType.INT,
    },
    value: "",
  },
  {
    fieldType: FieldType.DATE,
    label: addHubrfqLinesLocalize.dateReceivedFrom,
    name: "receivedDate<PERSON>rom",
    id: "addHubrfqLinesReceivedDateFrom",
    value: "",
    pairWith: "receivedDateTo",
  },
  {
    fieldType: FieldType.TEXT,
    label: addHubrfqLinesLocalize.partNo,
    name: "partSearch",
    id: "addHubrfqLinesPartSearch",
    value: "",
  },
  {
    fieldType: FieldType.DATE,
    label: addHubrfqLinesLocalize.dateReceivedTo,
    name: "receivedDateTo",
    id: "addHubrfqLinesReceivedDateTo",
    value: "",
  },
  {
    fieldType: FieldType.TEXT,
    label: addHubrfqLinesLocalize.bomName,
    name: "bomName",
    id: "addHubrfqLinesBomName",
    value: "",
  },
  {
    fieldType: FieldType.SELECT,
    label: addHubrfqLinesLocalize.client,
    name: "client",
    id: "addHubrfqLinesClient",
    value: "",
    options: {
      serverside: false,
      endpoint: '/user-account/clients/active',
      textKey: "name",
      valueKey: "id",
    }
  },
  {
    fieldType: FieldType.SELECT,
    label: addHubrfqLinesLocalize.hubrfq,
    name: "hubrfq",
    id: "addHubrfqLinesHubrfq",
    value: "",
    options: {
      serverside: false,
      endpoint: `/user-account/profile/levelLists`,
      isHideRefresButton: false,
      isCacheApplied: true,
      placeholderValue: 0,
      placeholder: null,
    }
  },
];
