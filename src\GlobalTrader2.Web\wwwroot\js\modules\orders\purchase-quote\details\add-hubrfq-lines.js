import { LiteDatatable } from "../../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#";
import { LiteFormDialog } from "../../../../components/base/lite-form-dialog.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { ADD_HUBRFQ_LINES_FILTER_INPUTS } from "../constants/add-hubrfq-lines-filter.constant.js?v=#{BuildVersion}#";

export class AddHubrfqLinesHandler {
    constructor() {
        this.form = null;
        this.filter = null;
        this.resultTable = null;
        this.selectedItems = [];
        this.init();
    }

    async init() {
        this.form = new LiteFormDialog("#add-hubrfq-line-dialog", {
            width: '1200px',
            closeWhenSuccess: true,
            url: "/api/orders/purchase-quote/add-purchase-quote-lines",
            buttons: [
                { name: "save", icon: "check", alt: "yes", display: 'Yes' },
                { name: "cancel", icon: "xmark", alt: "no", display: 'No' }
            ],
        });

        this.form.$form.on('closedWithSuccessedResponse.mf', async () => {
            $('#close-btn').prop('disabled', true);
        });

        await this.initAddItemFilter();
        this.setupSearchEventListener();
    }

    async initAddItemFilter() {
        // Check if filter container exists
        if (!$("#add-hubrfq-line-filter").length) {
            throw new Error("Filter container #add-hubrfq-line-filter not found in DOM");
        }

        this.filter = new TableFilterComponent(
            "#add-hubrfq-line-filter",
            addHubrfqLinesLocalize.addNewHUBRFQLinesTitle,
            {
                inputConfigs: ADD_HUBRFQ_LINES_FILTER_INPUTS,
                showButtons: false,
                wrapperClass: "bg-none m-0 p-0",
            }
        );

        this.filter.on("controlchanged.mtf", () => {
            const hasFilterValues = this.checkIfAnyAddItemFilterActive();
            console.log("Filter changed, has active values:", hasFilterValues);
            $("#search-hubrfq-line-btn").prop("disabled", !hasFilterValues);
        });

        await this.filter.init();
    }

    checkIfAnyAddItemFilterActive() {
        const filterValues = this.filter.getAllValue();

        return Object.keys(filterValues).some((key) => {
            const filterValue = filterValues[key];
            return (
                filterValue.isOn
            );
        });
    }

    async initAddLinesTable() {

        if (!$("#add-hubrfq-line-table").length) {
            throw new Error("Table container #add-hubrfq-line-table not found in DOM");
        }

        if (this.resultTable && this.resultTable.state && this.resultTable.state.table) {
            await this.resultTable.reloadAsync();
            return;
        }

        this.resultTable = new LiteDatatable("#add-hubrfq-line-table", {
            serverSide: true,
            autoWidth: true,
            paging: true,
            ordering: true,
            pageConfig: {
                pageSize: 10,
            },
            order: [[1, "desc"]],
            ajax: {
                url: "/api/orders/purchase-quote/item-search",
                type: "POST",
            },
            selectOverride: {
                toggleable: true,
                info: false,
            },
            columns: [
                {
                    data: "customerRequirementNumber",
                    title: addHubrfqLinesLocalize.customerRequirementNumber,
                    orderSequence: ["desc", "asc"],
                    className: "text-wrap text-break",
                    defaultContent: "",
                    width: "15%",
                },
                {
                    data: "companyName",
                    title: addHubrfqLinesLocalize.companyName,
                    orderSequence: ["desc", "asc"],
                    className: "text-wrap text-break",
                    width: "31%",
                    defaultContent: "",
                    render: function (data, type, row) {
                        return GlobalTrader.StringHelper.setCleanTextValue(data);
                    },
                },
                {
                    data: "receivedDate",
                    title: addHubrfqLinesLocalize.receivedDateText,
                    className: "text-wrap text-break",
                    orderSequence: ["desc", "asc"],
                    defaultContent: "",
                    width: "11%",
                },
                {
                    data: "quantity",
                    title: addHubrfqLinesLocalize.quantity,
                    className: "text-wrap text-break",
                    orderSequence: ["desc", "asc"],
                    defaultContent: "",
                    width: "11%",
                },
                {
                    data: "formatedPrice",
                    title: addHubrfqLinesLocalize.formatedPrice,
                    orderSequence: ["desc", "asc"],
                    className: "text-wrap text-break",
                    defaultContent: "",
                    width: "11%",
                },
                {
                    data: "bomName",
                    title: addHubrfqLinesLocalize.bomHeader,
                    orderSequence: ["desc", "asc"],
                    className: "text-wrap text-break",
                    defaultContent: "",
                    width: "11%",
                },
            ],
            rowId: "customerRequirementId",
        });

        this.resultTable.init();
        this._registerTableEvents();
    }

    _registerTableEvents() {
        if (this.resultTable && this.resultTable.state && this.resultTable.state.table) {
            this.resultTable.state.table.on("select deselect", (e, dt, type, indexes) => {
                this.updateSelectedItems();
                this.updateDialogButtons();
            });
        }
    }

    updateSelectedItems() {
        if (this.resultTable && this.resultTable.state && this.resultTable.state.table) {
            this.selectedItems = this.resultTable.state.table
                .rows(".selected")
                .data()
                .toArray()
                .map((row) => ({
                    customerRequirementId: row.customerRequirementId,
                    customerRequirementNumber: row.customerRequirementNumber,
                    part: row.part,
                    companyName: row.companyName,
                }));
        }
    }

    updateDialogButtons() {
        // TODO: Implement dialog button update logic based on selected items
        // This should be connected to the existing form dialog functionality
        console.log(`Selected ${this.selectedItems.length} items`);
    }

    getSelectedItems() {
        return this.selectedItems;
    }

    async reload(filterObj) {
        if (this.resultTable) {
            await this.resultTable.reloadAsync(filterObj);
        }
    }

    async search(filterValues) {
        let filterObj = {
            customerRequirementNoLo: filterValues.reqNoSearch?.isOn
                ? filterValues.reqNoSearch.low
                : null,
            customerRequirementNoHi: filterValues.reqNoSearch?.isOn
                ? filterValues.reqNoSearch.hi
                : null,
            partSearch: filterValues.partSearch?.isOn
                ? filterValues.partSearch.value
                : null,
            bomName: filterValues.bomName?.isOn
                ? filterValues.bomName.value
                : null,
            hubrfq: filterValues.hubrfq?.isOn
                ? filterValues.hubrfq.value
                : null,
            client: filterValues.client?.isOn
                ? filterValues.client.value
                : null,
            receivedDateFrom: filterValues.receivedDateFrom?.isOn
                ? filterValues.receivedDateFrom.value
                : null,
            receivedDateTo: filterValues.receivedDateTo?.isOn
                ? filterValues.receivedDateTo.value
                : null,
        };

        await this.reload(filterObj);

        // Show the table wrapper after successful search
        $("#add-hubrfq-line-table-wrapper").removeClass("d-none");
    }

    setupSearchEventListener() {
        const searchBtn = $("#search-hubrfq-line-btn");

        if (!searchBtn.length) {
            return;
        }

        searchBtn.button().on("click", async (event) => {
            event.stopPropagation();

            // Check if search button is disabled
            if (searchBtn.prop("disabled")) {
                return;
            }

            // Get filter values
            const filterValues = this.filter.getAllValue();

            // Initialize table if not already done
            if (!this.resultTable) {
                await this.initAddLinesTable();
            }

            // Perform search with current filter values
            await this.search(filterValues);
        });
    }
}
