﻿import { PageSearchSelectComponent } from '../../../../components/search-select/page-search-select.component.js?v=#{BuildVersion}#';

const requiredErrorMessage = window.localizedStrings.requiredField;
const saveChangedMessage = window.localizedStrings.saveChangedMessage;
const pleaseCheckBelowAndTryAgain = window.localizedStrings.pleaseCheckBelowAndTryAgain;
const thereWereSomeProblemsWithYourForm = window.localizedStrings.thereWereSomeProblemsWithYourForm;
const save = window.localizedStrings.save;
const cancel = window.localizedStrings.cancel;
const canEditAS9120 = $("#can-edit-as9120").val() === "true";

const dropdownCurrency = ["Currency"];
const dropdownContact = ["Contact"];
const dropdownDivisionHeader = ["DivisionHeader"];
const dropdownTerms = ["Terms"];
const dropdownIncoterms = ["Incoterms"];

const apiEndpoints = {
    currency: "/lists/buy-currency-by-global-no",
    incoterms: "/lists/incoterms",
    terms: "/terms/dropdown-terms",
    divisions: "/lists/divisions",
    employee: "/lists/employee",
    contact: "/lists/contact/",
};

const defaultIdValue = "0";

let isSubmitting = false;

let editQuoteSupportTeamMemberSearchSelect;

let dataQuoteMainInfo = null;

let companyId = defaultIdValue;

const option = {
    minDate: null,
    dateFormat: "dd/mm/yy",
};

$(() => {
    $('#edit-quote-form').validate({
        onfocusout: false,
        onkeyup: false,
        onclick: false,
        ignore: [],
        rules: {
            Contact: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            DivisionHeader: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            DateQuoted: {
                notEmptyOrWhiteSpace: true,
            },
            Terms: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            Currency: {
                notEmptyOrWhiteSpace: true,
                min: 1
            },
            Freight: {
                decimalRangeCheck: {
                    min: `0.00`, max: `100000000000000000.00`, decimalPartLength: 2, message: editQuoteLocalizedStrings.freightMessage
                }
            },
            Incoterms: {
                notEmptyOrWhiteSpace: true,
                min: 1
            }
        },
        messages: {
            Contact: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            DivisionHeader: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            DateQuoted: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
            },
            Terms: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            Currency: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            },
            Incoterms: {
                notEmptyOrWhiteSpace: requiredErrorMessage,
                min: requiredErrorMessage
            }
        },
        errorPlacement: function (error, element) {
            const inputName = element.attr("name");

            if (dropdownCurrency.includes(inputName)
                || dropdownContact.includes(inputName)
                || dropdownDivisionHeader.includes(inputName)
                || dropdownTerms.includes(inputName)
                || dropdownIncoterms.includes(inputName)
                || inputName === "DateQuoted"
                || inputName === "Freight") {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        invalidHandler: function (event, validator) {
            displayError("");
        },
    });
    $("#quote-main-information-edit-btn").button().on("click", async function (event) {
        event.stopPropagation();

        $("#edit-quote-dialog").dialog("open");
        dataQuoteMainInfo = stateValue.quoteMainInfo;
        const currentDialog = $("#edit-quote-dialog").dialog("instance");
        currentDialog.setLoading(true);

        initSearchSelects();
        initDropdowns();

        await fetchDropdownsAsync();

        await fetchEditQuote();

        if (canEditAS9120 != null) {
            $("#edit-quote-as9120").prop("disabled", !canEditAS9120);
        }
        else {
            $("#edit-quote-as9120").prop("disabled", true);
        }

        $("#edit-quote-form").find('input[name="DateOrdered"]').datepicker2();

        focusEdit();
    });

    const dialogEditquoteForm = $("#edit-quote-dialog").dialog({
        width: "40vw",
        height: "auto",
        autoOpen: false,
        maxHeight: $(window).height(),
        modal: true,
        create: function (event, ui) {
            const dialog = $(this).dialog("widget");
            dialog.css("maxWidth", "800px");
        },
        buttons: [
            {
                text: "Save",
                click: async function () {
                    const isValid = $("#edit-quote-form").valid();
                    const currentDialog = $("#edit-quote-dialog").dialog("instance");

                    if (isSubmitting) {
                        return;
                    }

                    if (!isValid) {
                        focusError();
                        return;
                    }

                    const formObject = getFormData();

                    try {
                        isSubmitting = true;
                        currentDialog.setLoading(true);

                        const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() };
                        
                        let response;

                        response = await GlobalTrader.ApiClient.putAsync(`orders/quotes/edit`, formObject, header);

                        if (response.success) {
                            dialogEditquoteForm.dialog("close");
                            showToast('success', saveChangedMessage);
                        }

                        $("#quote-main-information-box .section-box-refesh-button").trigger("click");
                        $("#quote-lines-box .section-box-refesh-button").trigger("click");
                    } catch (error) {
                        if (error.response?.data?.message) {
                            displayError(error.response.data.message);
                        } else {
                            displayError("An unexpected error occurred.");
                        }
                    } finally {
                        isSubmitting = false;
                        currentDialog.setLoading(false);
                    }
                }
            },
            {
                text: "Cancel",
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        close: function (event, ui) {
            companyId = defaultIdValue;
            $("#edit-quote-form")[0].reset();
            $("#edit-quote-form").validate().resetForm();
            $(".form-error-summary").hide();

            $("#edit-quote-currency-code").addClass("d-none");

            resetSearchSelects();
        },
        open: function (event, ui) {
            $(this).removeClass('d-none');
            $('.ui-dialog-titlebar-close').css('display', 'none');
            $("#edit-quote-form #currency-dropdown").empty();
            $("#edit-quote-form #currency-dropdown").append(`<option value="0">Select...</option>`)
        },
    });

    allowPositiveDecimalInput('#edit-quote-form input[id="Freight"]', true, 2);

    $('#edit-quote-form input[id="Freight"]').on("focusout", function () {
        if ($(this).val() === "") {
            $(this).val(0);
        }
    });

    $("#DateQuoted").datepicker2(option);

    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${save}')`).addClass("btn btn-primary fw-normal").html(`<img src="/img/icons/save.svg" alt="${save}"> ${save}`);
    $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${cancel}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/slash.svg" alt="${cancel}"> ${cancel}`);

});

function initDropdowns() {
    $("#edit-quote-form #contact-dropdown").dropdown({
        endpoint: apiEndpoints.contact + dataQuoteMainInfo.companyNo,
        serverside: false,
        deferLoad: true,
        valueKey: 'contactId',
        textKey: 'contactName',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#edit-quote-form #Contact-refresh-button")
    });

    $("#edit-quote-form #salesman-dropdown").dropdown({
        endpoint: apiEndpoints.employee,
        serverside: false,
        deferLoad: true,
        valueKey: 'loginId',
        textKey: 'employeeName',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#edit-quote-form #Salesman-refresh-button"),
        onSelected: async (data, selectedSalesman) => {
            if (selectedSalesman != null) {
                const securityUser = await GlobalTrader.ApiClient.getAsync(`/setup/security-settings/security-users/security-user?loginid=${selectedSalesman}`)

                $("#edit-quote-form #DivisionName").text(securityUser.data.divisionName);
                $("#edit-quote-form #DivisionNo").val(securityUser.data.divisionNo);
            }
        },
    });

    $("#edit-quote-form #division-header-dropdown").dropdown({
        endpoint: apiEndpoints.divisions,
        serverside: false,
        deferLoad: true,
        valueKey: 'divisionId',
        textKey: 'divisionName',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#edit-quote-form #DivisionHeader-refresh-button"),
    });

    $("#edit-quote-form #terms-dropdown").dropdown({
        endpoint: apiEndpoints.terms,
        serverside: false,
        deferLoad: true,
        valueKey: 'id',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#edit-quote-form #Terms-refresh-button"),
    });

    $("#edit-quote-form #currency-dropdown").dropdown({
        endpoint: apiEndpoints.currency,
        deferLoad: true,
        serverside: false,
        valueKey: 'currencyId',
        textKey: 'name',
        params: {
            globalNo: dataQuoteMainInfo.globalCurrencyNo,
        },
        onSelected: (data, selectedCurrency) => {
            const selected = data.find(item => item.currencyId == selectedCurrency);
            if (selected) {
                $("#edit-quote-currency-code").removeClass("d-none");
                $("#edit-quote-currency-code").text(selected.code);
            }
            else {
                $("#edit-quote-currency-code").addClass("d-none");
            }
        },
        isCacheApplied: false,
        isHideRefresButton: true,
        refreshButton: $("#edit-quote-form #CurrencyNo-refresh-btn")
    });

    $("#edit-quote-form #incoterms-dropdown").dropdown({
        endpoint: apiEndpoints.incoterms,
        serverside: false,
        deferLoad: true,
        valueKey: 'incotermId',
        textKey: 'name',
        isHideRefresButton: true,
        isCacheApplied: false,
        refreshButton: $("#edit-quote-form #Incoterms-refresh-button")
    });

    $("#edit-quote-form #CurrencyNo-refresh-button").on("click", () => {
        $("#edit-quote-form #currency-dropdown").dropdown("refresh");
    });

    setupDropdownRefresh(
        "#edit-quote-form #Contact-refresh-button",
        "#edit-quote-form #contact-dropdown",
        apiEndpoints.contact + dataQuoteMainInfo.companyNo,
        { refreshData: true },
        "contactId",
        "contactName"
    );

    setupDropdownRefresh(
        "#edit-quote-form #Salesman-refresh-button",
        "#edit-quote-form #salesman-dropdown",
        apiEndpoints.employee,
        { refreshData: true },
        "loginId",
        "employeeName"
    );

    setupDropdownRefresh(
        "#edit-quote-form #DivisionHeader-refresh-button",
        "#edit-quote-form #division-header-dropdown",
        apiEndpoints.divisions,
        { refreshData: true },
        "divisionId",
        "divisionName"
    );

    setupDropdownRefresh(
        "#edit-quote-form #CurrencyNo-refresh-button",
        "#edit-quote-form #currency-dropdown",
        apiEndpoints.currency,
        { refreshData: true, globalNo: dataQuoteMainInfo.globalCurrencyNo },
        "currencyId",
        "name"
    );

    setupDropdownRefresh(
        "#edit-quote-form #Terms-refresh-button",
        "#edit-quote-form #terms-dropdown",
        apiEndpoints.terms,
        { refreshData: true },
        "id",
        "name"
    );

    setupDropdownRefresh(
        "#edit-quote-form #Incoterms-refresh-button",
        "#edit-quote-form #incoterms-dropdown",
        apiEndpoints.incoterms,
        { refreshData: true },
        "incotermId",
        "name"
    );
}

function displayError(message) {
    const errorContainer = $(".form-error-summary");
    errorContainer.find("div").html(`<p>${thereWereSomeProblemsWithYourForm}</p><p>${pleaseCheckBelowAndTryAgain}</p><p>${message}</p>`);
    errorContainer.show();
}

function initSearchSelects() {
    editQuoteSupportTeamMemberSearchSelect = new PageSearchSelectComponent('edit-quote-support-team-member-auto-search', 'QuoteSupportTeamMemberId', 'single', 'keyword', '/orders/customer-requirements/auto-search-sale-persion');
}

function resetSearchSelects() {
    editQuoteSupportTeamMemberSearchSelect.resetSearchSelect();
}

async function fetchDropdownsAsync() {
    const contactPromise = $("#edit-quote-form #contact-dropdown").dropdown("reload", apiEndpoints.contact + dataQuoteMainInfo.companyNo, { refreshData: false });
    const salesPersonDropdownPromise = $("#edit-quote-form #salesman-dropdown").dropdown("reload", apiEndpoints.employee, { refreshData: false});
    const divisionHeaderPromise = $("#edit-quote-form #division-header-dropdown").dropdown("reload", apiEndpoints.divisions, { refreshData: false });
    const currencyPromise = $("#edit-quote-form #currency-dropdown").dropdown("reload", apiEndpoints.currency, { refreshData: false, globalNo: dataQuoteMainInfo.globalCurrencyNo });
    const termsPromise = $("#edit-quote-form #terms-dropdown").dropdown("reload", apiEndpoints.terms, { refreshData: false });
    const incotermsPromise = $("#edit-quote-form #incoterms-dropdown").dropdown("reload", apiEndpoints.incoterms, { refreshData: false });

    await Promise.all([
        contactPromise,
        salesPersonDropdownPromise,
        divisionHeaderPromise,
        currencyPromise,
        termsPromise,
        incotermsPromise
    ]);
}

function getFormData() {
    const formData = $("#edit-quote-form").serializeArray();
    let formObject = {};

    const nullIfEmpty = v => v === "" ? null : v;
    const boolIfOn = v => v === "on";

    const specialFields = {
        Contact: nullIfEmpty,
        Salesman: nullIfEmpty,
        DivisionHeader: nullIfEmpty,
        Terms: nullIfEmpty,
        Currency: nullIfEmpty,
        AS9120: boolIfOn,
        IsImportant: boolIfOn
    };

    formData.forEach(function (field) {
        if (formObject.hasOwnProperty(field.name)) return;

        if (field.name === "Incoterms") {
            formObject['Incoterm'] = nullIfEmpty(field.value);
            return;
        }

        if (specialFields.hasOwnProperty(field.name)) {
            formObject[field.name] = specialFields[field.name](field.value);
            return;
        }

        if (field.name === "DateQuoted") {
            formObject[field.name] = parseDateQuoted(field.value);
            return;
        }

        if (field.value.trim() !== "") {
            formObject[field.name] = field.value.trim();
        }
    });

    return formObject;
}

function parseDateQuoted(value) {
    if (value.trim() === "") return null;
    const parts = value.split('/');
    if (parts.length === 3) {
        const [day, month, year] = parts;
        return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
    }
    return null;
}

async function fetchEditQuote() {
    if (dataQuoteMainInfo != null) {
        $("#QuoteId").val(dataQuoteMainInfo.quoteId);
        await populateSearchSelect();

        $("#edit-quote-form #contact-dropdown").dropdown("select", dataQuoteMainInfo.contactNo ?? "");
        $("#edit-quote-form #salesman-dropdown").dropdown("select", dataQuoteMainInfo.salesman ?? "");
        $("#edit-quote-form #division-header-dropdown").dropdown("select", dataQuoteMainInfo.divisionHeaderNo ?? "");
        $("#edit-quote-form #terms-dropdown").dropdown("select", dataQuoteMainInfo.termsNo ?? "");
        $("#edit-quote-form #currency-dropdown").dropdown("select", dataQuoteMainInfo.currencyNo ?? "");
        $("#edit-quote-form #incoterms-dropdown").dropdown("select", dataQuoteMainInfo.incotermNo ?? "");

        $("#edit-quote-form #Customer").text(dataQuoteMainInfo.companyName);
        $("#edit-quote-form #edit-quote-approved-customer").prop("checked", dataQuoteMainInfo.companySOApproved === true);
        $("#edit-quote-form #edit-quote-as9120").prop("checked", dataQuoteMainInfo.aS9120 === true);
        $("#edit-quote-form #edit-quote-is-important").prop("checked", dataQuoteMainInfo.isImportant === true);
        $("#edit-quote-form #Freight").val(dataQuoteMainInfo.freight);
        $("#edit-quote-form #Freight").trigger("focusout");
        $("#edit-quote-form #edit-quote-notes-to-customer").val(dataQuoteMainInfo.notes).trigger('change');
        $("#edit-quote-form #edit-quote-internal-notes").val(dataQuoteMainInfo.instructions).trigger('change');

        $("#edit-quote-form").find('input[name="DateOrdered"]').datepicker2('setToDay');

        const dateQuoted = dataQuoteMainInfo.dateQuoted;

        if (dateQuoted) {
            const date = new Date(dateQuoted);
            const formatted =
                date.getDate().toString().padStart(2, '0') + '/' +
                (date.getMonth() + 1).toString().padStart(2, '0') + '/' +
                date.getFullYear();

            $("#edit-quote-form #DateQuoted").val(formatted);
        }

        const currentDialog = $("#edit-quote-dialog").dialog("instance");
        currentDialog.setLoading(false);
    }
    else {
        $("#edit-quote-dialog").dialog("close");
    }
}

function focusEdit() {
    $("#contact-dropdown").trigger("focus");
}

async function populateSearchSelect() {
    if (dataQuoteMainInfo.supportTeamMemberNo != null && dataQuoteMainInfo.supportTeamMemberName != null) {
        editQuoteSupportTeamMemberSearchSelect.selectItem({
            value: dataQuoteMainInfo.supportTeamMemberNo,
            label: dataQuoteMainInfo.supportTeamMemberName
        });
    }
}

function focusError() {
    const $form = $("#edit-quote-form");
    let $firstErrorField = $form.find(".is-invalid").first();

    if ($firstErrorField.is(":hidden")) {
        const fieldId = $firstErrorField.attr("id");
        const $visibleField = $form.find(`[data-input-value-id='${fieldId}']`);
        
        if ($visibleField.length) {
            $firstErrorField = $visibleField;
        }
    }

    if ($firstErrorField.length > 0) {
        $firstErrorField.trigger("focus");
    }
}