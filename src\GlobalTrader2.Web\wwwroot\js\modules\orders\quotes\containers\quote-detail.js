﻿import { SectionBox } from "../../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { ORIGINAL_OFFER_EDIT_FILTER_INPUTS } from "../constants/original-offer-edit-filter-inputs.constants.js?v=#{BuildVersion}#";
import { ButtonHelper } from '../../../../helper/button-helper.js?v=#{BuildVersion}#';
import { openPopup } from "../../../../helper/url-helper.js?v=#{BuildVersion}#";
import { IhsHelper } from "../../../../helper/ihs-helper.js?v=#{BuildVersion}#";
import { ProductHelper } from "../../../../helper/product-helper.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../../helper/rohs-helper.js?v=#{BuildVersion}#";
import { loadCSS } from '../../../../helper/load-css.helper.js?v=#{BuildVersion}#';

const urlOpenQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-open`;
const urlClosedQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-closed`;
const urlAllQuoteLine = `/orders/quotes/${stateValue.quoteId}/quote-line-all`;
const quoteLineUrls = Object.freeze({
    0: urlOpenQuoteLine,
    1: urlClosedQuoteLine,
    2: urlAllQuoteLine
});

const NO_ROW_SELECTED = -1;

const state = {
    quoteLineSectionBox: null,
    originalOfferEditDialog: null,
    quoteLinesResultsTable: null,
    quoteMainSectionBox: null,
    quoteInfo: null,
    quoteLineUrl: urlOpenQuoteLine,
    linesTable: null,
    currentRowIndex: NO_ROW_SELECTED
};

const productSource = $('#productSource');
const as6081Warning = $('#as6081-warning-section');
const as6081Text = $('#as6081');
const as9120Warning = $('#as9120-warning-section');
const as9120Text = $('#as9120');
const serviceNo = $('#service');
const description = $('#description');
const displayClass = 'd-none';

$(async () => {
    setupQuoteResultBox();

    await loadQuoteLineResultsAsync(false, urlOpenQuoteLine);
    
    await getQuoteMainInfoAndBinding();

    initDialogs();

    setupEventListener();

    await initFilter();
})

function setupQuoteResultBox() {
    state.quoteMainSectionBox = new SectionBox('#quote-main-information-box');
    state.quoteMainSectionBox.on('onRefreshed.msb', async () => {
        await loadQuoteMainInfoAsync(true);
    });

    setupQuoteLinesResultsTable();
    state.quoteLineSectionBox = new SectionBox('#quote-lines-box');
    state.quoteLineSectionBox.on('onRefreshed.msb', async () => {
        await loadQuoteLineResultsAsync(true, state.quoteLineUrl);
        await loadQuoteLineDataDetailAsync(state.quoteLinesResultsTable, state.currentRowIndex, onSelectRowData);
        restoreAndScrollToQuoteLineResultRow();
    });

    state.quoteMainSectionBox.init();
    state.quoteLineSectionBox.init();

    loadCSS('/js/helper/dynamic-css/html-helper.css');
}



function setupQuoteLinesResultsTable(){
    if (state.quoteLinesResultsTable) {
        return;
    }

    state.quoteLinesResultsTable = new DataTable('#quote-lines-results-table', {
        paging: false,
        searching: false,
        ordering: false,
        autoWidth: false,
        scrollCollapse: true,
        loading: true,
        info: false,
        select: {
            toggleable: true,
            info: false,
        },
        language: {
            emptyTable: localizedTitles.quoteLinesResultsEmptyMsg,
            zeroRecords: localizedTitles.quoteLinesResultsEmptyMsg
        },
        rowCallback: function (row, data) {
            if (data.closed === true) {
                $(row).addClass('text-light-gray');
            }
        },
        columns: [
            {
                ...createDataTableDefaultColumns('part', 'Part No', 'Customer Part'),
                width: "14%",
                render: function(_data, _type, row) {
                    return GlobalTrader.DataTablesHelper.createStackedCell([
                        GlobalTrader.StringHelper.getPartNoAndRohsStatus(row.part, row.rohs),
                        row?.customerPart
                    ]);
                }
            },
            {
                ...createDataTableDefaultColumns('manufacturerCode', 'Mfr', 'DC'),
                width: "14%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(ButtonHelper.nubButton_Manufacturer(row.manufacturerNo, row.manufacturerCode, row.manufacturerAdvisoryNotes))} <br> 
                            ${GlobalTrader.StringHelper.setCleanTextValue(row.dateCode)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('productDescription', 'Product', 'Package'),
                width: "14%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.productDescription)} <br> 
                            ${GlobalTrader.StringHelper.setCleanTextValue(row.packageName)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('quantity', 'Quantity'),
                type: 'string',
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('price', 'Unit Price'),
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('total', 'Total'),
                width: "14%"
            },
            {
                ...createDataTableDefaultColumns('aS6081', 'Inhouse AS6081 testing required'),
                render: function (_data, _type, row) {
                    return `${row.aS6081 ? 'Yes' : 'No'}`;
                }
            },
        ]
    });
}

async function loadQuoteLineResultsAsync(isRefreshed, url) {
    state.quoteLineSectionBox.loading(true);
    toggleSourcingResultsResizeBar(false);
    if (!isRefreshed) state.quoteLinesResultsTable.rows().deselect();
    let quoteLines = await GlobalTrader.ApiClient.getAsync(url);
    state.quoteLinesResultsTable.clear().rows().remove().draw();
    state.quoteLinesResults = quoteLines.data;
    loadDataToSummaryField(quoteLines.data.freight, quoteLines.data.total);

    if (quoteLines.data.line.length > 0) {
        toggleSourcingResultsResizeBar(true);
        for (let quoteLine of quoteLines.data.line) {
            state.quoteLinesResultsTable.row.add(quoteLine).draw();
        }
        state.quoteLineSectionBox.stopLoading(false);
        setQuoteLinesResultsTableHeight(null, isRefreshed);
    }else {
        toggleSourcingResultsResizeBar(true);
        state.quoteLineSectionBox.stopLoading(false);
        setQuoteLinesResultsTableHeight(95, isRefreshed);
    }
}

function toggleSourcingResultsResizeBar(isVisible) {
    if (isVisible) {
        $("#quote-lines-results-resize").removeClass("d-none");
    } else {
        $("#quote-lines-results-resize").addClass("d-none");
    }
}

function setQuoteLinesResultsTableHeight(height = null, isRefreshed = false) {
    const $container = $("#quote-lines-results-table-container");

    if (height !== null) {
        $container.css("height", `${height}px`);
        return;
    }

    const currentHeight = $container.outerHeight(true);
    const requiredHeight = calculateFirstXRowsHeight("quote-lines-results-table", 10);

    $container.css("height", `${!isRefreshed ? requiredHeight : Math.max(requiredHeight, currentHeight)}px`);
}


function restoreAndScrollToQuoteLineResultRow() {
    const datatable = $("#quote-lines-results-table").DataTable();
    const container = $("#quote-lines-results-table-container");
    const selectedRow = datatable.row({ selected: true }).data();
    if (!selectedRow) return;

    GlobalTrader.Helper.scrollToRowContainer(container, datatable, selectedRow.id);
}
function updateCalculatorButtonState() {
    const selectedIds = state.quoteLinesResultsTable.rows({ selected: true }).data();
    const calculatorBtn = $('#quote-lines-calculator-btn');
    const tabId = Number($('button[data-bs-toggle="tab"].active').data('tabid'));

    if (selectedIds.length !== 1 || tabId === 1 || selectedIds[0]?.closed) {
        calculatorBtn.prop('disabled', true);
    } else {
        calculatorBtn.prop('disabled', false);
    }
}

function setupEventListener() {
    
    updateCalculatorButtonState();

    state.quoteLinesResultsTable.on('select deselect', function () {
        updateCalculatorButtonState();
    });

    $('#quote-lines-calculator-btn').button().on('click', () => {
        const selectedIds = state.quoteLinesResultsTable.rows({ selected: true }).data();
        const quantity = selectedIds[0]?.quantity ?? "";
        const buyPrice = selectedIds[0]?.originalOfferPrice ?? "";  
        const freight = state.quoteLinesResults.cFreight       
        
        const params = new URLSearchParams({
            quotecalc: "CalcQoute",
            cqyt: quantity,
            cbuyPrice: buyPrice,
            cduty: "",
            cFreight: freight,
            cMarginReq: ""
        })

        const url = `/orders/quotes/salescalculator?${params.toString()}`;

        openPopup(url, "sales-calculator", 400);
    })

    $('#lines-details-toggle-btn').button().on('click', () => onToggleShowLineDetails());

    $('#original-offer-edit-btn').button().on('click', () => state.originalOfferEditDialog.dialog("open"));

    registerQuoteLineItemDetailEvent();
    registerQuoteLineNavigationEvents();
    registerTabChangeEvent();

    $("#quote-main-info-view-tree-btn").button().on("click", () => {
        openPopup(ButtonHelper.URL_All_Document(state.quoteInfo.quoteId, "Q", state.quoteInfo.quoteNumber), "winTreeView", 450)
    });
}

function loadDataToSummaryField(estimatedFreight, total) {
    $('#lines-details-estimated-freight').text(`${estimatedFreight}`);
    $('#lines-details-total').text(`${total}`);
}

function createDataTableDefaultColumns(name, ...title) {
    return {
        title: renderTitle(...title),
        className: 'text-wrap align-top text-break header-custom',
        data: name,
        name: name
    }
}

function renderTitle(...title) {
    if (title.length < 1)
        return '';
    if (title.length == 1)
        return title[0];
    return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
}

function initDialogs() {
    state.originalOfferEditDialog = $("#original-offer-edit-dialog").dialog({
        autoOpen: false,
        height: "auto",
        width: '55vw',
        modal: true,
        maxHeight: $(window).height(),
        draggable: false,
        buttons: [
            {
                text: 'Save',
                class: 'btn btn-primary fw-normal',
                html: `<i class="fa-solid fa-check"></i>${window.localizedStrings.save}`,
                click: function () {
                    $(this).dialog("close");
                }
            },
            {
                text: 'Cancel',
                class: 'btn btn-primary fw-normal',
                html: `<i class="fa-solid fa-ban"></i>${window.localizedStrings.cancel}`,
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        open: function (event, ui) {
            $(this).removeClass(displayClass);
            $('.ui-dialog-titlebar-close').css('display', 'none');
        },
        close: function (event, ui) {
            $(this).dialog("close");
        },
    });
}

async function initFilter() {
    const filter = new TableFilterComponent('#original-offer-edit-filter', 'Edit Original Offer', {
        inputConfigs: ORIGINAL_OFFER_EDIT_FILTER_INPUTS,
        showButtons: false,
        wrapperClass: 'bg-none m-0 p-0'
    });

    await filter.init();
}

function onSelectRowData(data, index) {
    loadDataToQuoteLineDetailSection(data, index);
    showQuoteLineDetailSection(true);
}

//show line Detail section
function showQuoteLineDetailSection(isShow) {
    $('#order-quotes-details-line-details-wrapper').toggleClass(displayClass, !isShow);
}

async function loadDataToQuoteLineDetailSection(data, index) {
    const lineServiceWithoutFields = [
        '#partNo',
        '#rohs',
        '#custPartNo',
        '#manufacturer',
        '#dateCode',
        '#eta',
        '#insToWarehouse'
    ];
    clearWarningMessages(as6081Warning, as9120Warning);

    const hasService = data.data.serviceNo != null;
    lineServiceWithoutFields.forEach(selector =>
        $(selector).toggleClass(displayClass, hasService === true)
    );
    serviceNo.toggleClass(displayClass, hasService === false);
    description.toggleClass(displayClass, hasService === false);

    $('#oqdLineDetail').text(`Line ${index + 1} of ${state.quoteLinesResultsTable.data().length}`);

    // load data in here
    $('#order-quotes-details-line-details-wrapper span[data-dto-name]').each(function() {
        const $span = $(this);
        const fieldName = $(this).attr('data-dto-name')?.trim();
        const renderedValue = renderQuoteLineFieldValue(fieldName, data.data);
        $span.html(renderedValue);
    });

    productSource.toggleClass(displayClass, !(data.data.lineContainSource && state.quoteInfo.aS9120));

    if (data.data.aS6081){
        const response = await getAS6081MessageAsync('ReceiveSOBanner');
        showWarningMessages(as6081Warning, as6081Text, response.data);
    }

    if(state.quoteInfo.aS9120 && !data.data.lineContainSource){
        showWarningMessages(as9120Warning, as9120Text, localizedTitles.aS9120Warning);
    }
}

async function getAS6081MessageAsync(operationType) {
    const param = new URLSearchParams({operationType: operationType});
    return await GlobalTrader.ApiClient.getAsync(`/as6081/alert-message?${param}`);
}

function onToggleShowLineDetails() {
    const $container = $('#order-quotes-details-line-details');
    const $btnIcon = $('#lines-details-toggle-btn img');
    const isVisible = !$container.hasClass(displayClass);

    if (isVisible) {
        $container.addClass(displayClass);
        $btnIcon.attr('src', '/img/icons/plus.svg');
        $btnIcon.attr('alt', 'Add icon');
    } else {
        $container.removeClass(displayClass);
        $btnIcon.attr('src', '/img/icons/minus.svg');
        $btnIcon.attr('alt', 'Remove icon');
    }
}

async function getQuoteMainInfoAndBinding() {
    state.quoteMainSectionBox.loading(true);
    let response = await GlobalTrader.ApiClient.getAsync(
        `orders/quotes/${quoteMainInfoStateValue.id}/main-info`
    )
    const data = response.data;
    state.quoteInfo = response.data;
    setButtonState("#quote-main-info-view-tree-btn", true);
    stateValue.quoteMainInfo = data;
    updateQuoteMainInformation(data);
    $("#quote-main-information-button-group").removeClass(displayClass).removeAttr('style');
    $("#quote-main-info-wrapper").removeClass(displayClass).removeAttr('style');
    state.quoteMainSectionBox.stopLoading(false);
}

function updateQuoteMainInformation(data) {
    const boxSelector = '#quote-main-information-box #quote-main-information-content';
    const setText = (name, value, allowHtml = false) => {
        const sanitized = GlobalTrader.StringHelper.setCleanTextValue(value);

        const $el = $(`${boxSelector} span[name="${name}"]`);
        if (allowHtml) $el.html(sanitized);
        else $el.text(sanitized);
    };
    const setCheckbox = (name, checked) => {
        $(`${boxSelector} input[name="${name}"]`).prop('checked', !!checked);
    };
    const setVisibility = (name, show) => {
        const $el = $(`${boxSelector} input[name="${name}"]`);
        if (show) {
            $el.removeClass(displayClass).addClass('d-flex');
        }
        else {
            $el.addClass(displayClass).removeClass('d-flex');
        }
    };


    const fields = {
        customer: state.quoteInfo.companyName,
        buyer: state.quoteInfo.contactName,
        salesperson: state.quoteInfo.salesmanName,
        'division-sales': state.quoteInfo.divisionName,
        'division-header': state.quoteInfo.divisionHeaderName,
        'date-quoted': state.quoteInfo.dateQuotedStr,
        terms: state.quoteInfo.termsName,
        incoterms: state.quoteInfo.incotermName,
        'support-team-member-to-update': state.quoteInfo.supportTeamMemberName,
        currency: state.quoteInfo.currencyName,
        'estimated-freight': state.quoteInfo.freightStr,
        'notes-to-customer': state.quoteInfo.notes,
        'internal-notes': state.quoteInfo.instructions,
        'purchasing-notes': state.quoteInfo.purchasingNotes,
        status: state.quoteInfo.quoteStatusName,
        'inhouse-AS6081-part-included': state.quoteInfo.aS6081,
    };
    for (const [name, value] of Object.entries(fields)) {
        setText(name, value);
    }

    setCheckbox("approved-customer", state.quoteInfo.companySOApproved);
    setCheckbox("source-of-supply-required", state.quoteInfo.aS9120);
    setCheckbox("mark-as-important", state.quoteInfo.isImportant);

    let $customerElement = $('#customer-link');
    $customerElement.attr('href', `/Contact/AllCompanies/Details?cm=${state.quoteInfo.companyNo}`);

    $('#customer-onstop-icon').html(
        state.quoteInfo.companyOnStop ? ButtonHelper.createOnStopIcon() : ""
    );
    $('#customer-advisory-icon').html(
        state.quoteInfo.companyAdvisoryNotes?.length > 0
            ? ButtonHelper.createAdvisoryNotesIcon(state.quoteInfo.companyAdvisoryNotes)
            : ""
    );

    let $buyerElement = $('#buyer-link');
    $buyerElement.attr('href', `/Contact/Contacts/Details?con=${state.quoteInfo.contactNo}`);
    $buyerElement.html(`<span>${state.quoteInfo.contactName || ''}</span>`)

    if (state.quoteInfo.uploadedBy) {
        setVisibility("is-from-pr-offer", true);
        setCheckbox("is-from-pr-offer", state.quoteInfo.isFromProspectiveOffer);
        $(`${boxSelector} span[name="uploaded-by"]`).show().text(state.quoteInfo.uploadedBy);
    }
    const isAs6081 = state.quoteInfo.aS6081 === true;

    $(`${boxSelector} span[name="inhouse-AS6081-part-included"]`).text(isAs6081 ? window.localizedStrings.yes : window.localizedStrings.no);
    const as6081ParentSpan = $(`${boxSelector} span[name="inhouse-AS6081-part-included"]`);
    as6081ParentSpan.empty();

    if (isAs6081) {
        as6081ParentSpan.append(`<span class="highlighted-as6081">${window.localizedStrings.yes}</span>`);
        as6081ParentSpan.find('.highlighted-as6081').css('background-color', 'yellow');
    } else {
        as6081ParentSpan.append(`<span class="highlighted-as6081">${window.localizedStrings.no}</span>`);
        as6081ParentSpan.find('.highlighted-as6081').css('background-color', '');
    }


    if (state.quoteInfo.purchasingNotes) {
        const parentSpan = $(`${boxSelector} span[name="purchasing-notes"]`);

        parentSpan.empty();

        parentSpan.append(`<span class="highlighted-notes">${state.quoteInfo.purchasingNotes}</span>`);

        parentSpan.find('.highlighted-notes').css('background-color', 'yellow');
    }

    if (state.quoteInfo.aS9120 === true && state.quoteLinesResults.allLineContainSource === false) {
        $('#quote-main-info-warning-section').removeClass(displayClass);
    } else {
        $('#quote-main-info-warning-section').addClass(displayClass);
    }

    // Last updated
    $(`${boxSelector} i[name="last-updated"]`).text(`${localizedTitles.lastUpdated}: ${state.quoteInfo.lastUpdated}`);
}

async function loadQuoteMainInfoAsync(isRefreshed = false) {

    state.quoteMainSectionBox.loading(true);
    let response = await GlobalTrader.ApiClient.getAsync(
        `orders/quotes/${quoteMainInfoStateValue.id}/main-info`
    );
    const data = response.data;
    state.quoteInfo = response.data;
    stateValue.quoteMainInfo = data;
    updateQuoteMainInformation(data);
    state.quoteMainSectionBox.stopLoading(false);
}

function registerTabChangeEvent(){
    $('button[data-bs-toggle="tab"]').on('shown.bs.tab', async function (e) {
        const tabId = Number($(e.target).data('tabid'));
        const url = quoteLineUrls[tabId];      
        if (url) {
            await loadQuoteLineResultsAsync(true, url);
            state.quoteLineUrl = url;
            showQuoteLineDetailSection(false);
            clearWarningMessages(as6081Warning, as9120Warning);
            state.currentRowIndex = NO_ROW_SELECTED;
        }
        const calculatorBtn = $('#quote-lines-calculator-btn');
        calculatorBtn.prop('disabled', true);
    });
}

function registerQuoteLineItemDetailEvent(){
    $('#quote-lines-results-table').off('click').on('click', 'tr', async function() {
        const row = state.quoteLinesResultsTable.row(this);
        const data = row.data();
        const index = row.index();
        const quoteLineId = data.quoteLineId;
        state.currentRowIndex = index;

        const response = await GlobalTrader.ApiClient.getAsync(`/orders/quotes/quote-line/${quoteLineId}`);
        const selectedRows = state.quoteLinesResultsTable.rows({ selected: true }).data().toArray();
        selectedRows.length === 1 ? onSelectRowData(response, index) : showQuoteLineDetailSection(false); 
    });
}


function registerQuoteLineNavigationEvents(){
    $('#previous-button').on('click', async function() {
        const table = state.quoteLinesResultsTable;
        if (state.currentRowIndex > 0) {
            state.currentRowIndex--;
            await loadQuoteLineDataDetailAsync(table, state.currentRowIndex, onSelectRowData)
        }
    });
    $('#next-button').on('click', async function() {
        const table = state.quoteLinesResultsTable;
        const rowCount = table.data().count();
        if (state.currentRowIndex < rowCount - 1) {
            state.currentRowIndex++;
            await loadQuoteLineDataDetailAsync(table, state.currentRowIndex, onSelectRowData)
        }
    });
}

async function loadQuoteLineDataDetailAsync(table, rowIndex, onRowSelected){
    const row = table.row(rowIndex);
    const data = row.data();
    if(data){
        const response = await GlobalTrader.ApiClient.getAsync(`/orders/quotes/quote-line/${data.quoteLineId}`);
        onRowSelected(response, state.currentRowIndex);
        table.rows().deselect();
        table.row(state.currentRowIndex).select();
    }
}


function renderQuoteLineFieldValue(fieldName, data) {
    const fieldRenderers = {
        part: function(data){
            if(data.serviceNo != null){
                return GlobalTrader.StringHelper.setCleanTextValue(
                    ButtonHelper.nubButton_Service(
                        data.serviceNo,
                        data.part
                    )
                );
            }
            if (data.stockAvailableDetail != null && data.stockAvailableDetail != '') {
                const stockDetailArray = data.stockAvailableDetail.split('-');
                const QuantityInStock = stockDetailArray[0];
                const QuantityOnOrder = stockDetailArray[1];
                const QuantityAllocated = stockDetailArray[2];
                const QuantityAvailable = stockDetailArray[3];

                return GlobalTrader.HtmlHelper.showStockAvailableNew(data.part,
                        QuantityInStock, QuantityOnOrder, QuantityAllocated, QuantityAvailable, "");
            }else{
                return GlobalTrader.StringHelper.setCleanTextValue(data.part);
            }      
        },
        rohs: function(data){
            return ROHSHelper.createRohsHtml(data.rohs);
        },
        manufacturerName: function(data) {
            return GlobalTrader.StringHelper.setCleanTextValue(
                ButtonHelper.nubButton_Manufacturer(
                    data.manufacturerNo,
                    data.manufacturerName,
                    data.mfrAdvisoryNotes
                )
            );
        },
        eccnCode: function(data){
            return GlobalTrader.StringHelper.setCleanTextValue(
                IhsHelper.showIHSECCNCodeDefi(data.eccnCode, data.ihseccnCodeDefination)
            );
        },      
        aS6081: function(data){
            return data.aS6081
                ? '<span style="background-color: yellow;">Yes</span>'
                : 'No';                          
        },
        productDescription: function(data){
            return ProductHelper.showProductWarningIndividual(
                GlobalTrader.StringHelper.setCleanTextValue(data.productDescription),
                GlobalTrader.StringHelper.setSingleLineBreakValue(data.hazardousMessage),
                GlobalTrader.StringHelper.setSingleLineBreakValue(data.ipoMessage),
                GlobalTrader.StringHelper.setSingleLineBreakValue(data.restrictedMessage)
        )}
    };

    if (fieldRenderers[fieldName]) {
        return fieldRenderers[fieldName](data);
    }
    return data[fieldName] ?? '';
}

function clearWarningMessages(...selectors){
    selectors.forEach(sel => $(sel).addClass('d-none'));
}

function showWarningMessages(section, selector, message, display = 'd-none'){
    section.removeClass(display);
    selector.text(message);
}


