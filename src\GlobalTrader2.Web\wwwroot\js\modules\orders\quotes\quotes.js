﻿import { FilterTableSectionBox } from "../../../components/base/filter-table-section-box.component.js?v=#{BuildVersion}#";
import { LiteDatatable } from "../../../components/base/lite-datatable.component.js?v=#{BuildVersion}#";
import { SectionBox } from "../../../components/base/section-box.component.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { DataListNuggetType } from "../../../config/data-list-nugget-type-config.js?v=#{BuildVersion}#";
import { ORDER_QUOTES_FILTER_INPUTS } from "./constants/order-quotes-filter-inputs.constants.js?v=#{BuildVersion}#";
import { ROHSHelper } from "../../../helper/rohs-helper.js?v=#{BuildVersion}#";

const state = {
    tabId: 0, // default currentTab
    tabDic: {
        0: "my-tab",
        1: "team-tab",
        2: "division-tab",
        3: "company-tab",
    },
    orderQuotesTable: null,
    filter: null,
    sectionBox: null,
    filterSectionBox: null,
    filterStateType: DataListNuggetType.Quotes,
}

const quoteStatusMap = {
    'Offered': 1,
    'Partially Offered': 6
};

$(async () => {
    await onLoaded();
    await initFilterTableSectionBoxAsync();
    setupEventListener();
    // Note: Add for demo
    $($("#order-quotes-nav-tabs-wrapper button")[0]).click();
})

async function onLoaded() {
    state.preferences = await getPreferences();
}

function setupEventListener() {
    $(document).on('click', '#order-quotes-nav-tabs-wrapper button', async (e, data) => {
        const ignoreSave = data?.notResetPageRequired
        setTimeout(function () {
            openningTab($(e.target).attr("tabId"), ignoreSave, !data?.notResetPageRequired);
        }, 100);
    })
}

async function openningTab(tabId, ignoreSave, resetPageRequired) {
    // this section for example & testing
    //params for testing
    state.tabId = tabId;
    const data = state.filter.getAppliedValues();
    const convertedData = prepareDataBeforeReload(data)
    if (resetPageRequired) {
        state.orderQuotesTable.resetPage();
    }
    convertedData._ignoredSave = ignoreSave;
    await state.orderQuotesTable.reloadAsync(convertedData);
}

async function initFilterTableSectionBoxAsync() {
    // init Datatable
    state.orderQuotesTable = new LiteDatatable('#orderQuotesTbl', {
        serverSide: true,
        dataSrc: 'data',
        ajax: {
            url: '/api/orders/quotes/quote-list', // API URL here
            type: 'POST',
        },
        ordering: true,
        autoWidth: false,
        paging: true,
        pageConfig: {
            pageSize: state.preferences.defaultListPageSize
        },
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
            infoFiltered: "",
            lengthMenu: "_MENU_ per page",
            loadingRecords: "",
        },
        processing: true,
        order: [[1, "desc"]],
        // Define columns here
        columns: [
            {
                data: "quoteId",
                visible: false,
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.no),
                className: "text-break align-baseline position-relative text-wrap text-break",
                orderSequence: ["desc", "asc"],
                width: "8%",
                render: function (_data, _type, row) {
                    return `<span><a class="dt-hyper-link" href="/Orders/Quotes/details?qt=${row.quoteId}">${row.quoteNumber}</a></span>`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.partNo),
                className: "text-break align-baseline position-relative text-wrap text-break",
                orderSequence: ["desc", "asc"],
                width: "10%",
                render: function (_data, _type, row) {
                    return `${ROHSHelper.writePartNo(row.part, row.rohs)}`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.unitPrice),
                width: "7%",
                className: "text-break align-baseline position-relative",
                orderSequence: ["desc", "asc"],
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(formatNumberString(row.price))}`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.quantity),
                className: "text-break align-baseline position-relative",
                orderSequence: ["desc", "asc"],
                width: "5%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.quantity)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('companyName', localizedQuoteTitile.company, localizedQuoteTitile.contact),
                className: "text-wrap text-break",
                orderSequence: ["desc", "asc"],
                width: "14%",
                render: function (_data, _type, row) {
                    return `${handleCompanyNameLink(row)} ${row.contactName ? $('<a>').attr('href', GlobalTrader.PageUrlHelper.Get_URL_Contact(row.contactNo)).addClass('dt-hyper-link').text(row.contactName).prop('outerHTML') + '<br>' : '<br>'}`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.quoted),
                className: "text-break align-baseline position-relative",
                orderSequence: ["desc", "asc"],
                width: "5%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.dateQuoted)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('quoteOfferedDate', localizedQuoteTitile.quoteOfferdDate),
                orderSequence: ["desc", "asc"],
                width: "10%",
                render: function (_data, _type, row) {
                    const linkHtml = row.quoteOfferedDate
                        ? $('<a>')
                            .text(row.quoteOfferedDate)
                            .prop('outerHTML')
                        : '';
                    
                    const spanHtml = `<span style="background-color: ${getColorRequiredDateStatus(row.dateOfferStatus)} !important; float: right; margin-top: -17px; height: 20px; width: 20px;"></span>`;

                    return `${linkHtml}<br>${spanHtml}`;
                }
            },
            {
                ...createDataTableDefaultColumns('salesmanName', localizedQuoteTitile.salesPerson, localizedQuoteTitile.status),
                className: "text-wrap text-break",
                orderSequence: ["desc", "asc"],
                width: "9%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(row.salesmanName)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(row.quoteStatusName)}`;
                }
            },
            {
                ...createDataTableDefaultColumns('totalValue', localizedQuoteTitile.totalValue, localizedQuoteTitile.totalInBase),
                className: "text-wrap text-break",
                orderSequence: ["desc", "asc"],
                width: "10%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(formatNumberString(row.totalValue))} <br> ${GlobalTrader.StringHelper.setCleanTextValue(formatNumberString(row.totalInBase))}`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.profit),
                className: "text-break align-baseline position-relative text-wrap",
                orderable: false,
                width: "10%",
                render: function (_data, _type, row) {
                    return `${GlobalTrader.StringHelper.setCleanTextValue(formatNumberString(row.offerProfit))}`;
                }
            },
            {
                ...createDataTableDefaultColumns(null, localizedQuoteTitile.toDoList),
                className: "text-break align-baseline position-relative",
                orderable: false,
                width: "4%",
                render: function (_data, _type, row) {
                    let addTaskHtml = "";
                    let viewTaskHtml = "";
                    let encodedCompanyName = GlobalTrader.StringHelper.htmlAttributeEncode(row.companyName);
                    let quoteStatusCode = quoteStatusMap[row.quoteStatusName] || 0;
                    addTaskHtml = `<a class="dt-hyper-link" 
                                        data-company-name="${encodedCompanyName}" 
                                        data-category-type="${QUOTE_TASK_CATEGORY}" 
                                        data-quote-number="${row.quoteNumber}" 
                                        data-quote-id="${row.quoteId}" 
                                        data-quote-status="${quoteStatusCode}"
                                        onClick="event.stopPropagation(); openSpecificCategoryToDoDialog(event)"
                                        title='Add task'>Add Task</a>`;
                    if (row.hasUnFinishedTask) {
                        viewTaskHtml = `<a class="dt-hyper-link view-task" href="javascript:void(0);" title="Need to complete open Tasks" onclick="event.stopPropagation(); redirectToTaskDetails('${row.quoteNumber}')">${row.taskCount} Task</a>`;
                    }
                    else {
                        viewTaskHtml = `<a class="dt-hyper-link" href="javascript:void(0);" title="View task" onclick="event.stopPropagation(); redirectToTaskDetails('${row.quoteNumber}')">${row.taskCount} Task</a>`;
                    }

                    return `${GlobalTrader.StringHelper.setCleanTextValue(addTaskHtml)} <br> ${GlobalTrader.StringHelper.setCleanTextValue(viewTaskHtml)}`;
                }
            },
        ]
    });

    state.orderQuotesTable.on('preXhr.dt', function () {
        $('#orderQuotesTbl tbody tr .dt-empty').hide();

        $(`#orderQuotesTbl_wrapper thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc') // Remove neutral sorting icon
            .addClass('position-relative');

        $(`#orderQuotesTbl_wrapper thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#orderQuotesTbl_wrapper thead th .dt-column-order`).addClass('dt-column-order-custom');
    })

    // init Table Filter
    state.filter = new TableFilterComponent("#filter-section-wrapper", "Filter Results", {
        inputConfigs: ORDER_QUOTES_FILTER_INPUTS
        //templateId:'my-template' // template insert input to filter
    });

    // init SectionBox
    state.sectionBox = new SectionBox('#order-quotes-box', {
        loadingContentId: state.orderQuotesTable.getContainerId() // only table should be hidden while processing api requests
    }, {
        enableFilterButton: false
    });

    // Init Filter Table Sectionbox - Datatable & Table Filter & SectionBox
    state.filterSectionBox = new FilterTableSectionBox(state.orderQuotesTable, state.filter, state.sectionBox, {
        prepareDataBeforeReload: prepareDataBeforeReload
    });

    state.filterSectionBox.setFilterStateType(state.filterStateType);

    await state.filterSectionBox.initAsync();
}

function createDataTableDefaultColumns(name, ...title) {
    return {
        title: renderTitle(...title),
        className: 'text-wrap text-break header-custom',
        data: name,
        name: name,
    }
}

function renderTitle(...title) {
    if (title.length < 1)
        return '';
    if (title.length == 1)
        return title[0];
    return GlobalTrader.StringHelper.stringFormat(`<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">{0}</div>{1}`, ...title);
}

function getColorRequiredDateStatus(requiredDateStatus) {
    const statusColors = {
        Green: 'green',
        Yellow: '#FFBF00',
        Red: 'red',
        Amber: '#FFBF00'
    };

    return statusColors[requiredDateStatus] || '';
}

function formatNumberString(str) {
    const match = str.match(/^([\d.]+)(?:\s*(\w+))?$/);
    if (!match) return str;

    const numberPart = parseFloat(match[1]);
    const unit = match[2] || "";

    const formattedNumber = numberPart.toLocaleString('en-US', {
        minimumFractionDigits: 5,
        maximumFractionDigits: 5
    });

    return unit ? `${formattedNumber} ${unit}` : formattedNumber;
}

function handleCompanyNameLink(row) {
    if (!row.companyName) return '<br>';

    const $a = $('<a>')
        .attr('href', GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo))
        .addClass('dt-hyper-link')
        .text(row.companyName);

    if (row.blnMakeYellow) {
        $a.css('background-color', 'yellow');
    }
    return $a.prop('outerHTML') + '<br>';
}

function prepareDataBeforeReload(filterData) {
    let requestData = {
        part: !shouldInclude(filterData.Part) ? null : filterData.Part?.value,
    }
    function shouldInclude(input) {
        return input?.isOn && input.isShown;
    }
    return requestData;
}

async function getPreferences() {
    const response = await GlobalTrader.ApiClient.getAsync(`/user-account/profile/preferences`, {}, {});
    // handle error?
    return response?.data;
}

window.redirectToTaskDetails = function (quoteNumber) {
    const baseUrl = window.location.origin;
    const targetUrl = `${baseUrl}/Profile/ToDo?qn=${quoteNumber}&Category=2`;
    window.location.href = targetUrl;
};