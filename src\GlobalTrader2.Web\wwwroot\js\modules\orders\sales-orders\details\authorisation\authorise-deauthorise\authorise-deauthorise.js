﻿import { AuthorisationSectionService } from '../authorisation-services.js?v=#{BuildVersion}#';
import { AuthoriseDeauthoriseService } from './authorise-deauthorise-service.js?v=#{BuildVersion}#';
export class AuthoriseDeauthoriseManager {

    constructor({ authoriseData, successCallback }) {
        this.$dialog = $('#authorise-dialog');
        this.authoriseData = authoriseData;
        this.successCallback = successCallback;
        this.$form = $('#authorise-deauthorise-form');
        this.notifyCheckbox = $("#authorise-notify-salesperson");
        this.notifyer = null;
        this.isAuthorise = false;
        this.authorisationSectionService = AuthorisationSectionService;
        this.apiService = AuthoriseDeauthoriseService;
        this.$comment = this.$form.find('#comment');
    }
    initialize() {
        this.setUpDialog();
    }
    setUpDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "auto",
            height: "auto",
            autoOpen: false,
            draggable: false,
            modal: true,
            open: async () => {
                this.$form.validate().resetForm();
                this.$dialog.dialog("setLoading", true);
                await this.getSONotifyer();
                this.bindingData();
                this.$dialog.dialog("setLoading", false);
            },
            close: () => {
                this.$dialog.find(".form-error-summary").hide();
            },
            buttons: [
                {
                    text: window.localizedStrings.yes,
                    class: 'btn btn-primary',
                    id: 'authorise-deauthorise-btn',
                    html: `<img src="/img/icons/check.svg" alt="Yes icon"/>${window.localizedStrings.yes}`,
                    click: async () => {
                        this.$dialog.find(".form-error-summary").hide();
                        this.$dialog.dialog("setLoading", true);
                        $("#authorise-deauthorise-btn").hide();
                        $("#authorise-deauthorise-close-btn").hide();
                        let response = await this.authoriseDeauthorise();
                        this.$dialog.dialog("setLoading", false);
                        if (!response?.success) {
                            $("#authorise-deauthorise-btn").show();
                            $("#authorise-deauthorise-close-btn").show();
                            showToast("danger", response.title);
                            return;
                        }
                        $("#authorise-deauthorise-btn").show();
                        $("#authorise-deauthorise-close-btn").show();
                        showToast('success', window.localizedStrings.saveChangedMessage);
                        this.$dialog.dialog("close");
                        if (this.successCallback) this.successCallback();
                        window.location = window.location.href;
                    }
                },
                {   id: 'authorise-deauthorise-close-btn',
                    text: window.localizedStrings.no,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/xmark.svg" alt="No icon"/>${window.localizedStrings.no}`,
                    click: () => {
                        this.$comment.val('');
                        this.$dialog.dialog('close');
                    }
                }
            ],
        });
    }
    bindingData() {
        this.$form.find("span[data-field]").toArray().forEach(element => {
            const $el = $(element);
            const fieldName = $el.data('field');
            let value = this.getPropertyCaseInsensitive(this.authoriseData, fieldName);
            $el.text(GlobalTrader.StringHelper.setCleanTextValue(value));
        });
        if (!this.notifyer.isAllowCheckSoNotify) {
            this.notifyCheckbox.prop('checked', true);
        }
        else {
            this.notifyCheckbox.prop('disabled', false);
        }
        if (this.isAuthorise) {
            let title = "AUTHORISE SALES ORDER";
            let description = "";
            if (!this.authoriseData.companyOnStop) {

                description = localizedTitles.authoriseTitle;
            }
            else {
                description = localizedTitles.onCompanyStopAuthoriseTitle
            }
      
            $("#authorise-notify-salesperson-wrapper").show();
            $("#authorise-deauthorise-description").html(GlobalTrader.StringHelper.setCleanTextValue(description, true));
            $("#authorise-deauthorise-title").text(GlobalTrader.StringHelper.setCleanTextValue(title));
        }
        else {
            let title = "DE-AUTHORISE SALES ORDER";
            let description = localizedTitles.deauthoriseTitle;
            $("#authorise-notify-salesperson-wrapper").hide();
            $("#authorise-deauthorise-description").html(GlobalTrader.StringHelper.setCleanTextValue(description, true));
            $("#authorise-deauthorise-title").text(GlobalTrader.StringHelper.setCleanTextValue(title));
        }
    }
    getPropertyCaseInsensitive(obj, key) {
        key = key.toLowerCase();
        for (let prop in obj) {
            if (prop.toLowerCase() === key) {
                return obj[prop];
            }
        }
        return undefined; // not found
    }
    reloadData(data) {
        this.authoriseData = data;
    }
    setAuthorise(isAuthorise) {
        this.isAuthorise = isAuthorise;
    }
    async getSONotifyer() {
        let response = await this.authorisationSectionService.GetSoNotifyerAsync(this.authoriseData.salesOrderId);
        if (response.success) {
            this.notifyer = response.data;
        }
    }
    async authoriseDeauthorise() {
        return this.apiService.AuthoriseDeauthoriseAsync(this.authoriseData.salesOrderId, this.$form, this.isAuthorise);
    }
}