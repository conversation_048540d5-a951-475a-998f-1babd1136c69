﻿import { SalesOrderDetailApiUrl } from '../../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#';
export class RequestApprovalService {
    static #baseURL = SalesOrderDetailApiUrl;

    static async SendRequestApproval(salesOrderId,$form, mailMessage) {
        const data = GlobalTrader.FormHelper.convertFormDataToOject($form);
        const payload = {
            isNotifySO: data.notify,
            subject: data.subject,
            message: mailMessage,
            approverIds: JSON.parse(data.approverNo)
           }
      
        return GlobalTrader.ApiClient.postAsync(`${this.#baseURL}/${salesOrderId}/approval-request`,
            payload, {
            "RequestVerificationToken": $form.find('input[name="__RequestVerificationToken"]').val()
        });
    }

}