﻿import { SearchSelectComponent } from '../../../../../../components/search-select/search-select.component.js?v=#{BuildVersion}#';
import { AuthorisationSectionService } from '../authorisation-services.js?v=#{BuildVersion}#';
import { RequestApprovalService } from './request-approval-service.js?v=#{BuildVersion}#';
export class RequestApprovalManager {
    constructor(requestApprovalData, onRequestApprovalSuccess = () => { }) {
        this.$dialog = $("#request-approval-dialog");
        this.$form = $("#request-approval-form");
        this.$submitButton = $("#request-approval-submit-button");
        this.salesOrderId = null;
        this.requestApprovalData = requestApprovalData;
        this.$notifyCheckBox = $("#notify-salesperson");
        this.$approverSearch = this.$form.find("#approverSearch");
        this.approverSearch = null;
        this.notifyer = null;
        this.apiService = AuthorisationSectionService;
        this.requestApprovalService = RequestApprovalService;
        this.successCallback = onRequestApprovalSuccess;
        this.$messageSummerNote = this.$form.find('#message');

    }
    initialize() {
        this.setUpDialog();
        this.setupApproverSearch();
        this._initFormValidation();
        this.$dialog.find('input, select, textarea')
            .filter(':visible:enabled:not([readonly])')
            .first()
            .trigger("focus");
        this.$messageSummerNote.summernote2({
            maxLength: 60000,
            characterCounter: false,
        });
        this.$form.find('.note-toolbar.card-header').css({
            display: 'flex',
            'flex-wrap': 'wrap'
        });
        this.$form.on('submit', e => e.preventDefault());
    }
    setUpDialog() {
        this.$dialog.dialog({
            maxHeight: $(window).height(),
            width: "50vw",

            open: async () => {
                this.$dialog.dialog("setLoading", true);
                this.$form.validate().resetForm();

                this.approverSearch.resetSearchSelect();
                await this.getSONotifyer();
                this.loadFormData();
                this.setUpCheckbox();
                this.$dialog.dialog("setLoading", false);
            },
            close: () => {
                this.$dialog.find(".form-error-summary").hide();
            },
            buttons: [
                {   id: 'request-approval-auth-btn',
                    text: 'Send',
                    class: 'btn btn-primary fw-normal',
                    html: `<img src="/img/icons/send.svg" alt="send">Send`,
                    type: 'submit',
                    click: async () => {
                        if (!this.$form.valid()) {
                            this.$dialog.find(".form-error-summary").show();
                            return;
                        }
                       
                        this.$dialog.find(".form-error-summary").hide();
                        this.$dialog.dialog("setLoading", true);
                        $("#request-approval-auth-btn").hide();
                        $("#request-approval-auth-close-btn").hide();
                        let response = await this.sendApproval();
                        this.$dialog.dialog("setLoading", false);
                        if (!response?.success) {
                            showToast("danger", response.title);
                            $("#request-approval-auth-btn").show();
                            $("#request-approval-auth-close-btn").show();
                            return;
                        }
                        $("#request-approval-auth-btn").show();
                        $("#request-approval-auth-close-btn").show();
                        this.$dialog.dialog("close");
                        showToast('success', window.localizedStrings.saveChangedMessage);
                        if (this.successCallback) this.successCallback();
                    }
                },
                {
                    id:'request-approval-auth-close-btn',
                    text: this.cancel,
                    class: 'btn btn-danger fw-normal',
                    html: `<img src="/img/icons/slash.svg" alt="Cancel">Cancel`,
                    click: () => {
                        this.$dialog.dialog("close");
                    },
                },
            ]
        });
    }
    loadFormData() {
        this.$messageSummerNote.summernote2('code', GlobalTrader.StringHelper.setCleanTextValue(this.requestApprovalData.body) || '');

        $("#subject").val(GlobalTrader.StringHelper.setCleanTextValue(this.requestApprovalData.subject) || '');
        $("#sales-man").text(GlobalTrader.StringHelper.setCleanTextValue(this.notifyer.soNotifyersName));
        if (!this.notifyer.isAllowCheckSoNotify) {
            $('#sales-man').show();
            this.$notifyCheckBox.prop('checked', true);
            this.$notifyCheckBox.prop('disabled', true);
        }
        else {
            $('#sales-man').show();
            this.$notifyCheckBox.prop('checked', true);
            this.$notifyCheckBox.prop('disabled', false);
        }
    }
    setupApproverSearch() {
        this.approverSearch = new SearchSelectComponent(
            "approverSearch",
            "approverNo",
            "multiple",
            "keyword",
            `/powerapp-approver/${this.requestApprovalData.salesOrderId}/auto-search`,
            0
        );
    }
    setUpCheckbox() {
        this.$notifyCheckBox.on('change', async () => {
            if (this.$notifyCheckBox.is(':checked')) {
                $('#sales-man').show();
            } else {
                $('#sales-man').hide();
            }
        });
    }
    async getSONotifyer() {
        let response = await this.apiService.GetSoNotifyerAsync(this.requestApprovalData.salesOrderId);
        if (response.success) {
            this.notifyer = response.data;
        }
    }
    sendApproval() {
        let mailMessage = this.$messageSummerNote.summernote2('code');
        return this.requestApprovalService.SendRequestApproval(this.requestApprovalData.salesOrderId, this.$form, mailMessage);
    }
    _initFormValidation() {
        this.$form.validate({
            ignore: [],
            rules: {
                approverNo: {
                    required: true
                },
                subject: {
                    required: true,
                    maxlength: 256
                },
                message: {
                    required: true,
                    summernoteRequired: true,
                    noWhiteSpace: true,
                    noWhiteSpaceSummernote: true,
                    maxlength: 60000
                }
            },
            messages: {
                approverNo: 'Please enter value',
                subject: 'Please enter value',
                message: 'Please enter value',
            },
            // Focus the first invalid element
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
            highlight: function (element) {
                const inputName = $(element).attr("name");
                if (inputName === 'message') {
                    $('.note-editor').addClass('is-invalid');
                } else {
                    $(element).addClass("is-invalid");
                }
            },
            unhighlight: function (element) {
                const inputName = $(element).attr("name");
                if (inputName === 'message') {
                    $('.note-editor').removeClass('is-invalid');
                } else {
                    $(element).removeClass("is-invalid");
                }
            },
        });
    }
    reloadData(data) {
        this.requestApprovalData = data;
    }
}