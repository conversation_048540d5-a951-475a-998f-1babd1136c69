﻿import { SalesOrderDetailApiUrl } from '../../../../../../../config/api-endpoint-config.js?v=#{BuildVersion}#';
import { ROHSHelper } from '../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#';
import { ButtonHelper } from '../../../../../../../helper/button-helper.js?v=#{BuildVersion}#';
import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { ExportApprovalStatusTabConstant } from '../../../constants/export-approval-status-tab.js?v=#{BuildVersion}#';

export class ExportApprovalTabManager extends EventEmitter {
    constructor(salesOrderId, tabName) {
        super();
        this.salesOrderId = salesOrderId;
        this.dataTable = null;
        this.BaseUrl = SalesOrderDetailApiUrl;
        this.selectedSOLine = null;
        this.TabConfiguration = {
            [ExportApprovalStatusTabConstant.ALL]: {
                dataTableSelector: '#all-export-sol-table',
                apiEndpoint: `/api${this.BaseUrl}/${this.salesOrderId}/all-export-approval-so-lines`,
            },
            [ExportApprovalStatusTabConstant.APPROVED]: {
                dataTableSelector: '#approved-export-sol-table',
                apiEndpoint: `/api${this.BaseUrl}/${this.salesOrderId}/approved-export-approval-so-lines`,
            },
            [ExportApprovalStatusTabConstant.WAITING]: {
                dataTableSelector: '#waiting-export-sol-table',
                apiEndpoint: `/api${this.BaseUrl}/${this.salesOrderId}/waiting-export-approval-so-lines`,
            },
        };
        this.tabConfig = this.TabConfiguration[tabName];
    }

    initialize() {
        this._setupDataTable();
    }

    async refreshTabData() {
        await GlobalTrader.Helper.reloadResizeDatatable(this.dataTable, null);
    }

    setSelectedSOLineByIds(selectedIds) {
        const idSet = new Set(selectedIds);
        this.selectedSOLine = this.dataTable.rows((idx, data) => idSet.has(data.exportApprovalId)).data().toArray();
    }

    setSelectedSOLine(selectedExportApprovalData) {
        this.selectedSOLine = selectedExportApprovalData;
    }

    selectAllItem() {
        this.dataTable.rows().select();
    }
    deselectAllItem() {
        this.dataTable.rows().deselect();
    }

    _bindSelectedExportApprovalData(selectedExportApprovalData) {
        if (!selectedExportApprovalData)
            return;
        const selectedLineIds = new Set(selectedExportApprovalData.map(x => x.exportApprovalId)); 
        this.dataTable.rows().deselect();
        this.trigger('soLineSelected', selectedExportApprovalData);
        this.dataTable.rows((idx, data) => selectedLineIds.has(data.exportApprovalId)).select();
    }

    _setupDataTable() {
        this.dataTable = $(this.tabConfig.dataTableSelector)
            .on('preXhr.dt', () => {
                this.trigger('loadingTable', true);
            })
            .on('draw.dt', () => {
                this.trigger('loadingTable', false);
                this._bindSelectedExportApprovalData(this.selectedSOLine);
            })
            .ResizeDataTable({
                ajax: this.tabConfig.apiEndpoint,
                dataSrc: 'data',
                resizeConfig: {
                    numberOfRowToShow: 4
                },
                info: false,
                responsive: true,
                select: {
                    toggleable: true,
                    info: false,
                },
                language: {
                    emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
                    zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
                },
                paging: false,
                ordering: false,
                searching: false,
                scrollCollapse: true,
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                    $(thead).find('th').eq(0).addClass('text-start');
                },
                columns: [
                    {
                        data: 'lineId', visible: false
                    },
                    {
                        data: (row) => (
                            {
                                lineNo: row.lineNo,
                                isExportDetailsFilled : row.isExportDetailsFilled
                            }
                        ),
                        title: 'Line No',
                        width: '7%',
                        className: 'text-wrap text-break',
                        render: (data) => {
                            const imageText = data.isExportDetailsFilled === true ? `<img src='/img/icons/star-checked.svg' title='Export Approval Data filled' width="18" height="18" />` : ``;
                            return `
                                  <div class="text-center">
                                    <p class="m-0" style="min-height: 15px;">${data.lineNo}</p>
                                    <p class="m-0" style="min-height: 15px;">${imageText}</p>
                                  </div>
                                `;
                        }
                    },
                    {
                        data: (row) => (
                            {
                                part: row.part,
                                customerPart: row.customerPart,
                                rohsStatus: row.rohs
                            }
                        ),
                        name: 'partNo_customerPart',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Part No</div>Customer Part`,
                        width: '30%',
                        className: 'text-wrap text-break text-start',
                        render: (data) => {
                            let partNoText = "";
                            let customerPartText = "";
                            if (data.part) {
                                const escapedPartNoText = DataTable.render.text().display(data.part);
                                partNoText = GlobalTrader.StringHelper.setCleanTextValue(escapedPartNoText);
                            }
                            if (data.customerPart) {
                                const escapedCustomerPartText = DataTable.render.text().display(data.customerPart);
                                customerPartText = GlobalTrader.StringHelper.setCleanTextValue(escapedCustomerPartText);
                            }
                            const partNoWithRohsImage = ROHSHelper.writePartNo(partNoText, data.rohsStatus);
                            return `${partNoWithRohsImage}<div class="m-0" style="min-height: 15px;">${customerPartText}</div>`;
                        }
                    },
                    {
                        data: (row) => (
                            {
                                manufacturer: row.manufacturer,
                                manufacturerNo: row.manufacturerNo,
                                manufacturerAdvisoryNotes: row.manufacturerAdvisoryNotes,
                                dateCode: row.dateCode,
                            }
                        ),
                        name: 'manufacturer_dateCode',
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">Mfr</div>DC`,
                        width: '10%',
                        className: 'text-wrap text-break',
                        render: (data) => {
                            let dateCodeText = "";
                            let advisoryText = "";
                            if (data.dateCode) {
                                const escapeddateCodeText = DataTable.render.text().display(data.dateCode);
                                dateCodeText = GlobalTrader.StringHelper.setCleanTextValue(escapeddateCodeText);
                            }
                            if (data.manufacturerAdvisoryNotes) {
                                const escapedAdvisoryText = DataTable.render.text().display(data.manufacturerAdvisoryNotes);
                                advisoryText = GlobalTrader.StringHelper.formatAdvisoryNotes(escapedAdvisoryText); 
                            }
                            const manufaturerAdvisory = ButtonHelper.nubButton_Manufacturer(GlobalTrader.StringHelper.setCleanTextValue(data.manufacturerNo), GlobalTrader.StringHelper.setCleanTextValue(data.manufacturer), advisoryText)
                            return `<div style="min-height: 15px;">${manufaturerAdvisory}</div>
                                    <div class="m-0" style="min-height: 15px;">${dateCodeText}</div>
                                    `;
                        }
                    },
                    {
                        data: 'exportApprovalStatus',
                        title: 'Export Approval Status',
                        width: '7%',
                        className: 'text-wrap',
                    },
                    {
                        data: 'ogelLicenseRequired',
                        title: 'OGEL License Required?',
                        className: 'text-wrap',
                        width: '7%',
                    },
                    {
                        data: 'euuFormRequired',
                        title: 'EUU Form Required?',
                        className: 'text-wrap',
                        width: '7%',
                    },
                    {
                        data: 'dated',
                        title: 'Date',
                        className: 'text-wrap',
                        render: DataTable.render.text()
                    },
                    {
                        data: 'by',
                        title: 'By',
                        className: 'text-wrap text-break',
                        render: DataTable.render.text()
                    },
                    {
                        data: 'comment',
                        title: 'Comments',
                        className: 'text-wrap text-break',
                        render: DataTable.render.text()
                    },
                ],
                rowId: 'lineId',
                createdRow: (row, data) => {
                    $(row).attr('tabindex', '0');               
                }
            })
            .on('select deselect', async (e, dt, type, indexes) => {
                if (type === 'row') {
                    const selectedRow = dt.rows({ selected: true }).data().toArray();
                    this.selectedSOLine = selectedRow;
                    this.trigger('soLineSelected', selectedRow);
                }
            });
    }
}