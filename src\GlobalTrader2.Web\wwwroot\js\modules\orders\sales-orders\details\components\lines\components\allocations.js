﻿import { <PERSON>lapsibleBoxHelper } from '../../../../../../../helper/collapsible-box-helper.js?v=#{BuildVersion}#';
import { ROH<PERSON>Helper } from '../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#'
import { ButtonHelper } from '../../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
import { SystemDocument } from "../../../../../../../config/system-document-enum.js?v=#{BuildVersion}#";
import { MediaDocumentManager } from "../../../../../../../components/documents-list/media-document-list.js?v=#{BuildVersion}#";
import { DeallocationDialogManager } from "./deallocation-dialog.js?v=#{BuildVersion}#";
import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#'
export class AllocationsManager extends EventEmitter {
    constructor(salesOrderLineId, level) {
        super();

        this.salesOrderLineId = salesOrderLineId;
        this.level = level;

        this.$lineContainer = $(`#lines-${this.level}`);
        this.$allocationSectionContainer = this.$lineContainer.find('#allocations-details-container');
        this.$tableContainer = this.$lineContainer.find('#allocations-container');
        this.$refreshButton = this.$allocationSectionContainer.find('.collapsible-box-refresh-button');

        this.allocationsData = [];
        this.$allocationsTable = null;
        this.allocationsEndpoint = () => `/orders/sales-order/so-lines/${this.salesOrderLineId}/allocations`;

        this.$imageSectionContainer = this.$lineContainer.find('#allocation-images-container');
        this.allocationImagesManager = null;
        this.$imageRefreshButton = this.$imageSectionContainer.find('#allocation-images-refresh-button');

        this.$deallocateButton = this.$allocationSectionContainer.find('#deallocate-btn');
        this.deallocationDialogManager = new DeallocationDialogManager(this.level);
        this.deallocationDialogManager.initialize();
    }

    async initialize() {
        this._setupButtonBehaviours();
        this._setupDeallocateSuccessEvent();
        await this._initTable();
    }

    async _initTable() {
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$allocationSectionContainer, true);
        await this.getAllocationsAsync();
        this.$tableContainer.show();
        this.$allocationsTable = this.$lineContainer.find('#allocations-table')
            .DataTable({
                data: this.allocationsData ?? [],
                info: false,
                scrollCollapse: true,
                responsive: true,
                searching: false,
                paging: false,
                ordering: false,
                lengthChange: false,
                select: {
                    toggleable: true,
                    info: false,
                },
                language: {
                    emptyTable: `<i>${allocationsLocalizer.noDataFound}</i>`,
                    zeroRecords: `<i>${allocationsLocalizer.noDataFound}</i>`,
                },
                headerCallback: (thead) => {
                    $(thead).find("th").addClass('align-baseline');
                },
                columnDefs: [
                    { type: 'string', targets: '_all' }
                ],
                columns: [
                    {
                        className: 'text-wrap text-break',
                        title:
                            `
                                <span class="dt-column-title d-flex flex-column gap-1">
                                    <div>${allocationsLocalizer.partNo}</div>
                                    <div class="dt-column-line"></div>
                                    <div>${allocationsLocalizer.supplierPart}</div>
                                </span>
                            `,
                        data: 'part',
                        render: (data, type, row) => {
                            const part = ROHSHelper.writePartNo(data, row.rohs);
                            return `
                            <a href="${GlobalTrader.PageUrlHelper.Get_URL_Stock(row.stockNo)}" class="dt-hyper-link" style="min-height: 15px;">${part}</a>
                            <p class="m-0" style="min-height: 15px;">${GlobalTrader.StringHelper.setCleanTextValue(row.supplierPart)}</p>
                            `;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title: allocationsLocalizer.supplier,
                        data: 'ipoSupplier',
                        render: (data, type, row) => {
                            let companyInfo = {
                                href: '',
                                name: '',
                                advisoryNoteHtml: GlobalTrader.StringHelper.isNullOrWhitespace(row.companyAdvisoryNotes)
                                    ? ""
                                    : GlobalTrader.HtmlHelper.createAdvisoryHtml(
                                    {
                                        advisory: GlobalTrader.StringHelper.formatAdvisoryNotes(row.companyAdvisoryNotes),
                                        options: {
                                            class: "ms-1",
                                            style: "margin-top: -3px"
                                        }
                                    }
                                )
                            };

                            if (row.ipoSupplier > 0) {
                                companyInfo.href = GlobalTrader.PageUrlHelper.Get_URL_Company(row.ipoSupplier)
                                companyInfo.name = row.ipoSupplierName;
                            } else {
                                companyInfo.href = GlobalTrader.PageUrlHelper.Get_URL_Company(row.companyNo);
                                companyInfo.name = row.companyName;
                            }

                            return `
                                <div class="m-0" style="min-height: 15px;">
                                    <a class="dt-hyper-link" href="${companyInfo.href}">
                                        ${DataTable.render.text().display(GlobalTrader.StringHelper.setCleanTextValue(companyInfo.name))} 
                                    </a> 
                                    <span>${companyInfo.advisoryNoteHtml}</span>
                                </div>
                                <div class="d-flex justify-content-between" style="min-height: 15px;">
                                    <span></span>
                                    <span></span>
                                </div>
                            `;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title:
                            `
                                <span class="dt-column-title d-flex flex-column gap-1">
                                    <div>${allocationsLocalizer.qtyInStock}</div>
                                    <div class="dt-column-line"></div>
                                    <div>${allocationsLocalizer.qtyOnOrder}</div>
                                </span>
                            `,
                        width: '142px',
                        data: (row) => (
                            {
                                quantityInStock: row.quantityInStock,
                                quantityOnOrder: row.quantityOnOrder,
                            }
                        ),
                        render: (data, type, row) => {
                            return `<p class="m-0" style="min-height: 15px;">${data.quantityInStock}</p><p class="m-0" style="min-height: 15px;">${data.quantityOnOrder}</p>`;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title:
                            `
                                <span class="dt-column-title d-flex flex-column gap-1">
                                    <div>${allocationsLocalizer.poLineNo}</div>
                                    <div class="dt-column-line"></div>
                                    <div>${allocationsLocalizer.expediteDate}</div>
                                </span>
                            `,
                        width: '140px',
                        data: (row) => (
                            {
                                internalPurchaseOrderId: row.internalPurchaseOrderId,
                                internalPurchaseOrderNumber: row.internalPurchaseOrderNumber,
                                purchaseOrderNo: row.purchaseOrderNo,
                                purchaseOrderNumber: row.purchaseOrderNumber,
                                lineNo: row.poSerialNo,
                                expediteDate: row.expediteDate,
                            }
                        ),
                        render: (data, type, row) => {
                            const getPo = () => data.purchaseOrderNo > 0
                                ? ButtonHelper.nubButton_SystemDocument(SystemDocument.PurchaseOrder, data.purchaseOrderNo, data.purchaseOrderNumber)
                                : data.purchaseOrderNumber;

                            const purchaseOrderHtml = data.internalPurchaseOrderId > 0
                                ? ButtonHelper.nubButton_SystemDocument(SystemDocument.InternalPurchaseOrder, data.internalPurchaseOrderId, data.internalPurchaseOrderNumber)
                                : getPo();
                            return `<p class="m-0" style="min-height: 15px;">${GlobalTrader.StringHelper.showSerialNumber(purchaseOrderHtml, data.lineNo) || ""}</p><p class="m-0" style="min-height: 15px;">${row.expediteDateString || ""}</p>`;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title:
                            `
                                <span class="dt-column-title d-flex flex-column gap-1">
                                    <div>${allocationsLocalizer.crma}</div>
                                    <div class="dt-column-line"></div>
                                    <div>${allocationsLocalizer.returnDate}</div>
                                </span>
                            `,
                        data: (row) => (
                            {
                                customerRMANo: row.customerRMANo,
                                customerRMANumber: row.customerRMANumber,
                                returnDate: row.returnDate,
                            }
                        ),
                        render: (data, type, row) => {
                            const customerRMAHtml = data.customerRMANo > 0
                                ? ButtonHelper.nubButton_SystemDocument(SystemDocument.CustomerRMA, data.customerRMANo, data.customerRMANumber)
                                : data.customerRMANumber;

                            return `<p class="m-0" style="min-height: 15px;">${customerRMAHtml || ""}</p><p class="m-0" style="min-height: 15px;">${row.returnDateString}</p>`;
                        }
                    },
                    {
                        className: 'text-wrap text-break',
                        title:
                            `
                                <span class="dt-column-title d-flex flex-column gap-1">
                                    <div>${allocationsLocalizer.quantity}</div>
                                    <div class="dt-column-line"></div>
                                    <div>${allocationsLocalizer.landedCost}</div>
                                </span>
                            `,
                        data: (row) => (
                            {
                                quantityAllocated: row.quantityAllocated,
                                landedCost: row.landedCost,
                            }
                        ),
                        render: (data, type, row) => {
                            return `<p class="m-0" style="min-height: 15px;">${data.quantityAllocated}</p><p class="m-0" style="min-height: 15px;">${row.landedCostString}</p>`;
                        }
                    },

                ],
                rowId: 'allocationId',
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                }
            })
            .on('draw.dt', () => {
                this.$tableContainer.show();
                this.$allocationsTable.columns.adjust();
            })
            .on('select deselect', async (e, dt, type, indexes) => {
                if (type === 'row') {
                    const selectedLines = dt.rows({ selected: true }).data().toArray();
                    this.enableDeallocateButton(selectedLines.length > 0);
                    this.deallocationDialogManager.updateSelectedAllocation(selectedLines);
                    this._initAllocationImagesManager(selectedLines[selectedLines.length - 1]?.stockNo);
                }
            });
        const self = this;
        this.$allocationsTable.on('keydown', 'tr', function (e) {
            if (e.key === 'Enter') {
                e.preventDefault(); // Prevent default action

                // Toggle row selection
                const $row = $(this);
                if ($row.hasClass('selected')) {
                    $row.removeClass('selected');
                    self.$allocationsTable.row($row).deselect();
                } else {
                    $row.addClass('selected');
                    self.$allocationsTable.row($row).select();
                }
            }
        });

        CollapsibleBoxHelper.updateRowsCount(this.$allocationSectionContainer, this.allocationsData.length);
        CollapsibleBoxHelper.setCollapsibleBoxLoading(this.$allocationSectionContainer, false);
    }

    _setupButtonBehaviours() {
        this.enableDeallocateButton(false);
        this.$refreshButton.button().on('keydown click', async (event) => {
            if (event.type === "click" || event.key === "Enter") {
                event.preventDefault();
                await this.reloadSection(this.salesOrderLineId);
            }
        });          

        this.$imageRefreshButton.button().on("click keydown", async (event) => {
            if (event.type === "click" || event.key === "Enter") {
                CollapsibleBoxHelper.updateRowsCount(this.$imageSectionContainer, 0);
                await this.allocationImagesManager.refreshDocumentsSectionBox();
                CollapsibleBoxHelper.updateRowsCount(this.$imageSectionContainer, this.allocationImagesManager?.documentsList?.length);
            }
        })

        this.$deallocateButton.button().on("click", async () => {
            this.deallocationDialogManager.$dialog.dialog("open");
        });
    }

    async getAllocationsAsync() {
        if (!this.salesOrderLineId) return;
        const result = await GlobalTrader.ApiClient.getAsync(this.allocationsEndpoint());
        if (result.success) {
            this.allocationsData = result.data;
        }
    }
    async reloadSection(salesOrderLineId) {
        if (!salesOrderLineId) return;
        this.setLoadingCollapseSection(this.$allocationSectionContainer, true)
        this.enableDeallocateButton(false);

        this.salesOrderLineId = salesOrderLineId;
        await this.getAllocationsAsync();
        this.$allocationsTable.clear().draw();
        this.$allocationsTable.rows.add(this.allocationsData).draw();

        const selectedAllocation = this.$allocationsTable?.row({ selected: true }).data();
        this.$allocationsTable.row('#' + selectedAllocation?.allocationId).select();

        this.setLoadingCollapseSection(this.$allocationSectionContainer, false, this.allocationsData.length)
    }

    showSection(isShow = true) {
        if (isShow) {
            this.$allocationSectionContainer.show();
        } else {
            this.$allocationSectionContainer.hide();
            this.showImageSection(false);
        }
    }

    async _initAllocationImagesManager(stockId) {
        if (!stockId) {
            this.showImageSection(false);
            return;
        };
        this.showImageSection();
        this.setLoadingCollapseSection(this.$imageSectionContainer, true)

        if (!this.allocationImagesManager) {
            const imageAllocationsSection = { ...imageAllocationsSectionBase };
            imageAllocationsSection.sectionId = imageAllocationsSection.sectionId + `-${this.level}`;

            this.allocationImagesManager = new MediaDocumentManager({
                documentSectionComponent: imageAllocationsSection,
                id: stockId,
                isDisplayMaxSize: false
            });
            await this.allocationImagesManager.initialize();
        } else {
            await this.allocationImagesManager.reloadDocumentsSectionBox(stockId);
        }
        this.setLoadingCollapseSection(this.$imageSectionContainer, false, this.allocationImagesManager?.documentsList?.length || 0)
    }

    async showImageSection(isShow = true) {
        if (!this.$imageSectionContainer) return;
        if (isShow) {
            this.$imageSectionContainer.show();
        } else {
            this.$imageSectionContainer.hide();
        }
    }

    setLoadingCollapseSection($allocationSectionContainer, isLoading = true, count = 0) {
        CollapsibleBoxHelper.setCollapsibleBoxLoading($allocationSectionContainer, isLoading);
        CollapsibleBoxHelper.updateRowsCount($allocationSectionContainer, count);
    }

    _setupDeallocateSuccessEvent() {
        this.deallocationDialogManager.$dialog.on('deallocateSuccess', async () => {
            this.enableDeallocateButton(false);
            this.setLoadingCollapseSection(this.$allocationSectionContainer, true)
            this.showImageSection(false);
        })
    }

    enableDeallocateButton(isEnabled = true) {
        const isSalesOrderComplete = $("#hidSalesOrderStatus").val() == salesOrderInfo.completeInt;
        if (isSalesOrderComplete) {
            this.$deallocateButton.prop('disabled', true);
            return;
        }
        this.$deallocateButton.prop('disabled', !isEnabled);
    }
    
}

