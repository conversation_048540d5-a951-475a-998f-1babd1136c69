﻿import { EventEmitter } from '../../../../../../../components/base/event-emmiter.js?v=#{BuildVersion}#';
import { SalesOrderDetailApiUrl } from '../../../../../../../config/api-endpoint-config.js';

export class CreateIPOManager extends EventEmitter {
    constructor(salesOrderId, type, dialogSelector, formSelector) {
        super();
        this.clientId = SODetailGeneralInfo.soClientId;
        this.salesOrderId = salesOrderId;
        this.salesOrderLineIds = [];
        this.$dialog = $(`#${dialogSelector}`);
        this.$form = $(`#${formSelector}`);
        this.$dropdown = this.$form.find('#create-ipo-warehouse-dropdown');
        this.$preferredWarehouse = this.$form.find('#preferred-warehouse-name');
        this.$preferredWarehouseWrapper = this.$form.find('#preferred-warehouse-wrapper');
        this.$saveButton = null;
        this.dialog = null;
        this.baseUrl = SalesOrderDetailApiUrl;
        this.type = type
    }

    initialize() {
        this._setUpDialog();
        this._setUpDropdown();
        this._setUpValidator();
    }

    setSelectedLine(salesOrderLineIds) {
        this.salesOrderLineIds = salesOrderLineIds;
    }

    openDialog() {
        this.$dialog.dialog("open");
    }

    bindingPreferredWarehouse(warehouseName, warehouseNo) {
        if(!warehouseName || !warehouseNo) {
            this.$preferredWarehouseWrapper.toggleClass('d-none', true);
            return;
        }
        if (warehouseName.length > 1 && warehouseNo > 0) {
            this.$preferredWarehouseWrapper.toggleClass('d-none', false)
            this.$preferredWarehouse.text(warehouseName);
            this.$dropdown.val(warehouseNo);
        } else {
            this.$preferredWarehouseWrapper.toggleClass('d-none', true)
        }
    }

    _setUpDialog() {
        this.dialog = this.$dialog.dialog({
            width: "auto",
            height: "auto",
            maxHeight: 700,
            minWidth: 240,
            autoOpen: false,
            draggable: false,
            modal: true,
            open: async () => {
                $('.ui-dialog-titlebar-close').hide();
                const $input = this.$dialog.find('select:visible:enabled:first');
                if ($input.length) $input[0].focus();
            },
            buttons: [
                {
                    text: window.localizedStrings.save,
                    class: 'btn btn-primary',
                    id: `save-create-ipo-btn-${this.type}`,
                    html: `<img src="/img/icons/save.svg" alt="Save icon"/>${window.localizedStrings.save}`,
                    click: async () => {
                        if (this.$form.valid()) {
                            await this._handleSaveCreateIPO();
                        } else {
                            this.$dialog.find(".form-error-summary").show();
                        };
                    }
                },
                {
                    text: window.localizedStrings.cancel,
                    class: 'btn btn-danger',
                    html: `<img src="/img/icons/slash.svg" alt="Cancel icon"/>${window.localizedStrings.cancel}`,
                    click: () => {
                        this.$dialog.dialog('close');
                    }
                }
            ],
            close: () => {
                this._resetDialog();
            }
        });
        this.$saveButton = $(`#save-create-ipo-btn-${this.type}`);
    }

    _setUpDropdown() {
        this.$dropdown.dropdown({
            serverside: false,
            endpoint: `/lists/warehouse-for-client`,
            params: {
                GlobalLoginClientNo: this.clientId
            },
            textKey: 'name',
            valueKey: 'id',
            placeholderValue: 0,
            selectedValue: null,
        });
    }

    _setUpValidator() {
        this.$form.validate({
            rules: {
                warehouseNo: {
                    min: 1
                },
            },
            messages: {
                warehouseNo: window.localizedStrings.requiredField,
            },
            invalidHandler: function (event, validator) {
                if (validator.errorList.length) {
                    $(validator.errorList[0].element).trigger("focus");
                }
            },
        }); 
    }

    _resetDialog() {
        this.$dialog.find(".form-error-summary").hide();
        this.$form[0].reset();
        this.$form.validate().resetForm();
        this.$form.find('.is-invalid').removeClass("is-invalid");
        this.$dropdown.dropdown("reset");
    }

    async _handleSaveCreateIPO() {
        this.$dialog.find(".form-error-summary").hide();
        this.$dialog.dialog("setLoading", true);
        this.$saveButton.prop('disabled', true);

        const requestBody = {
            soLineNumbers: this.salesOrderLineIds,
            warehouseNo: this.$dropdown.val(),
            createFrom: this.type,
            globalClientId: this.clientId
        }
        const header = { "RequestVerificationToken": this.$form.find(':input[name="__RequestVerificationToken"]').val() };

        const response = await this._createIPO(this.salesOrderId, requestBody, header);
        if (!response?.success) {
            showToast("danger", response.message);
        } else {
            alert(response.data.message);
            this.$dialog.trigger('saveSuccess');
            showToast("success", window.localizedStrings.saveChangedMessage)
            this._notifyIPOCreated(response.data.notifyObjects, this.clientId, header)
        }

        this.$saveButton.prop('disabled', false);
        this.$dialog.dialog("setLoading", false);
        this.$dialog.dialog("close");
    }

    async _createIPO(salesOrderId, requestBody, header) {
        let response = await GlobalTrader.ApiClient.postAsync(`${this.baseUrl}/${salesOrderId}/create-ipo`, requestBody, header);
        return response;
    }

    async _notifyIPOCreated(notifyObjects, clientId, header) {
        const requestBody = {
            clientId: clientId,
            notifyObjects: notifyObjects
        }
        await GlobalTrader.ApiClient.postAsync(`${this.baseUrl}/create-ipo-notify`, requestBody, header);
    }
}