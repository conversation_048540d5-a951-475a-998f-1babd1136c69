import { ResizeLiteDatatable } from '../../../../../../../components/base/resize-lite-datatable/resize-lite-datatable.component.js?v=#{BuildVersion}#'
import { ROHSHelper } from '../../../../../../../helper/rohs-helper.js?v=#{BuildVersion}#'
import { ButtonHelper } from '../../../../../../../helper/button-helper.js?v=#{BuildVersion}#'
export class LinesResizeDatatable extends ResizeLiteDatatable {
    constructor(tableId, data, level) {
        const baseColumnConfig = {
            data: null,
            type: 'string',
            className: 'text-wrap text-break',
        };
        super(
            tableId,
            '',
            {
                resizeConfig: {
                    numberOfRowToShow: 3
                },
                rowId: 'lineId',
                ajax: null,
                data: data,
                createdRow: function (row, data, dataIndex) {
                    $(row).attr('tabindex', '0');
                    if (data.inactive) {
                        $(row).addClass('text-light-gray');
                    }
                },
                columns: [
                    {
                        ...baseColumnConfig,
                        width: '7%',
                        render: (data, type, row, meta) => {
                            const isService = row.serviceNo > 0;
                            const details = {
                                allocated: !isService && data.isAllocated,
                                shipped: data.isShipped,
                                isService: isService,
                                inactive: row.inactive,
                                partShipped: !isService && !row.isShipped && row.shipped > 0,
                                posted: row.isPosted
                            }
                            let img = "";
                            if (details.posted) img = "posted_sel.gif";
                            if (details.allocated) img = "allocated_sel.gif";
                            if (details.shipped) img = "shipped_sel.gif";
                            if (details.partShipped) img = "partshipped_sel.gif";
                            if (details.inactive) img = "inactive_sel.gif";

                            let checkboxHtml = '<span>&nbsp</span>';
                            if (row.isIPO) {
                                if (row.isIPO && row.isChecked && row.isPosted && !row.isAllocated) {
                                    checkboxHtml = `<input id="${level}-tab-can-create-ipo-${row.lineId}" type="checkbox" data-line-id="${row.lineId}" />`;
                                }
                                else {
                                    checkboxHtml = `<input id="${level}-tab-can-create-ipo-${row.lineId}" type="checkbox" data-line-id="${row.lineId}" disabled/>`;
                                }
                            }
                            if (img.length === 0) {
                                return `
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div></div>
                                        ${checkboxHtml}
                                    </div>
                                    `
                            }
                            else {
                                return `
                                    <div class="d-flex justify-content-between align-items-center">
                                        <img src="/App_Themes/Original/images/tables/${img}" alt="${img}" />
                                        ${checkboxHtml}
                                    </div>
                                    `
                            }
                        }
                    },
                    {
                        data: 'lineNo',
                        title: linesResource.lineNo,
                        type: 'string',
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.partNo}</div>${linesResource.customerPart}`,
                        width: '17%',
                        render: (data, type, row, meta) => {
                            return `
                                <div>${ROHSHelper.writePartNo(row.part, row.rohs)}</div>
                                <span>${GlobalTrader.StringHelper.setCleanTextValue(row.customerPart)}</span>
                            `;
                        }
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.mfr}</div>${linesResource.dc}`,
                        width: '7%',
                        render: (data, type, row, meta) => {
                            const isService = row.serviceNo > 0;
                            if (!isService) {
                                return `
                                <div>${ButtonHelper.nubButton_Manufacturer(row.mfrNo, row.mfr, row.mfrAdvisoryNotes)}</div>
                                <span>${GlobalTrader.StringHelper.setCleanTextValue(row.dc)}</span>
                            `;
                            }
                            else return '';
                        }
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.product}</div>${linesResource.package}`,
                        width: '12%',
                        render: (data, type, row, meta) => {
                            const isService = row.serviceNo > 0;
                            if (!isService) {
                                return `
                                <span>${GlobalTrader.StringHelper.setCleanTextValue(row.product)}</span>
                                <span>${GlobalTrader.StringHelper.setCleanTextValue(row.package)}</span>
                            `;
                            }
                            else return '';
                        }
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.ordered}</div>${linesResource.shipped}`,
                        render: (data, type, row, meta) => {
                            return `
                                <div>${row.quantity}</div>
                                <div>${row.shipped}</div>
                            `;
                        }
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.allocated}</div>${linesResource.backOrder}`,
                        render: (data, type, row, meta) => {
                            const isService = row.serviceNo > 0;
                            if (!isService) {
                                return `
                                <div>${row.allocated}</div>
                                <div>${row.backOrder}</div>
                            `;
                            }
                            else return '';
                        }
                    },
                    {
                        ...baseColumnConfig,
                        data: 'price',
                        title: `${linesResource.unitPrice}`,
                        width: '12%',
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.total}</div>${linesResource.tax}`,
                        width: '12%',
                        render: (data, type, row, meta) => {
                            return `
                                <div>${row.total}</div>
                                <div>${row.tax}</div>
                            `;
                        }
                    },
                    {
                        ...baseColumnConfig,
                        title: `<div style="border-bottom: dotted 1px #999; margin-bottom: 5px; padding-bottom: 8px;">${linesResource.dateConfirmed}</div>${linesResource.asRequired}`,
                        render: (data, type, row, meta) => {
                            return `
                                <div>${row.dateConfirmed.length <= 0 ? '&nbsp;' : row.dateConfirmed}</div>
                                <span>${row.aS6081 ? 'Yes' : 'No'}</span>
                            `;
                        }
                    },
                ],
            }
        )
    }

    init() {
        super.init();
        this.$table.on('change', "input[id*='-tab-can-create-ipo-']", () => {
            const checkedLineIds = [];
            this.$table.find("input[id*='-tab-can-create-ipo-']:checked").each(function () {
                const lineId = $(this).data('line-id');
                if (lineId) {
                    checkedLineIds.push(lineId);
                }
            });
            this.trigger('ipo-checked', checkedLineIds);
        });
    }

    deselectIpoCheckbox() {
        this.$table.find("input[id*='-tab-can-create-ipo-']").prop("checked", false).trigger('change');
    }
}