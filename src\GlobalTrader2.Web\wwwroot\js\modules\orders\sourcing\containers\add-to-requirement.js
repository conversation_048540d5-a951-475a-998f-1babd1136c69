﻿import { StepperComponent } from "../../../../components/stepper/stepper.component.js?v=#{BuildVersion}#";
import { TableFilterComponent } from "../../../../components/table-filter/table-filter.component.js?v=#{BuildVersion}#";
import { ADD_ALTERNATIVE_PART_FILTER_INPUTS, ADD_ALTERNATIVE_PART_STEPS } from "../constants/alternative-part.constant.js?v=#{BuildVersion}#";

const cancel = window.localizedStrings.cancel;
const yesText = window.localizedStrings.yes;
const noText = window.localizedStrings.no;
const {
    saveChangedMessage,
    notAllSaveChangedMessage,
    failToSaveChangedMessage,
    restrictedManufacturerMessage,
    offersFailedToBeAddedMessage,
    partsAdded,
    partsNotAdded,
    reason,
    offerFailedHeader,
    partNumbers,
    requirementLabel,
} = window.localizedStrings;

const AddToRequirementSource = Object.freeze({
    ALTERNATIVE: {
        type: "alternative",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    TRUSTED: {
        type: "trusted",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    OFFERS: {
        type: "offers",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    STRATEGICOFFERS: {
        type: "strategicoffers",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    REVERSELOGISTICS: {
        type: "reverselogistics",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    OFFERSHISTORY: {
        type: "offershistory",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    STOCKONORDER: {
        type: "stockonorder",
        endpoint: "orders/sourcing/add-sourcing-result"
    },
    PRICEREQUEST: {
        type: "pricerequest",
        endpoint: "orders/sourcing/add-sourcing-result"
    }
});

const sourcingIdFieldMap = {
    [AddToRequirementSource.ALTERNATIVE.type]: "alternativePartId",
    [AddToRequirementSource.TRUSTED.type]: "excessId",
    [AddToRequirementSource.OFFERS.type]: "offerId",
    [AddToRequirementSource.STRATEGICOFFERS.type]: "epoId",
    [AddToRequirementSource.REVERSELOGISTICS.type]: "reverseLogisticId",
    [AddToRequirementSource.OFFERSHISTORY.type]: "historyId",
    [AddToRequirementSource.STOCKONORDER.type]: "stockId",
    [AddToRequirementSource.PRICEREQUEST.type]: "purchaseRequestLineDetailId",
};

const isOnRequirementPage = isRequirementPage();
let addToRequirementSource;
let selectedSourcings = [];
let selectedRequirementId;
let selectedRequirementNumber;
let filter;
let stepper;
let customerRequirementsMainInfoTable;
let dialog;

$(async () => {
    dialog = initDialog();

    initStepper();

    setUpEventListeners();

    await initFilter();
})

function addTooltipToDisabledButton(buttonSelector, tooltipText) {
    const $button = $(buttonSelector);

    if ($button.prop("disabled")) {
        // Wrap only if not already wrapped
        if (!$button.parent().is(".btn-tooltip-wrapper")) {
            $button.wrap(`<span title="${tooltipText}"></span>`);
        } else {
            $button.parent().attr("title", tooltipText);
        }
    } else {
        // If enabled, apply tooltip directly
        $button.attr("title", tooltipText);
    }
}

function setupAddToRequirementButton(buttonSelector, tableSelector, sourceEnum, isDisabled = false, tooltipMessage = '') {
    if (isDisabled) {
        addTooltipToDisabledButton(buttonSelector, tooltipMessage);
        return;
    }

    $(buttonSelector).button().on("click", function (event) {
        event.stopPropagation();

        selectedSourcings = $(tableSelector).DataTable().rows({ selected: true }).data().toArray();

        addToRequirementSource = sourceEnum;
        $("#add-to-requirement-dialog").dialog("open");

        $("#add-to-requirement-dialog")
            .find(":input:visible:not([readonly]):first")
            .trigger("focus");

        $("#hubrfq-warning-message").toggleClass("d-none", addToRequirementSource.type !== AddToRequirementSource.REVERSELOGISTICS.type);

        if (currentSourcingPageType !== sourcingPageTypeMap.Sourcing) {
            stepper.updateStepper(2);

            $("#add-to-requirement-stepper-container").addClass("d-none");

            const selectedRows = $(`#${stateValue.customerRequirementDetailsId}`)
                .DataTable()
                .rows({ selected: true })
                .data()
                .toArray();

            const selectedRequirement = selectedRows.length === 1 ? selectedRows[0] : null;

            if (selectedRequirement != null) {
                $("#customer-requirement-number-preview").text(selectedRequirement.customerRequirementNumber);
                selectedRequirementId = selectedRequirement.id ?? selectedRequirement.customerRequirementId;
                selectedRequirementNumber = selectedRequirement.customerRequirementNumber;
            }
        }
    });
}

function setUpEventListeners() {
    setupAddToRequirementButton("#alt-part-add-to-requirement", "#alternative-parts-table", AddToRequirementSource.ALTERNATIVE);
    setupAddToRequirementButton("#trusted-add-to-requirement", "#trusted-table", AddToRequirementSource.TRUSTED);
    setupAddToRequirementButton("#offer-add-to-requirement", "#offers-table", AddToRequirementSource.OFFERS);
    setupAddToRequirementButton("#strategic-offers-add-to-requirement", "#strategic-offers-table", AddToRequirementSource.STRATEGICOFFERS);
    setupAddToRequirementButton("#reverse-logistics-add-to-requirement", "#reverse-logistics-table", AddToRequirementSource.REVERSELOGISTICS);
    setupAddToRequirementButton("#offer-history-add-to-requirement", "#offers-history-table", AddToRequirementSource.OFFERSHISTORY);
    setupAddToRequirementButton("#stock-on-order-add-to-requirement", "#stock-on-order-table", AddToRequirementSource.STOCKONORDER);
    setupAddToRequirementButton("#price-request-add-to-requirement", "#price-request-table", AddToRequirementSource.PRICEREQUEST);

    $("#search-requirement-btn").button().on("click", function (event) {
        event.stopPropagation();
        onSearch();
    });
}

function onSearch() {
    if (!customerRequirementsMainInfoTable) {
        setupDataTable();
    } else {
        customerRequirementsMainInfoTable.ajax.reload();
    }

    $("#customer-requirements-main-info-table-wrapper").removeClass("d-none")
}

async function initFilter() {
    filter = new TableFilterComponent('#add-to-requirement-filter', 'Select a Customer Requirement', {
        inputConfigs: ADD_ALTERNATIVE_PART_FILTER_INPUTS,
        showButtons: false,
        wrapperClass: 'bg-none m-0 p-0'
    });

    filter.on("controlchanged.mtf", () => {
        if ($('#add-to-requirement-filter input[type="checkbox"]:checked').length > 0) {
            $("#search-requirement-btn").prop('disabled', false)
        } else {
            $("#search-requirement-btn").prop('disabled', true)
        }
    });

    await filter.init();
}

function initStepper() {
    stepper = new StepperComponent(document.getElementById("add-to-requirement-stepper-container"), ADD_ALTERNATIVE_PART_STEPS);

    stepper.on("stepChange.ms", ({ step }) => {
        updateDialogButtonsByStep(step);
    })
}

function updateDialogButtonsByStep(step) {
    if (step === 1) {
        dialog.dialog({
            buttons: [
                {
                    text: cancel,
                    click: function () {
                        $(this).dialog("close");
                    }
                }
            ]
        })
        $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${cancel}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/slash.svg" alt="${cancel}"> ${cancel}`);
    } else {
        dialog.dialog({
            buttons: [
                {
                    text: yesText,
                    click: function () {
                        handleSaveAddToRequirement();
                    }
                },
                {
                    text: noText,
                    click: function () {
                        $(this).dialog("close");
                    }
                }
            ]
        });
        $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${yesText}')`).addClass("btn btn-primary fw-normal").html(`<img src="/img/icons/check.svg" alt="${yesText}"> ${yesText}`).trigger("focus");
        $(`.ui-dialog-buttonpane .ui-dialog-buttonset button:contains('${noText}')`).addClass("btn btn-danger fw-normal").html(`<img src="/img/icons/slash.svg" alt="${noText}"> ${noText}`);
    }
}
function initDialog() {
    return $("#add-to-requirement-dialog").dialog({
        autoOpen: false,
        height: "auto",
        width: isOnRequirementPage ? '35vw' : '70vw',
        modal: true,
        maxHeight: $(window).height(),
        draggable: false,
        buttons: [
            {
                text: cancel,
                click: function () {
                    $(this).dialog("close");
                }
            }
        ],
        open: function (event, ui) {
            $(this).removeClass('d-none');
            $('.ui-dialog-titlebar-close').css('display', 'none');
        },
        close: function (event, ui) {
            stepper.updateStepper(1);
        },
    });
}

function setupDataTable() {
    $('#customer-requirements-main-info-table').on('preXhr.dt', function () {
        $('#customer-requirements-main-info-table tbody tr .dt-empty').hide();

        $(`#customer-requirements-main-info-table-wrapper thead th`)
            .removeClass('dt-orderable-asc dt-orderable-desc') // Remove neutral sorting icon
            .addClass('position-relative');

        $(`#customer-requirements-main-info-table-wrapper thead th:not(.dt-orderable-none)`)
            .attr('role', 'button');

        $(`#customer-requirements-main-info-table-wrapper thead th .dt-column-order`).addClass('dt-column-order-custom');
    })

    customerRequirementsMainInfoTable = new DataTable('#customer-requirements-main-info-table', {
        autoWidth: false,
        scrollCollapse: true,
        paging: true,
        scrollY: '400px',
        dataSrc: 'data',
        serverSide: true,
        lengthMenu: [5, 10, 25, 50],
        pageLength: 10,
        ajax: {
            url: "/api/orders/customer-requirements/main-info",
            type: 'POST',
            contentType: 'application/json',
            data: (data) => {
                const filterValues = filter.getAllValue()

                return JSON.stringify({
                    customerRequirementNoLo: filterValues.requirementSearch.isOn ? filterValues.requirementSearch.low : null,
                    customerRequirementNoHi: filterValues.requirementSearch.isOn ? filterValues.requirementSearch.hi : null,
                    includeClosed: filterValues.includeClosed.isOn ? filterValues.includeClosed.value : null,
                    partSearch: filterValues.partSearch.isOn ? filterValues.partSearch.value : null,
                    companySearch: filterValues.companySearch.isOn ? filterValues.companySearch.value : null,
                    receivedDateFrom: filterValues.receivedDateFrom.isOn ? formatDateFilter(filterValues.receivedDateFrom.value) : null,
                    receivedDateTo: filterValues.receivedDateTo.isOn ? formatDateFilter(filterValues.receivedDateTo.value) : null,
                    draw: data.draw,
                    index: data.start,
                    size: data.length,
                    sortDir: data.order[0]?.column ? GlobalTrader.SortHelper.getSortDirIdByName(data.order[0].dir) : 1,
                    orderBy: data.order[0]?.column ? data.order[0].column : 1,
                });
            },
        },
        info: true,
        responsive: {
            details: false
        },
        select: {
            style: 'single'
        },
        ordering: true,
        searching: false,
        processing: true,
        columnDefs: [
            { visible: false, targets: 0 },
            { "orderSequence": ["asc", "desc"], "targets": "_all" },
        ],
        language: {
            emptyTable: `<i>${window.localizedStrings.noDataFound}</i>`,
            zeroRecords: `<i>${window.localizedStrings.noDataFound}</i>`,
            infoFiltered: "",
            lengthMenu: "_MENU_ per page",
            loadingRecords: "",
        },
        dom: '<"dt-layout-row dt-layout-table" <"dt-layout-cell dt-layout-full" rt >>' +
            '<"dt-layout-row" <"dt-layout-cell dt-layout-start" i l >' +
            '<"dt-layout-cell dt-layout-end" p >><"clear">',
        rowId: "id",
        order: [[1, "desc"]],
        columns: [
            {
                data: "customerRequirementId",
                title: "Id",
                type: 'string',
            },
            {
                data: "customerRequirementNumber",
                title: "Req",
                type: 'string',
            },
            {
                data: "companyName",
                title: "Company",
                type: 'string',
            },
            {
                data: null,
                title: "Part No",
                render: (row) => `${GlobalTrader.StringHelper.getPartNoAndRohsStatus(row.part, row.rohs)}`
            },
            {
                data: "receivedDateText",
                title: "Date",
                type: 'string',
            },
            {
                data: "quantity",
                title: "Quantity",
                type: 'string',
            },
            {
                data: "formatedPrice",
                title: "Unit Price",
                type: 'string',
            },
        ]
    });

    customerRequirementsMainInfoTable.on('select.dt', function (e, dt, type, indexes) {
        const rowData = customerRequirementsMainInfoTable.row(indexes[0]).data();
        stepper.updateStepper(2);
        $("#customer-requirement-number-preview").text(rowData.customerRequirementNumber);
        selectedRequirementId = rowData.customerRequirementId;
        selectedRequirementNumber = rowData.customerRequirementNumber;
    });
}

// dd/MM/yyyy
function formatDateFilter(dateString) {
    const [day, month, year] = dateString.split("/");
    return `${year}-${month}-${day}T00:00:00.000Z`
}

async function handleSaveAddToRequirement() {
    const sourcingIdField = sourcingIdFieldMap[addToRequirementSource.type];

    const unrestrictedSourcingIds = selectedSourcings
        .filter(part => !part.isRestrictedManufacturer)
        .map(part => part[sourcingIdField]);

    const restrictedParts = selectedSourcings
        .filter(part => part.isRestrictedManufacturer);

    dialog.dialog("setLoading", true);

    const header = { "RequestVerificationToken": $('input[name="__RequestVerificationToken"]').val() }
    const result = await GlobalTrader.ApiClient.postAsync(addToRequirementSource.endpoint, {
        source: addToRequirementSource.type,
        customerRequirementId: selectedRequirementId,
        lineIDs: unrestrictedSourcingIds,
        pageType: currentSourcingPageType
    }, header);

    dialog.dialog("setLoading", false);

    if (!result) return;

    if (!result.success) {
        showToast("danger", result.message);
        dialog.dialog("close");
        return;
    }

    const getPartNumber = sourcingId => {
        const match = selectedSourcings.find(s => s[sourcingIdField] === sourcingId);
        return match ? match.part : `[ID: ${sourcingId}]`;
    };

    const successParts = result.data
        .filter(r => r.success)
        .map(r => getPartNumber(r.sourcingId));

    let failedParts = result.data
        .filter(r => !r.success)
        .map(r => getPartNumber(r.sourcingId));

    let failedMessages = result.data
        .filter(r => !r.success)
        .map(r => r.linkCurrencyMsg?.trim() || offersFailedToBeAddedMessage);

    if (restrictedParts.length > 0) {
        const restrictedPartNumbers = restrictedParts.map(p => p.part);
        failedParts = [...failedParts, ...restrictedPartNumbers];
        failedMessages.push(restrictedManufacturerMessage);
    }

    const isOfferWithFailures = failedMessages.length > 0;

    const uniqueMessages = [...new Set(failedMessages)];

    const toastHeader = getToastHeader(successParts, failedParts);

    showToastAddToRequirement({
        header: toastHeader,
        successParts: formatPartList(successParts),
        failedParts: formatPartList(failedParts),
        requirementId: selectedRequirementId,
        requirementNumber: selectedRequirementNumber,
        failedMessage: formatPartList(uniqueMessages),
        isOfferWithFailures: isOfferWithFailures,
        localized: {
            partsAdded: partsAdded,
            partsNotAdded: partsNotAdded,
            reason: reason,
            offerFailedHeader: offerFailedHeader,
            partNumbers: partNumbers,
            requirementLabel: requirementLabel
        },
        isOnRequirementPage
    });

    dialog.dialog("close");

    if (isOnRequirementPage && stateValue?.sourcingResultsBox) {
        $(`#${stateValue.sourcingResultsBox}`).find('.section-box-refesh-button').trigger('click');
    }

    if (isOnRequirementPage && successParts.length > 0) {
        $(document).trigger("requirementAdded", { parts: successParts });
    }
}

function formatPartList(parts) {
    return parts && parts.length > 0
        ? parts.join('<span class="fw-bold" style="color: #939393"> | </span>')
        : '';
}

function getToastHeader(successParts, failedParts) {
    if (successParts.length === 0) {
        return failToSaveChangedMessage;
    }

    if (failedParts.length > 0) {
        return notAllSaveChangedMessage;
    }
    return saveChangedMessage;
}