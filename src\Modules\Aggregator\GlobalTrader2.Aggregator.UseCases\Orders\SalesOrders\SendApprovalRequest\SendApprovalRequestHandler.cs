﻿using System.Text;
using GlobalTrader2.Aggregator.UseCases.Account.LoginForSendEmai.LoginForSendEmai.Queries;
using GlobalTrader2.Aggregator.UseCases.FileUpload.Commands.UploadSalesOrderReportPdf;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Extensions;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.SalesOrder;
using GlobalTrader2.Orders.UserCases.Orders.ConvertCurrency;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPowerAppTokenInfo;

namespace GlobalTrader2.Aggregator.UseCases.Orders.SalesOrders.SendApprovalRequest
{
    public class SendApprovalRequestHandler : IRequestHandler<SendApprovalRequestCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<SorDetailForPowerAppReadModel> _sorDetailForPowerAppRepository;
        private readonly ISender _sender;
        private readonly IBaseRepository<SalesOrderOpenLineSummaryValuesModel> _saleOrderTotalRepository;
        private readonly IBaseRepository<SalesOrderForCompanyReadModel> _salesOrderOpenRepository;
        private readonly IPowerAutomateApiClient _powerAutomateApiClient;
        private readonly IBaseRepository<PowerAppUrl> _powerappUrlRepository;
        public SendApprovalRequestHandler(IBaseRepository<SorDetailForPowerAppReadModel> sorDetailForPowerAppRepository
            , ISender sender
            , IBaseRepository<SalesOrderOpenLineSummaryValuesModel> saleOrderTotalRepository
            , IBaseRepository<SalesOrderForCompanyReadModel> salesOrderOpenRepository
            , IPowerAutomateApiClient powerAutomateApiClient
            , IBaseRepository<PowerAppUrl> powerappUrlRepository)
        {
            _sorDetailForPowerAppRepository = sorDetailForPowerAppRepository;
            _sender = sender;
            _saleOrderTotalRepository = saleOrderTotalRepository;
            _salesOrderOpenRepository = salesOrderOpenRepository;
            _powerAutomateApiClient = powerAutomateApiClient;
            _powerappUrlRepository = powerappUrlRepository;
        }
        public async Task<BaseResponse<bool>> Handle(SendApprovalRequestCommand request, CancellationToken cancellationToken)
        {   
            var sorParams = new List<SqlParameter>()
            {
                new SqlParameter("SalesOrderNo",SqlDbType.Int){Value= request.SalesOrderId},
            };
            var sorQuery = await _sorDetailForPowerAppRepository
                .SqlQueryRawAsync($"{StoredProcedures.Get_SOR_Detail_For_PowerApp} @SalesOrderNo"
                , sorParams.ToArray());
            var sor = sorQuery.Count > 0 ? sorQuery[0] : null;
            if (sor != null)
            {
                DateTime currencyDate = sor.CurrencyDate == null ? sor.DateOrdered : sor.CurrencyDate.Value;
                var paramters = new List<SqlParameter>() {
                new SqlParameter("@CompanyId", System.Data.SqlDbType.Int){Value = sor.CompanyNo}
                 };
                var openSalesOrders = await _salesOrderOpenRepository.SqlQueryRawAsync(
                    $"{StoredProcedures.Select_All_SalesOrder_Open_for_Company} @CompanyId"
                    , paramters.ToArray());

                decimal openSOTotal = 0;
                foreach (var openSalesOrder in openSalesOrders)
                {
                    if (openSalesOrder != null)
                    {
                        var saleOrderIdParams = new List<SqlParameter>() {
                        new SqlParameter("@SalesOrderId", System.Data.SqlDbType.Int)
                        { Value = openSalesOrder.SalesOrderId }
                        };
                        var soTotalQuery = (await _saleOrderTotalRepository.SqlQueryRawAsync(
                           sql: $"{StoredProcedures.Select_SalesOrder_OpenLineSummaryValues}" +
                           $"@SalesOrderId ", parameters: saleOrderIdParams.ToArray()));

                        var soTotal = soTotalQuery?.Count > 0 ? soTotalQuery[0] : null;

                        if (soTotal != null)
                        {

                            if (soTotal?.CurrencyNo != sor.SOCurrencyNo)
                            {
                                openSOTotal += await _sender.Send(new ConvertValueBetweenTwoCurrenciesQuery(soTotal?.TotalValue, soTotal?.CurrencyNo ?? 0, openSalesOrder?.SOCurrencyNo ?? 0, currencyDate), cancellationToken);

                            }
                            else
                            {
                                openSOTotal += Convert.ToDecimal(soTotal?.TotalValue ?? 0);
                            }
                        }


                    }

                }
                bool isSoCompleted = sor.IsSoCompleted == 1;
                bool isPoDocExists = sor.IsPODocExists == 1;
                bool isAllowAuthorise = Convert.ToDecimal(sor.CreditLimit) >= (Convert.ToDecimal(sor.Balance) + openSOTotal);
                if (isAllowAuthorise && !isSoCompleted && isPoDocExists)
                {  
                    var powerAppUrl = await _powerappUrlRepository.GetAsync(x => x.FlowName == PowerAutomateFlow.SOR_Approval);
                    if(powerAppUrl != null && !string.IsNullOrEmpty(powerAppUrl.FlowUrl))
                    { 
                        var uploadPdf = await _sender.Send(new UploadSalesOrderReportPdfCommand() { SalesOrderId = request.SalesOrderId, ClientId = request.ClientId, LoginFullName = request.LoginFullName, BaseDirectory = request.BaseDirectory, LoginId = request.LoginId}, cancellationToken);

                        var emailToBuilder = new StringBuilder();

                        foreach (var loginId in request.ApproverIds)
                        {
                            var loginPreference = await _sender.Send(new GetLoginForSendEmailQuery
                            {
                                LoginId = loginId
                            }, cancellationToken);

                            if (loginPreference?.Success == true && !string.IsNullOrEmpty(loginPreference.Data?.Email))
                            {
                                emailToBuilder.Append(loginPreference.Data.Email).Append(";");
                            }
                        }
                        string emailTo = emailToBuilder.ToString();
                        var powerAppToken = await _sender.Send(new GetPowerAppTokenInfoQuery(request.LoginId, ApproverType.SORAPPROVAL.GetDescription(),request.IsNotifySO)
                                                                , cancellationToken);
                        

                        var requestBody = FormatRequestBody(
                              request.SalesOrderId
                            , StringHelper.RemoveHyperLink(request.Message)
                            , request.Subject
                            , emailTo
                            , request.LoginId
                            , uploadPdf.Data ?? sor.SOReportFileName
                            , sor.POReportFileName
                            , powerAppToken?.Data?.RequestId ?? 0
                            , powerAppToken?.Data?.TokenValue ?? string.Empty
                            , request.Uri
                            , request.V1Uri
                            );
                        await _powerAutomateApiClient.SendSOApprovalToTeam(powerAppUrl.FlowUrl ?? string.Empty, requestBody);
                    }
                }
                  
            }

            return new BaseResponse<bool>()
            {
                Success = true,
                Data = true,
            };
        }
        private static Dictionary<string, string> FormatRequestBody(
            int? salesOrderId
            , string message
            , string subject
            , string toAddress
            , int userId
            , string SOReportFileName
            , string POReportFileName
            , int requestId
            , string tokenValue
            , string uri
            , string v1Uri)
        {
            string messageFormatted = message.Replace("Sales Order", $"[Sales Order]({uri}/Orders/SalesOrders/Details?so={salesOrderId})");
            string soReportFormmated = $"[SO Report.pdf]({v1Uri}/PowerApp.ashx?RequestType=SOREPORT&SOFileName={SOReportFileName})";
            string poReportFormmated = $"[Customer PO.pdf]({v1Uri}/PowerApp.ashx?RequestType=POREPORT&SOFileName={POReportFileName})";
            string SOReportAttachment = $"/gtdocmgmt/SOREPORT/{SOReportFileName}";
            string POReportAttachment = $"/gtdocmgmt/SOR/{POReportFileName}";

            var requestBody = new Dictionary<string, string>()
            {
                { "Email", toAddress },
                { "UserId", userId.ToString() },
                { "Subject", subject },
                { "SalesOrderId", salesOrderId?.ToString() ?? string.Empty},
                { "SOReportUrl", soReportFormmated },
                { "POReportUrl", poReportFormmated },
                { "SOReportAttachment", SOReportAttachment },
                { "POReportAttachment", POReportAttachment },
                { "RequestId", requestId.ToString() },
                { "TokenValue", tokenValue },
                { "Message", messageFormatted }
            };

            return requestBody;
        }
    }

}
