﻿using GlobalTrader2.Dto.PurchaseOrderEpr;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.CreateEpr;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Commands.CreateEpr;

public class CreateEprCommandValidatorTests
{
    private readonly CreateEprCommandValidator _validator;

    public CreateEprCommandValidatorTests()
    {
        _validator = new CreateEprCommandValidator();
    }

    [Fact]
    public void ValidModel_ShouldPassValidation()
    {
        // Arrange
        var request = new CreateEprRequest
        {
            PurchaseOrderNumber = 123456,
            CompanyName = "Valid Company",
            OrderValue = 1000.1234m,
            CurrencyCode = "USD",
            DeliveryDate = DateTime.Today,
            RaisedByDate = DateTime.Today,
            Name = "Ref Name",
            Address = "Some Address",
            Tel = "*********",
            Fax = "*********",
            Email = "<EMAIL>",
            Name1 = "Ref Name 1",
            Address1 = "Address 1",
            Tel1 = "*********",
            Fax1 = "*********",
            Email1 = "<EMAIL>",
            Name2 = "Ref Name 2",
            Address2 = "Address 2",
            Tel2 = "*********",
            Fax2 = "*********",
            Email2 = "<EMAIL>",
            Authorized = "Manager",
            SLTerms = "Net 30",
            PaymentAuthorizedBy = "Director",
            Countersigned = "CEO",
            SupplierCode = "SUP123"
        };

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = new CreateEprCommandValidator().TestValidate(command);

        // Assert
        result.ShouldNotHaveAnyValidationErrors();
    }

    [Theory]
    [InlineData(null)]
    public void PurchaseOrderNumber_ShouldNotBeNull(int? value)
    {
        // Arrange
        var request = GetValidRequest();
        request.PurchaseOrderNumber = value;

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.PurchaseOrderNumber);
    }

    [Fact]
    public void CompanyName_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.CompanyName = new string('a', 251);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.CompanyName);
    }

    [Theory]
    [InlineData(123.45678)]
    [InlineData(*********0*********.0)]
    [InlineData(-0.12345)]
    public void OrderValue_ShouldNotBeEmptyOrNull(decimal value)
    {
        // Arrange
        var request = GetValidRequest();
        request.OrderValue = value;

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.OrderValue);
    }

    [Fact]
    public void CurrencyCode_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.CurrencyCode = new string('a', 51);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.CurrencyCode);
    }

    [Fact]
    public void DeliveryDate_ShouldNotBeEmptyOrNull()
    {
        // Arrange
        var request = GetValidRequest();
        request.DeliveryDate = default;

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.DeliveryDate);
    }

    [Fact]
    public void Name_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Name = new string('a', 251);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Name);
    }

    [Fact]
    public void Address_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Address = new string('a', 501);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Address);
    }

    [Fact]
    public void Tel_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Tel = new string('a', 51);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Tel);
    }

    [Fact]
    public void Fax_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Fax = new string('a', 51);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Fax);
    }

    [Fact]
    public void Email_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Email = new string('a', 251);

        var command = new CreateEprCommand(request)
        {
            PurchaseOrderId = 1,
            UpdatedBy = 1
        };

        // Act
        var result = _validator.TestValidate(command);

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Email);
    }

    [Fact]
    public void Name1_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Name1 = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Name1);
    }

    [Fact]
    public void Address1_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Address1 = new string('a', 501);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Address1);
    }

    [Fact]
    public void Tel1_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Tel1 = new string('1', 51);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Tel1);
    }

    [Fact]
    public void Fax1_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Fax1 = new string('1', 51);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Fax1);
    }

    [Fact]
    public void Email1_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Email1 = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Email1);
    }

    [Fact]
    public void Name2_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Name2 = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Name2);
    }

    [Fact]
    public void Address2_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Address2 = new string('a', 501);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Address2);
    }

    [Fact]
    public void Tel2_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Tel2 = new string('1', 51);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Tel2);
    }

    [Fact]
    public void Fax2_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Fax2 = new string('1', 51);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Fax2);
    }

    [Fact]
    public void Email2_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Email2 = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Email2);
    }

    [Fact]
    public void RaisedByDate_ShouldNotBeEmptyOrNull()
    {
        // Arrange
        var request = GetValidRequest();
        request.RaisedByDate = default;

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.RaisedByDate);
    }

    [Fact]
    public void Authorized_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Authorized = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Authorized);
    }

    [Fact]
    public void SLTerms_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.SLTerms = new string('a', 501);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.SLTerms);
    }

    [Fact]
    public void PaymentAuthorizedBy_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.PaymentAuthorizedBy = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.PaymentAuthorizedBy);
    }

    [Fact]
    public void Countersigned_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.Countersigned = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.Countersigned);
    }

    [Fact]
    public void SupplierCode_ShouldRespectMaxLength()
    {
        // Arrange
        var request = GetValidRequest();
        request.SupplierCode = new string('a', 251);

        // Act
        var result = _validator.TestValidate(new CreateEprCommand(request));

        // Assert
        result.ShouldHaveValidationErrorFor(x => x.Data.SupplierCode);
    }

    private CreateEprRequest GetValidRequest()
    {
        return new CreateEprRequest
        {
            PurchaseOrderNumber = 123456,
            CompanyName = "Valid Company",
            OrderValue = 1000.1234m,
            CurrencyCode = "USD",
            DeliveryDate = DateTime.Today,
            RaisedByDate = DateTime.Today,
            Name = "Ref Name",
            Address = "Some Address",
            Tel = "*********",
            Fax = "*********",
            Email = "<EMAIL>",
            Name1 = "Ref Name 1",
            Address1 = "Address 1",
            Tel1 = "*********",
            Fax1 = "*********",
            Email1 = "<EMAIL>",
            Name2 = "Ref Name 2",
            Address2 = "Address 2",
            Tel2 = "*********",
            Fax2 = "*********",
            Email2 = "<EMAIL>",
            Authorized = "Manager",
            SLTerms = "Net 30",
            PaymentAuthorizedBy = "Director",
            Countersigned = "CEO",
            SupplierCode = "SUP123"
        };
    }
}
