﻿using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.CreateEpr;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Commands.CreateEpr;

public class CreateEprHandlerTests
{
    private readonly IFixture _fixture;
    private readonly Mock<IBaseRepository<CreateEprReadModel>> _mockRepository;
    private readonly CreateEprHandler _handler;

    public CreateEprHandlerTests()
    {
        _fixture = new Fixture().Customize(new AutoMoqCustomization());
        _fixture.Behaviors.Add(new OmitOnRecursionBehavior());

        _mockRepository = new Mock<IBaseRepository<CreateEprReadModel>>();
        _handler = new CreateEprHandler(_mockRepository.Object);
    }

    [Fact]
    public async Task Handle_CreateEpr_ShouldReturnSuccess()
    {
        // Arrange
        var command = _fixture.Build<CreateEprCommand>()
            .With(c => c.Updated<PERSON>y, 123)
            .With(c => c.PurchaseOrderId, 456)
            .Create();

        _mockRepository
            .Setup(repo => repo.SqlQueryRawReturnValueAsync(
                It.IsAny<string>(),
                It.IsAny<object[]>()))
            .ReturnsAsync(new CreateEprReadModel { EPRId = 999 });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.True(result.Success);
        Assert.Equal(999, result.Data);

        _mockRepository.Verify(repo => repo.SqlQueryRawReturnValueAsync(
            It.IsAny<string>(),
            It.IsAny<object[]>()), Times.Once);
    }

    [Fact]
    public async Task Handle_CreateEpr_ShouldReturnFailure_WhenNoRowsAffected()
    {
        // Arrange
        var command = _fixture.Build<CreateEprCommand>()
            .With(c => c.UpdatedBy, 123)
            .With(c => c.PurchaseOrderId, 456)
            .Create();

        _mockRepository
            .Setup(repo => repo.SqlQueryRawReturnValueAsync(
                It.IsAny<string>(),
                It.IsAny<object[]>()))
            .ReturnsAsync(new CreateEprReadModel { EPRId = 0 });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        Assert.NotNull(result);
        Assert.False(result.Success);
        Assert.Equal(0, result.Data);

        _mockRepository.Verify(repo => repo.SqlQueryRawReturnValueAsync(
            It.IsAny<string>(),
            It.IsAny<object[]>()), Times.Once);
    }
}
