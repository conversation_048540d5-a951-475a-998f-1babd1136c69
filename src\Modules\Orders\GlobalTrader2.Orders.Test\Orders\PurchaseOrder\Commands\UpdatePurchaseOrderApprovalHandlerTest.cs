﻿using GlobalTrader2.Core;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.StoreName;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.UpdatePurchaseOrderApproval;
using Microsoft.Data.SqlClient;
using Moq;
using System.Data;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.PurchaseOrder.Commands
{
    public class UpdatePurchaseOrderApprovalHandlerTest
    {
        private readonly Mock<IBaseRepository<AffectedRows>> _mockRepository;
        private readonly UpdatePurchaseOrderApprovalHandler _handler;

        public UpdatePurchaseOrderApprovalHandlerTest()
        {
            _mockRepository = new Mock<IBaseRepository<AffectedRows>>();
            _handler = new UpdatePurchaseOrderApprovalHandler(_mockRepository.Object);
        }

        [Fact]
        public async Task Handle_ShouldReturnSuccess_WhenRepositoryReturnsAffectedRowsGreaterThanZero()
        {
            // Arrange
            var command = new UpdatePurchaseOrderApprovalCommand
            {
                PurchaseOrderId = 123,
                ApprovedBy = 456,
                IsApprove = true
            };

            _mockRepository
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync((string sql, SqlParameter[] parameters) =>
                {
                    // Simulate stored procedure setting the output parameter
                    var outputParam = parameters.FirstOrDefault(p => p.ParameterName == "@AffectedRows");
                    if (outputParam != null)
                    {
                        outputParam.Value = 1;
                    }
                    return 1;
                });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.True(result.Success);
            Assert.True(result.Data);
            Assert.Equal("Purchase order approval updated successfully.", result.Message);

            _mockRepository.Verify(r => r.ExecuteSqlRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Update_PurchaseOrder_Approval)),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 4 &&
                    VerifyParameters(parameters, command))),
                Times.Once);
        }

        [Fact]
        public async Task Handle_ShouldReturnFailure_WhenRepositoryReturnsZeroAffectedRows()
        {
            // Arrange
            var command = new UpdatePurchaseOrderApprovalCommand
            {
                PurchaseOrderId = 789,
                ApprovedBy = 101,
                IsApprove = false
            };

            _mockRepository
                .Setup(r => r.ExecuteSqlRawAsync(
                    It.IsAny<string>(),
                    It.IsAny<SqlParameter[]>()))
                .ReturnsAsync((string sql, SqlParameter[] parameters) =>
                {
                    // Simulate stored procedure setting the output parameter to 0 (no rows affected)
                    var outputParam = parameters.FirstOrDefault(p => p.ParameterName == "@AffectedRows");
                    if (outputParam != null)
                    {
                        outputParam.Value = 0;
                    }
                    return 0;
                });

            // Act
            var result = await _handler.Handle(command, CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.False(result.Success);
            Assert.False(result.Data);
            Assert.Equal("Failed to update purchase order approval.", result.Message);

            _mockRepository.Verify(r => r.ExecuteSqlRawAsync(
                It.Is<string>(sql => sql.Contains(StoredProcedures.Update_PurchaseOrder_Approval)),
                It.Is<SqlParameter[]>(parameters =>
                    parameters.Length == 4 &&
                    VerifyParameters(parameters, command))),
                Times.Once);
        }

        private static bool VerifyParameters(SqlParameter[] parameters, UpdatePurchaseOrderApprovalCommand command)
        {
            var purchaseOrderIdParam = parameters.FirstOrDefault(p => p.ParameterName == "@PurchaseOrderId");
            var approvedByParam = parameters.FirstOrDefault(p => p.ParameterName == "@ApprovedBy");
            var isApproveParam = parameters.FirstOrDefault(p => p.ParameterName == "@Approve");
            var affectedRowsParam = parameters.FirstOrDefault(p => p.ParameterName == "@AffectedRows");

            return purchaseOrderIdParam != null &&
                   (int)purchaseOrderIdParam.Value == command.PurchaseOrderId &&
                   purchaseOrderIdParam.SqlDbType == SqlDbType.Int &&

                   approvedByParam != null &&
                   (int)approvedByParam.Value == command.ApprovedBy &&
                   approvedByParam.SqlDbType == SqlDbType.VarChar &&
                   approvedByParam.Size == 100 &&

                   isApproveParam != null &&
                   (bool)isApproveParam.Value == command.IsApprove &&
                   isApproveParam.SqlDbType == SqlDbType.Bit &&

                   affectedRowsParam != null &&
                   affectedRowsParam.SqlDbType == SqlDbType.Int &&
                   affectedRowsParam.Direction == ParameterDirection.Output;
        }
    }
}