﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Orders.UserCases.Commons.Mappings;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPowerAppTokenInfo;

namespace GlobalTrader2.Orders.UserCases.Test.Orders.SalesOrders.Queries
{
    public class GetPowerAppTokenInfoHandlerTest
    {
        private readonly Mock<IBaseRepository<PowerAppTokenInfoReadModel>> _repository;
        private readonly IMapper _mapper;
        private readonly GetPowerAppTokenInfoHandler _handler;

        public GetPowerAppTokenInfoHandlerTest()
        {
            _repository = new Mock<IBaseRepository<PowerAppTokenInfoReadModel>>();
            var mapperConfig = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile(new PowerAppTokenMapper());
            });
            _mapper = mapperConfig.CreateMapper();
            _handler = new GetPowerAppTokenInfoHandler(_repository.Object, _mapper);
        }

        [Fact]
        public async Task Handle_GetData_ShouldReturnNullValue() { 
            // Arrange
            _repository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<PowerAppTokenInfoReadModel>());

            // Act
            var result = await _handler.Handle(new GetPowerAppTokenInfoQuery(1, "Export Approval", true), CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.Null(result.Data);
            Assert.True(result.Success);
        }

        [Fact]
        public async Task Handle_GetData_ShouldReturnNotNullValue()
        {
            // Arrange
            _repository.Setup(x => x.SqlQueryRawAsync(It.IsAny<string>(), It.IsAny<object[]>()))
                .ReturnsAsync(new List<PowerAppTokenInfoReadModel>(){ 
                    new PowerAppTokenInfoReadModel(){ 
                    RequestId = 1,
                    TokenValue = "Token Value"
                    }
                });

            // Act
            var result = await _handler.Handle(new GetPowerAppTokenInfoQuery(1, "Export Approval", true), CancellationToken.None);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.Data);
            Assert.True(result.Success);
        }
    }
}
