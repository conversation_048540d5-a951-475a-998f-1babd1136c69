﻿using GlobalTrader2.Dto.PurchaseOrderEpr;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.CreateEpr;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprById;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprList;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprRejectedLog;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetNotifyEprMessageTemplate;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[Authorize]
[ApiController]
[Route("api/orders/purchase-orders/details/epr")]
public class PurchaseOrderEprController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly SessionManager _sessionManager;

    public PurchaseOrderEprController(
        IMediator mediator, 
        SessionManager sessionManager)
    {
        _mediator = mediator;
        _sessionManager = sessionManager;
    }

    [HttpGet("{eprNo}")]
    public async Task<IActionResult> GetEprById([FromRoute] int eprNo)
    {
        var result = await _mediator.Send(new GetEprByIdQuery(eprNo));

        return Ok(result);
    }

    [HttpGet("list")]
    public async Task<IActionResult> GetPurchaseOrderEprList([FromQuery] int poId, [FromQuery] string? orderBy)
    {
        var result = await _mediator.Send(new GetEprListQuery(poId, orderBy));

        return Ok(result);
    }

    [HttpGet("{eprNo}/rejected-log")]
    public async Task<IActionResult> GetPurchaseOrderEprRejectedLog([FromRoute] int eprNo)
    {
        var result = await _mediator.Send(new GetEprRejectedLogQuery(eprNo));

        return Ok(result);
    }

    [HttpGet("{eprId}/notification-template")]
    public async Task<IActionResult> GetEprNotificationTemplate(int eprId)
    {
        var result = await _mediator.Send(new GetNotifyEprMessageTemplateQuery
        {
            EprId = eprId,
            ClientName = _sessionManager.ClientName!,
        });

        return Ok(result);
    }

    [HttpPost("save")]
    [ValidateAntiForgeryToken]
    [ApiAuthorize(false, SecurityFunction.Orders_PurchaseOrder_EPR_Complete)]
    public async Task<IActionResult> CreateEpr([FromQuery] int purchaseOrderId, [FromBody] CreateEprRequest request)
    {
        var command = new CreateEprCommand(request);
        command.PurchaseOrderId = purchaseOrderId;
        command.UpdatedBy = UserId;

        var result = await _mediator.Send(command);

        return Ok(result);
    }
}
