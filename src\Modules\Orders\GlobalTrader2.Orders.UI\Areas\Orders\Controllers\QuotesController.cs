using GlobalTrader2.Aggregator.UseCases.Orders.Quote.GetQuoteMainInfoById;
using GlobalTrader2.Dto.Datatables;
using GlobalTrader2.Dto.Quote;
using GlobalTrader2.Dto.QuoteLine;
using GlobalTrader2.Dto.Sourcing;
using GlobalTrader2.Orders.UserCases.Orders.ItemSearch.GetItemsearchQuoteLine;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.AutoSearchQuotes;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetListQuoteForTab;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteById;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteForPage;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteIdByNumber;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineAll;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineClosed;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineDetails;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineOpen;
using GlobalTrader2.SharedUI;
using GlobalTrader2.SharedUI.Helper;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.UserAccount.UseCases.LoginManager.Queries.GetNameByLoginId;
using MediatR;
using Microsoft.Extensions.Localization;
using System.Globalization;
using GlobalTrader2.Aggregator.UseCases.Orders.Quote.GetQuoteLineDetailWithCalculation;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineClosed;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetQuoteLineAll;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Queries.GetListQuoteForTab;
using GlobalTrader2.Dto.Quote;
using GlobalTrader2.Orders.UserCases.Orders.Quotes.Commands.EditQuote;
using GlobalTrader2.UserAccount.UseCases.Security.Queries;
using Microsoft.AspNetCore.Http;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Controllers;

[ApiController]
[Authorize]
[Route("api/orders/quotes")]
public class QuotesController : ApiBaseController
{
    private readonly IMediator _mediator;
    private readonly SecurityManager _securityManager;
    private readonly IStringLocalizer<SharedUI.Misc> _miscLocalizer;
    private readonly SessionManager _sessionManager;

    public QuotesController(IMediator mediator, SecurityManager securityManager, IStringLocalizer<Misc> miscLocalizer, SessionManager sessionManager)
    {
        _mediator = mediator;
        _securityManager = securityManager;
        _miscLocalizer = miscLocalizer;
        _sessionManager = sessionManager;
    }

    [HttpPost("auto-search")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> AutoSearchQuotes([FromBody] AutoSearchQuotesRequest request)
    {
        var response = await _mediator.Send(new AutoSearchQuotesQuery
        {
            ClientNo = ClientId,
            Keyword = request.Keyword,
        });

        return new JsonResult(response);
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetQuoteById([FromRoute] int id)
    {
        var response = await _mediator.Send(new GetQuoteByIdQuery
        {
            QuoteId = id,
        });

        return new JsonResult(response);
    }

    [HttpGet("search-id")]
    public async Task<IActionResult> GetQuoteIdByNumber([FromQuery] int quoteNum)
    {
        return Ok(await _mediator.Send(new GetQuoteIdQuery()
        {
            ClientNo = ClientId,
            QuoteNum = quoteNum
        }));
    }

    [HttpPut("edit")]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> EditQuote([FromBody] EditQuoteRequestDto request)
    {
        var responseGetQuoteById = await _mediator.Send(new GetQuoteByIdQuery
        {
            QuoteId = request.QuoteId,
        });

        var canEditAS9120 = await CheckFunctionPermissions(UserId, IsGlobalLogin, new List<SecurityFunction>() { SecurityFunction.Orders_QuoteAndSO_Edit_AS9120 });

        var hasPermission = await CheckPermissionManual(SecurityFunction.Orders_Quote_MainInfo_Edit, IsGlobalLogin);

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        if (!canEditAS9120)
        {
            request.AS9120 = responseGetQuoteById.Data?.AS9120;
        }

        var command = new EditQuoteCommand
        {
            QuoteId = request.QuoteId,
            DateQuoted = request.DateQuoted,
            ContactNo = request.Contact,
            DivisionNo = request.DivisionNo,
            CurrencyNo = request.Currency,
            Freight = request.Freight,
            Salesman = request.Salesman,
            TermsNo = request.Terms,
            Notes = request.Notes?.Trim() ?? string.Empty,
            Instructions = request.Instructions?.Trim() ?? string.Empty,
            IncotermNo = request.Incoterm,
            UpdatedBy = UserId,
            AS9120 = request.AS9120,
            IsImportant = request.IsImportant ?? false,
            QuoteStatus = responseGetQuoteById.Data?.QuoteStatus ?? 0,
            SupportTeamMemberNo = request.SupportTeamMemberNo,
            DivisionHeaderNo = request.DivisionHeader,
            Closed = responseGetQuoteById.Data?.Closed ?? false,
        };

        var response = await _mediator.Send(command);
        return Ok(response);
    }

    [HttpGet("{quoteId}/main-info")]
    public async Task<IActionResult> GetQuoteMainInfo([FromRoute] int quoteId)
    {
        var response = await _mediator.Send(
            new GetQuoteMainInfoByIdQuery(
                quoteId,
                ClientId,
                ClientCurrencyId,
                ClientCurrencyCode,
                await _securityManager.CheckFunctionPermissions(UserId, false, [SecurityFunction.Orders_SalesOrder_AllowCheckedCompanyOnStop])
                )
            );
        if(response.Data is not null)
        {
            response.Data.LastUpdated = LocalizerHelper.FormatDLUP(response.Data.DLUP, response.Data.UpdatedByName, _miscLocalizer, CultureInfo.CurrentCulture);
        }
        return Ok(response);
    }

    [HttpGet("{quoteId}/for-page")]
    public async Task<IActionResult> GetQuoteForPage([FromRoute] int quoteId)
    {
        return Ok(await _mediator.Send(new GetQuoteForPageQuery(quoteId)));
    }

    [HttpPost("quote-line/item-search")]
    public async Task<IActionResult> GetItemsearchQuoteLine([FromBody] GetItemSearchQuoteLineRequest request, CancellationToken cancellationToken)
    {
        var query = new GetItemsearchQuoteLineQuery
        {
            PageIndex = request.Index,
            PageSize = request.Size,
            OrderBy = request.OrderBy,
            SortDir = request.SortDir,
            ClientId = ClientId,
            PartSearch = request.PartSearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.PartSearch) : null,
            CompanySearch = request.CompanySearch != null ? StringHelper.RemovePunctuationRetainingPercentSigns(request.CompanySearch) : null,
            IncludeClosed = request.IncludeClosed,
            DateQuotedFrom = request.DateQuotedFrom,
            DateQuotedTo = request.DateQuotedTo,
            QuoteNoLo = request.QuoteNoLo,
            QuoteNoHi = request.QuoteNoHi
        };

        var result = await _mediator.Send(query, cancellationToken);

        var totalItems = result.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var cultureInfo = new CultureInfo(Culture);

        foreach (var item in result.Data ?? Enumerable.Empty<GetItemsearchQuoteLineDto>())
        {
            item.FormatedPrice = Core.Helpers.Functions.FormatCurrency(item.Price ?? 0, cultureInfo, item.CurrencyCode ?? string.Empty, 5, false);
        }

        var response = new DatatableResponse<IEnumerable<GetItemsearchQuoteLineDto>>()
        {
            Success = result.Success,
            Data = result.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };

        return Ok(response);
    }

    [HttpGet("{quoteId}/quote-line-open")]
    public async Task<IActionResult> GetAllQuoteLineOpenForQuote([FromRoute] int quoteId)
    {
        return Ok(await _mediator.Send(new GetAllQuoteLineOpenForQuoteQuery(quoteId, ClientId, new CultureInfo(Culture))));
    }

    [HttpGet("{quoteId}/quote-line-closed")]
    public async Task<IActionResult> GetAllQuoteLineClosedForQuote([FromRoute] int quoteId)
    {
        return Ok(await _mediator.Send(new GetAllQuoteLineClosedForQuoteQuery(quoteId, ClientId, new CultureInfo(Culture))));
    }

    [HttpGet("{quoteId}/quote-line-all")]
    public async Task<IActionResult> GetAllQuoteLineForQuote([FromRoute] int quoteId)
    {
        return Ok(await _mediator.Send(new GetAllQuoteLineForQuoteQuery(quoteId, ClientId, new CultureInfo(Culture))));
    }

    [HttpGet("quote-line/{quoteLineId}")]
    public async Task<IActionResult> GetQuoteLineDetail([FromRoute] int quoteLineId)
    {
        return Ok(await _mediator.Send(new GetQuoteLineDetailWithCalculationQuery(quoteLineId, ClientId, new CultureInfo(Culture), IsPOHub)));
    }

    [HttpGet("quote-line/{quoteLineId}/for-new")]
    public async Task<IActionResult> GetQuoteLineForNew([FromRoute] int quoteLineId, CancellationToken cancellationToken)
    {
        var response = await _mediator.Send(new GetQuoteLineDetailsQuery(quoteLineId), cancellationToken);
        return Ok(response);
    }

    [HttpPost("quote-list")]
    public async Task<IActionResult> GetQuoteListForTab(GetQuoteListRequest request)
    {
        var hasPermission = await CheckPermissionManual(SecurityFunction.Orders_Quote_View, IsGlobalLogin);

        if (!hasPermission) return StatusCode(StatusCodes.Status403Forbidden);

        var query = new GetListQuoteForTabQuery()
        {
            ClientId = ClientId,
            LoginId = UserId,
            TeamId = LoginTeamID,
            DivisionId = LoginDivisionID,
            ClientCurrencyCode = ClientCurrencyCode,
            CultureInfo = _sessionManager.GetCurrentCulture(),
            AS6081 = request.AS6081,
            CMSearch = request.CMSearch,
            ContactSearch = request.ContactSearch,
            DateQuotedFrom = request.DateQuotedFrom,
            DateQuotedTo = request.DateQuotedTo,
            Important = request.Important,
            IncludeClosed = request.IncludeClosed,
            OrderBy = request.OrderBy,
            PageIndex = request.PageIndex,
            PageSize = request.PageSize,
            PartSearch = request.PartSearch,
            QuoteNoHi = request.QuoteNoHi,
            QuoteNoLo = request.QuoteNoLo,
            QuoteStatus = request.QuoteStatus,
            RecentOnly = request.RecentOnly,
            SalesmanSearch = request.SalesmanSearch,
            SelectedClientNo = request.SelectedClientNo,
            SelectedLoginID = request.SelectedLoginID,
            SortDir = request.SortDir,
            TotalHi = request.TotalHi,
            TotalLo = request.TotalLo,
            TotalProfitHi = request.TotalProfitHi,
            TotalProfitLo = request.TotalProfitLo,
        };

        var response = await _mediator.Send(query);

        var totalItems = response.Data?.FirstOrDefault()?.RowCnt ?? 0;

        var result = new DatatableResponse<IEnumerable<GetQuoteListDto>>()
        {
            Success = response.Success,
            Data = response.Data,
            RecordsTotal = totalItems,
            RecordsFiltered = totalItems,
            Draw = request.Draw
        };
        return new JsonResult(result);
    }

    private async Task<bool> CheckFunctionPermissions(int loginId, bool isDataOtherClient, List<SecurityFunction> permissions)
    {
        var checkFunctionPermissionsQuery = new CheckFunctionPermissionsQuery(loginId, isDataOtherClient, permissions.Select(p => (int)(object)p).ToList());
        var checkFunctionPermissionsResult = await _mediator.Send(checkFunctionPermissionsQuery);

        return checkFunctionPermissionsResult?.Data ?? false;
    }

    private async Task<bool> CheckPermissionManual(SecurityFunction securityFunction, bool isDataOtherClient = false)
    {
        return await CheckFunctionPermissions(UserId, isDataOtherClient, new List<SecurityFunction>() { securityFunction });
    }
}

public record AutoSearchQuotesRequest(string Keyword);
