﻿@using Microsoft.Extensions.Localization
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.PoQuote.Details
@using GlobalTrader2.SharedUI.Helper

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer

@model IndexModel

<div id="add-hubrfq-line-dialog" class="dialog-container" title="@_localizer["Add New HUBRFQ Lines"]" data-table-id="" style="display: none;">
    <form method="post" id="add-hubrfq-line-form">
        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="X-icon" />
            <div>
                <p>@_commonLocalizer["There were some problems with your form."]</p>
                <p>@_commonLocalizer["Please check below and try again."]</p>
                <p id="row-select-validation-message" class='d-none'></p>
            </div>
        </div>
    </form>

    <div class="">
        <div id="add-hubrfq-line-filter"></div>
        <div class="mt-2">
            <button class="btn btn-primary" id="search-hubrfq-line-btn" disabled>
                <img src="/img/icons/search-glass.svg" alt="search" width="18" height="18">
                <span class="lh-base">@_commonLocalizer["Search"]</span>
            </button>
        </div>
        <div id="add-hubrfq-line-table-wrapper">
            <table id="add-hubrfq-line-table" class="table simple-table display responsive">
                <tr>
                    <th></th>
                </tr>
            </table>
        </div>
    </div>
</div>

<script>
    const addHubrfqLinesLocalize = {
        addNewHUBRFQLinesTitle : '@_localizer["AddNewHUBRFQLinesTitle"]',
        message : '@_localizer["Message"]',
        reqNo : '@_localizer["ReqNo"]',
        partNo : '@_localizer["PartNo"]',
        bomName : '@_localizer["BOMName"]',
        hubrfq : '@_localizer["HUBRFQ"]',
        dateReceivedFrom : '@_localizer["DateReceivedFrom"]',
        dateReceivedTo : '@_localizer["DateReceivedTo"]',
        client : '@_localizer["Client"]',
        customerRequirementNumber : '@_localizer["CustomerRequirementNumber"]',
        companyName : '@_localizer["CompanyName"]',
        bomHeader : '@_localizer["BOMHeader"]',
        part : '@_localizer["Part"]',
        receivedDate : '@_localizer["ReceivedDate"]',
        quantity : '@_localizer["Quantity"]',
        price : '@_localizer["Price"]',
        as9120 : '@_localizer["AS9120"]',
    };
</script>