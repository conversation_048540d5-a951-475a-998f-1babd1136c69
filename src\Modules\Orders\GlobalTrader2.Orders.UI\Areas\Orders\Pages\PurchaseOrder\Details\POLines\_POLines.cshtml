﻿@using GlobalTrader2.Dto.PurchaseOrder
@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldSetLite
@using GlobalTrader2.SharedUI.Interfaces

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<SharedUI.Status> _statusLocalizer;

@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.Details.IndexModel

@{
    var sectionBoxClasses = HtmlHelperExtensions.GetSectionBoxClass();
    var headerClasses = HtmlHelperExtensions.GetHeaderClass();
    var contentClasses = HtmlHelperExtensions.GetContentClass();
}

<div id="lines-box" class="@sectionBoxClasses mb-3">
    <div class="@headerClasses">
        <span class="section-box-title">
            @_localizer["Lines"]
        </span>
        <div class="section-box-button-group">
            @if (!Model.LinesSectionViewModel.IsReadOnly)
            {
                <div class="d-flex flex-wrap gap-2">
                    @if (Model.LinesSectionViewModel.CanAdd)
                    {
                        <button class="btn btn-primary" id="lines-add-btn" disabled>
                            <img src="~/img/icons/plus.svg" alt="@_commonLocalizer["Add"]" />
                            <span>@_commonLocalizer["Add"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanEdit)
                    {
                        <button class="btn btn-primary" id="lines-edit-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_commonLocalizer["Edit"]" />
                            <span>@_commonLocalizer["Edit"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanPost)
                    {
                        <button class="btn btn-primary" id="lines-post-btn" disabled>
                            <img src="~/img/icons/clipboard-check.svg" alt="@_commonLocalizer["Post"]" />
                            <span>@_commonLocalizer["Post"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanPost)
                    {
                        <button class="btn btn-primary" id="lines-post-all-btn" disabled>
                            <img src="~/img/icons/clipboard-check.svg" alt="@_commonLocalizer["Post All"]" />
                            <span>@_commonLocalizer["Post All"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanUnpost)
                    {
                        <button class="btn btn-primary" id="lines-unpost-btn" disabled>
                            <img src="~/img/icons/clipboard-cross.svg" alt="@_commonLocalizer["Unpost"]" />
                            <span>@_commonLocalizer["Unpost"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanUnpost)
                    {
                        <button class="btn btn-primary" id="lines-unpost-all-btn" disabled>
                            <img src="~/img/icons/clipboard-cross.svg" alt="@_commonLocalizer["Unpost All"]" />
                            <span>@_commonLocalizer["Unpost All"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanDelete)
                    {
                        <button class="btn btn-danger" id="lines-delete-btn" disabled>
                            <img src="~/img/icons/trash-can.svg" alt="@_commonLocalizer["Delete"]" />
                            <span>@_commonLocalizer["Delete"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanAllocate)
                    {
                        <button class="btn btn-primary" id="lines-allocate-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_commonLocalizer["Allocate"]" />
                            <span>@_commonLocalizer["Allocate"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanClose)
                    {
                        <button class="btn btn-danger" id="lines-close-btn" disabled>
                            <img src="~/img/icons/slash.svg" alt="@_commonLocalizer["Close"]" />
                            <span>@_commonLocalizer["Close"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanViewEpr)
                    {
                        <div class="epr-menu dropdown" id="po-lines-epr-menu-dropdown-container">
                            <button id="manage-epr-list-button"
                                    class="btn btn-primary dropdown-toggle" data-bs-toggle="dropdown" disabled>
                                <img src="~/img/icons/printer.svg" alt="Add icon" width="18" height="18"
                                     style="filter: brightness(0) invert(1); " />

                                <span class="lh-base">@_localizer["EPR"]</span>
                            </button>

                            <button class="d-none" id="open-notify-epr"></button>

                            <ul id="po-lines-epr-menu" class="dropdown-menu text-center" aria-labelledby="view-epr-button" style="z-index: 200;"></ul>
                        </div>
                    }

                    @if (Model.LinesSectionViewModel.CanCreateIpo)
                    {
                        <button class="btn btn-primary" id="lines-create-ipo-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Create IPO"]" />
                            <span>@_localizer["Create IPO"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanCloneThisLine)
                    {
                        <button class="btn btn-primary" id="lines-clone-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Clone this line"]" />
                            <span>@_localizer["Clone this line"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanConfirm)
                    {
                        <button class="btn btn-primary" id="lines-confirm-btn" disabled>
                            <img src="~/img/icons/check-circle.svg" alt="@_commonLocalizer["Confirm"]" />
                            <span>@_commonLocalizer["Confirm"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanConfirmAll)
                    {
                        <button class="btn btn-primary" id="lines-confirm-all-btn" disabled>
                            <img src="~/img/icons/check-circle.svg" alt="@_commonLocalizer["Confirm All"]" />
                            <span>@_commonLocalizer["Confirm All"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanEditAll)
                    {
                        <button class="btn btn-primary" id="lines-edit-all-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["Edit-All"]" />
                            <span>@_localizer["Edit-All"]</span>
                        </button>
                    }

                    @if (Model.LinesSectionViewModel.CanEccnLog)
                    {
                        <button class="btn btn-primary" id="lines-eccn-log-btn" disabled>
                            <img src="~/img/icons/edit-3.svg" alt="@_localizer["ECCN Log"]" />
                            <span>@_localizer["ECCN Log"]</span>
                        </button>
                    }
                </div>
            }
        </div>
    </div>
    <div class="@contentClasses" style="display: none">
        <div id="lines-wrapper">
            <div id="warning-section" class="alert alert-warning d-flex align-items-center gap-2 d-none" role="alert">
                <span>
                    <img width="18" height="18" src="/img/icons/triangle-exclamation-solid.svg"
                         alt="triangle exclamation solid icon">
                </span>
                <span id="warning-section-message"></span>
            </div>
            <div class="d-flex justify-content-between align-items-start border-bottom">
                <div id="nav-tabs-wrapper" role="tablist">
                    <ul class="nav nav-tabs border-0 justify-content-start" id="lines-tabs">
                        <li class="nav-item">
                            <button id="lines-all-tab" class="nav-link active" data-bs-toggle="tab" data-bs-target="#lines-table"
                                    type="button" role="tab" aria-controls="all" aria-selected="true"
                                    data-view-level="@((int)LineSectionTab.all)">
                                @_commonLocalizer["All"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="lines-open-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#lines-table"
                                    type="button" role="tab" aria-controls="open" aria-selected="false"
                                    data-view-level="@((int)LineSectionTab.open)">
                                @_commonLocalizer["Open"]
                            </button>
                        </li>
                        <li class="nav-item">
                            <button id="lines-closed-tab" class="nav-link" data-bs-toggle="tab" data-bs-target="#lines-table"
                                    type="button" role="tab" aria-controls="closed" aria-selected="false"
                                    data-view-level="@((int)LineSectionTab.closed)">
                                @_commonLocalizer["Closed"]
                            </button>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="tab-content">
                <div class="tab-pane fade active show" id="lines-table" role="tabpanel" aria-labelledby="lines-all-tab">
                    <div class="table-container">
                        <div id="lines-all-wrapper" class="position-relative">
                            <table id="po-lines-tbl" class="table simple-table display responsive nowrap">
                                <tr>
                                    <th></th>
                                </tr>
                            </table>
                            <div class="d-flex flex-row-reverse gap-3 p-2" style="background-color: #EEEEEE;">
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Total"]</span>

                                    <span id="lines-total-value" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["Tax"]</span>

                                    <span id="lines-tax-value" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                                <div>
                                    <span class="form-label" style="color: #999999; font-weight: bold;">@_localizer["SubTotal"]</span>

                                    <span id="lines-subtotal-value" class="border rounded-2 p-1" style="background-color: #ffffff;"></span>
                                </div>
                            </div>
                            @await Html.PartialAsync("POLines/_POLineDetail", LineSectionTab.all.ToString())
                            @await Component.InvokeAsync(nameof(CollapsibleFieldSetLite), Model.AllocationsForLineViewModel)
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@await Html.PartialAsync("POLines/_PostUnpostLines", Model)

<script>
    const linesResource = {
        lineNo: '@_localizer["Line No"]',
        partNo: '@_localizer["Part No"]',
        supplierPart: '@_localizer["Supplier Part"]',
        customerPart: '@_localizer["Customer Part"]',
        mfr: '@_localizer["Mfr"]',
        dc: '@_localizer["DC"]',
        product: '@_localizer["Product"]',
        package: '@_localizer["Package"]',
        qtyOrdered: '@_localizer["Qty Ordered"]',
        shipped: '@_localizer["Qty Shipped"]',
        qtyAllocated: '@_localizer["Qty Allocated"]',
        qtyOutstanding: '@_localizer["Qty Outstanding"]',
        qtyReceived: '@_localizer["Qty Received"]',
        backOrder: '@_localizer["Qty BackOrder"]',
        unitPrice: '@_localizer["Unit Price"]',
        total: '@_localizer["Total"]',
        tax: '@_localizer["Tax"]',
        dateConfirmed: '@_localizer["Date Confirmed"]',
        asRequired: '@_localizer["AS6081 Required?"]',
        reason: '@_localizer["Reason"]',
        promiseDate: '@_localizer["Promise Date"]',
        by: '@_localizer["By"]',
        date: '@_localizer["Date"]',
        noShippedLineData: '@_localizer["This Sales Order Line has no shipped lines"]',
        shippedBy: '@_localizer["Shipped By"]',
        invoiceNo: '@_localizer["Invoice No"]',
        deliveryDate: '@_localizer["Delivery Date"]'
    }

    const allocationsLocalizedStrings = {
        partNo: '@_localizer["Part No"]',
        customerPart: '@_localizer["Customer Part"]',
        customer: '@_localizer["Customer"]',
        customerPo: '@_localizer["Customer PO"]',
        so: '@_localizer["SO"]',
        lineNo: '@_localizer["Line No"]',
        promised: '@_localizer["Promised"]',
        srma: '@_localizer["SRMA"]',
        returnDate: '@_localizer["Return Date"]',
        quantity: '@_localizer["Quantity"]',
        price: '@_localizer["SO Unit Price"]',
        dc: '@_localizer["DC"]',
        deliveryDate: '@_localizer["Delivery Date"]',
        margin: '@_localizer["Margin"]',
        marginValue: '@_localizer["Margin Value"]',
        priceToClient: '@_localizer["Total Price to Client"]',
        profit: '@_localizer["Client Profit"]',
        sellExchangeRate: '@_localizer["Sell Exchange Rate"]',
        exchangeRate: '@_localizer["Buy Exchange Rate"]',
        noDataMessage: '@_localizer["Sorry, no data was found"]',
    }
</script>

@{
    var notifyViewModel = new NotifyEmailModel()
    {
        ContainerId = "po-notify-epr-dialog",
        FormId = "po-notify-epr-form",
        Title = _commonLocalizer["EPR Notify"],
        Description = _commonLocalizer["EPR NOTIFY"]
    };
}

@await Html.PartialAsync("Partials/_NotifyEPRDialog", notifyViewModel)
