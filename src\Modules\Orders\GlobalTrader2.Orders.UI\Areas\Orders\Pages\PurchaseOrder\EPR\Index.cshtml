@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.EPR
@using GlobalTrader2.SharedUI
@using GlobalTrader2.Core.Helpers
@model IndexModel

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject SessionManager _sessionManager;
@inject SettingManager _settingManager;

@{
    Layout = "_FormLayout";
    ViewData["Title"] = "Early Payment Request";

    var epr = Model.EprDetails;
    var po = Model.PurchaseOrderDetails;
    var eprRejectedLog = Model.EprRejectedLog;

    // Infomation
    var isNew = epr?.IsNew ?? false;
    var supplierCode = epr?.SupplierCode ?? po?.SupplierCode ?? "";
    var companyName = epr?.CompanyName ?? po?.CompanyName ?? "";
    var companyType = epr?.CompanyType ?? po?.CompanyType ?? "";
    var purchaseOrderNumber = epr?.PurchaseOrderNumber ?? po?.PurchaseOrderNumber;
    var polineIds = (epr is null, string.IsNullOrWhiteSpace(Model.PurchaseOrderLineIdsParams)) switch
    {
        (true, false) => Model.PurchaseOrderLineIdsParams,
        _ => null
    };
    var pols = (epr is null, string.IsNullOrWhiteSpace(Model.PurchaseOrderLinesSerialNoParams)) switch
    {
        (true, false) => Model.PurchaseOrderLinesSerialNoParams,
        (false, _) => epr?.POLineSerialNo,
        _ => null
    };
    var orderValue = epr?.OrderValue.ToString("0.####") ?? "";
    var currency = epr?.CurrencyCode ?? po?.CurrencyCode ?? "";
    var deliveryDate = epr is not null ? Functions.FormatDate(epr.DeliveryDate) : null;
    var inAdvance = epr?.InAdvance ?? false;
    var uponReceipt = epr?.UponReceipt ?? false;
    var netSpecify = epr?.NetSpecify?.ToString() ?? "";
    var otherSpecify = epr?.OtherSpecify ?? "";
    var tt = epr?.TT ?? false;
    var cheque = epr?.Cheque ?? false;
    var creditCard = epr?.CreditCard ?? false;
    var paymentMethodComment = epr?.Comments ?? "";

    // References information
    var name = epr?.Name ?? "";
    var address = epr?.Address ?? "";
    var tel = epr?.Tel ?? "";
    var fax = epr?.Fax ?? "";
    var email = epr?.Email ?? "";
    var name1 = epr?.Name1 ?? "";
    var address1 = epr?.Address1 ?? "";
    var tel1 = epr?.Tel1 ?? "";
    var fax1 = epr?.Fax1 ?? "";
    var email1 = epr?.Email1 ?? "";
    var name2 = epr?.Name2 ?? "";
    var address2 = epr?.Address2 ?? "";
    var tel2 = epr?.Tel2 ?? "";
    var fax2 = epr?.Fax2 ?? "";
    var email2 = epr?.Email2 ?? "";
    var referenceComment = epr?.Comment ?? "";

    var proFormaAttached = epr?.ProFormaAttached ?? false;
    var raisedByNo = epr?.RaisedByNo ?? _sessionManager.LoginID;
    var raisedByDate = epr is not null ? Functions.FormatDate(epr?.RaisedByDate) : null;
    var sorSigned = epr?.SORSigned ?? false;
    var forStock = epr?.ForStock ?? false;
    var valuesCorrect = epr?.ValuesCorrect ?? false;
    var authorizedBy = epr?.Authorized ?? "";
    var authorizedDate = (epr is not null && epr.AuthorizedDate is not null) ? Functions.FormatDate(epr.AuthorizedDate) : "";

    // Account only information
    var eraiMember = epr?.ERAIMember ?? null;
    var eraiReported = epr?.ERAIReported ?? null;
    var debitNotes = epr?.DebitNotes ?? false;
    var apOpenOrder = epr?.APOpenOrders ?? false;
    var totalValue = epr?.ACTotalValue.ToString("0.####") ?? "";
    var totalValue1 = epr?.ACTotalValue1.ToString("0.####") ?? "";
    var slComments = epr?.SLComment ?? "";
    var slTerms = epr?.SLTerms ?? "";
    var slOverdue = epr?.SLOverdue ?? false;
    var slTotalValue = epr?.SLTotalValue.ToString("0.####") ?? "";
    var paymentAuthorizedBy = epr?.EPRCompletedBy ?? "";
    var counterSigned = epr?.Countersigned ?? "";
    var paymentAuthorizedDate = (epr is not null && epr.PaymentAuthorizedDate is not null) ? Functions.FormatDate(epr?.PaymentAuthorizedDate) : "";
    var eprCompletedByNo = epr?.EPRCompletedByNo;

    var hasPermission = Model.HasPermission;
    var canAuthorize = Model.EprViewModel.CanAuthorise && string.IsNullOrEmpty(epr?.Authorized);
    var hasCompletePermission = Model.EprViewModel.CanComplete;
    var canComplete = Model.EprViewModel.CanComplete &&
                      epr is not null &&
                      !string.IsNullOrEmpty(epr.Authorized) &&
                      !epr.EPRCompletedByNo.HasValue;
    var canDelete = epr is not null && Model.EprViewModel.CanDelete;

    var showRejectedLog = eprRejectedLog?.EPRTotalRejectCounts > 0;
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
    <link type="text/css" rel="stylesheet" href="~/css/date-picker.css" asp-append-version="true" />
}

@if (Model.HasPermission)
{
    <div class="mx-5">
        <form method="post" id="epr-form" enctype="application/x-www-form-urlencoded">
            <section class="row">
                <div class="col-4 text-center">
                    <img src="/img/rebound/ReboundLogo.jpg" alt="reboundLogo" />
                </div>
                <div class="col-4 d-flex justify-content-center align-items-center fw-bold fs-5">
                    @_localizer["EarlyPaymentRequest"]
                </div>
                <div style="flex-direction: column" class="col-4 d-flex justify-content-center align-items-center fw-bold fs-5">
                    <span>
                        (@_localizer["SubDayPayment"])
                    </span>
                    <span>
                        @supplierCode
                    </span>
                    <input type="hidden" name="SupplierCode" id="SupplierCode" value="@supplierCode">
                </div>
            </section>
            <section class="d-flex">
                <div class="col-4 pe-1">
                    <table style="height: 246px">
                        <thead>
                            <tr>
                                <th colspan="2">
                                    @_localizer["OrderDetails"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    <div class="row">
                                        <span class="col-6">@_localizer["Supplier"]</span>
                                        <div class="d-flex col-6 gap-1">
                                            <p class="m-0">@_localizer["New"]</p>
                                            <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKNewSupplier" id="ChKNewSupplier"
                                                   checked="@isNew" disabled="@(!hasCompletePermission)">
                                        </div>
                                        <span class="col-12">
                                            <span>
                                                @companyName
                                            </span>
                                            <input type="hidden" name="CompanyName" id="CompanyName" value="@companyName">
                                            @if (!string.IsNullOrEmpty(companyType))
                                            {
                                                <span>
                                                    (@companyType)
                                                </span>
                                            }
                                        </span>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["PoNumber"]
                                </td>
                                <td>
                                    @purchaseOrderNumber
                                    <input type="hidden" name="PurchaseOrderNumber" id="PurchaseOrderNumber" value="@purchaseOrderNumber">
                                    <input type="hidden" name="PurchaseOrderId" id="PurchaseOrderId" value="@Model.PurchaseOrderId">

                                    @if (pols is not null)
                                    {
                                        <span>( @pols )</span>
                                        <input type="hidden" name="POLineIds" id="POLineIds" value="@polineIds">
                                    }
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["ValueAndCurrency"]
                                </td>
                                <td>
                                    <div class="d-flex col-6 gap-1">
                                        <input class="form-control form-input" name="OrderValue" id="OrderValue"
                                               value="@orderValue" disabled="@(!hasCompletePermission)">
                                        <p class="m-0">@currency</p>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["DeliveryDate"]
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <input class="form-control form-input mt-0" style="height: 28px !important" type="text" name="DeliveryDate" id="DeliveryDate" data-input-type="DATE"
                                               value="@deliveryDate" disabled="@(!hasCompletePermission)">
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-4 px-1">
                    <table>
                        <thead>
                            <tr>
                                <th colspan="2">
                                    @_localizer["PaymentTerms"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @_localizer["InAdvance"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKInAdvance" id="ChKInAdvance"
                                           checked="@inAdvance" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["UponReceipt"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKUponReceipt" id="ChKUponReceipt"
                                           checked="@uponReceipt" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["Net"] (@_localizer["Specify"])
                                </td>
                                <td>
                                    <div class="d-flex col-6 gap-1">
                                        <input class="form-control form-input" name="Net" id="Net" maxlength="5"
                                               value="@netSpecify">
                                        <strong class="m-0">@_localizer["Days"]</strong>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <div class="form-control-wrapper">
                                        <label for="OtherSpecify" class="form-label">
                                            @_localizer["Other"] (@_localizer["Specify"])
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="OtherSpecify" name="OtherSpecify" rows="4" cols="38"
                                              disabled="@(!hasCompletePermission)">@otherSpecify</textarea>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-4 ps-1">
                    <table>
                        <thead>
                            <tr>
                                <th colspan="2">
                                    @_localizer["PaymentMethod"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @_localizer["TelegraphicTransfer"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKTelegraphicTransfer" id="ChKTelegraphicTransfer"
                                           checked="@tt" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["Cheque"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKCheque" id="ChKCheque"
                                           checked="@cheque" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["CreditCard"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKCreditCard" id="ChKCreditCard"
                                           checked="@creditCard" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    <div class="form-control-wrapper">
                                        <label for="Comment" class="form-label">
                                            @_localizer["Comment"]
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="Comment" name="Comment" rows="4" cols="40"
                                              disabled="@(!hasCompletePermission)">@paymentMethodComment</textarea>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <h1 class="text-center fs-5 fw-bold my-3">
                ------ @_localizer["References"] --------
            </h1>

            <section class="d-flex">
                <div class="col-6 pe-1">
                    <table>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Name"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Name" id="Name" value="@name" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="form-control-wrapper">
                                        <label for="Comment" class="form-label">
                                            @_localizer["Address"]
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="Address" name="Address" rows="2" cols="64"
                                              disabled="@(!hasCompletePermission)">@address</textarea>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-1">
                                    @_localizer["Tel"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Tel" id="Tel" value="@tel" disabled="@(!hasCompletePermission)">
                                </td>
                                <td class="col-1">
                                    @_localizer["Fax"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Fax" id="Fax" value="@fax" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Email"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Email" id="Email" type="email" value="@email" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <th colspan="4" scope="colgroup"></th>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Name"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Name1" id="Name1" value="@name1" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="form-control-wrapper">
                                        <label for="Comment" class="form-label">
                                            @_localizer["Address"]
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="Address1" name="Address1" rows="2" cols="64"
                                              disabled="@(!hasCompletePermission)">@address1</textarea>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-1">
                                    @_localizer["Tel"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Tel1" id="Tel1" value="@tel1" disabled="@(!hasCompletePermission)">
                                </td>
                                <td class="col-1">
                                    @_localizer["Fax"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Fax1" id="Fax1" value="@fax1" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Email"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Email1" id="Email1" type="email" value="@email1" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="col-6 ps-1">
                    <table>
                        <tbody>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Name"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Name2" id="Name2" value="@name2" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="form-control-wrapper">
                                        <label for="Comment" class="form-label">
                                            @_localizer["Address"]
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="Address2" name="Address2" rows="2" cols="64"
                                              disabled="@(!hasCompletePermission)">@address2</textarea>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="col-1">
                                    @_localizer["Tel"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Tel2" id="Tel2" value="@tel2" disabled="@(!hasCompletePermission)">
                                </td>
                                <td class="col-1">
                                    @_localizer["Fax"]
                                </td>
                                <td class="col-5">
                                    <input class="form-control form-input" name="Fax2" id="Fax2" value="@fax2" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    @_localizer["Email"]
                                </td>
                                <td colspan="2">
                                    <input class="form-control form-input" name="Email2" id="Email2" type="email" value="@email2" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <th colspan="4" scope="colgroup"></th>
                            </tr>
                            <tr style="height: 181px">
                                <td colspan="4" rowspan="4">
                                    <div class="form-control-wrapper">
                                        <label for="Comment" class="form-label">
                                            @_localizer["Comment"]
                                        </label>
                                        <textarea class="form-control form-textarea height-auto" id="Comment2" name="Comment2" rows="7" cols="64"
                                              disabled="@(!hasCompletePermission)">@referenceComment</textarea>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <section class="d-flex mt-2">
                <div class="col-5 pe-1">
                    <table>
                        <thead>
                            <tr>
                                <th colspan="2">
                                    @_localizer["Sales"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @_localizer["ProFormaAttached"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKProFormaAttached" id="ChKProFormaAttached" checked="@proFormaAttached" disabled="@(!hasCompletePermission)">
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["RaisedBy"]
                                </td>
                                <td>
                                    <select id="CmBRaisedBy" name="CmBRaisedBy" data-raised-by-no="@raisedByNo" aria-label="Raised by select" class="form-control mt-0" disabled="@(!hasCompletePermission)"></select>
                                </td>
                            </tr>
                            <tr>
                                <td>
                                    @_localizer["Date"]
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        <input name="RaisedDate" id="RaisedDate" class="form-control form-input mt-0" style="height: 28px !important" type="text" data-input-type="DATE" value="@raisedByDate" disabled="@(!hasCompletePermission)">
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="col-7 ps-1">
                    <table>
                        <thead>
                            <tr>
                                <th colspan="6">
                                    @_localizer["Manager"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    @_localizer["SORSigned"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKSORSigned" id="ChKSORSigned" checked="@sorSigned">
                                </td>
                                <td>
                                    @_localizer["FORStock"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKFORStock" id="ChKFORStock" checked="@forStock">
                                </td>
                                <td>
                                    @_localizer["ValuesCorrect"]
                                </td>
                                <td>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKValuesCorrect" id="ChKValuesCorrect" checked="@valuesCorrect">
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    @_localizer["Authorized"]
                                </td>
                                <td colspan="2">
                                    @_localizer["Date"]
                                </td>
                            </tr>
                            <tr>
                                <td colspan="4">
                                    <div class="d-flex align-items-center justify-content-between">
                                        <p class="m-0">@authorizedBy</p>
                                        @if (canAuthorize)
                                        {
                                            <button class="btn btn-primary">
                                                <span class="lh-base">@_localizer["Authorise"]</span>
                                            </button>
                                        }
                                    </div>
                                </td>
                                <td colspan="2">@authorizedDate</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            <h1 class="text-center fs-5 fw-bold my-3">
                ----- @_localizer["AccountsOnly"] --------
            </h1>

            <section class="d-flex">
                <div class="col-4 pe-1">
                    <table>
                        <tr>
                            <th class="fw-normal">@_localizer["ERAIMember"]</th>
                            <td>
                                <div class="d-flex gap-1">
                                    <p class="m-0">@_localizer["Yes"]</p>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKERAIMemberYes" id="ChKERAIMemberYes" checked="@eraiMember" disabled="@(!hasCompletePermission)">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <p class="m-0">@_localizer["No"]</p>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKERAIMemberNo" id="ChKERAIMemberNo" checked="@(!eraiMember)" disabled="@(!hasCompletePermission)">
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <th class="fw-normal">@_localizer["ERAIReported"]</th>
                            <td>
                                <div class="d-flex gap-1">
                                    <p class="m-0">@_localizer["Yes"]</p>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKERAIReportedYes" id="ChKERAIReportedYes" checked="@eraiReported" disabled="@(!hasCompletePermission)">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <p class="m-0">@_localizer["No"]</p>
                                    <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKERAIReportedNo" id="ChKERAIReportedNo" checked="@(!eraiReported)" disabled="@(!hasCompletePermission)">
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-8 ps-1">
                    <table>
                        <tr>
                            <th class="fw-normal" scope="col">
                                @_localizer["OutStandingDebitNotes"]
                            </th>
                            <td>
                                <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKOutStandingDebit" id="ChKOutStandingDebit" checked="@debitNotes" disabled="@(!hasCompletePermission)">
                            </td>
                            <th class="fw-normal" scope="col">@_localizer["TotalValue"]</th>
                            <td>
                                <input class="form-control form-input" name="OutStandingDebitTotalValue" id="OutStandingDebitTotalValue" value="@totalValue" disabled="@(!hasCompletePermission)">
                            </td>
                        </tr>
                        <tr>
                            <th class="fw-normal" scope="col">
                                @_localizer["AdvancePayment"]
                            </th>
                            <td>
                                <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKAdvancePayment" id="ChKAdvancePayment" checked="@apOpenOrder" disabled="@(!hasCompletePermission)">
                            </td>
                            <th class="fw-normal" scope="col">@_localizer["TotalValue"]</th>
                            <td>
                                <input class="form-control form-input" name="AdvancePaymentTotalValue" id="AdvancePaymentTotalValue" value="@totalValue1" disabled="@(!hasCompletePermission)">
                            </td>
                        </tr>
                    </table>
                </div>
            </section>
            <section class="d-flex mt-2">
                <div class="col-4 pe-1">
                    <table>
                        <tr>
                            <th class="fw-normal">
                                @_localizer["Comments"]
                            </th>
                            <td>
                                <textarea class="form-control form-textarea height-auto" id="Comments" name="Comments" rows="3" cols="64"
                                      disabled="@(!hasCompletePermission)">@slComments</textarea>
                            </td>
                        </tr>
                    </table>
                </div>
                <div class="col-8 ps-1">
                    <table>
                        <tr>
                            <th class="fw-normal" scope="col">
                                @_localizer["SalesLedgerTerms"]
                            </th>
                            <td colspan="3">
                                <input class="form-control form-input" name="SalesLedgerTerms" id="SalesLedgerTerms" value="@slTerms" disabled="@(!hasCompletePermission)" disabled="@(!hasCompletePermission)">
                            </td>
                        </tr>
                        <tr>
                            <th class="fw-normal" scope="col">
                                @_localizer["SalesLedgerOverdue"]
                            </th>
                            <td>
                                <input type="checkbox" class="form-check-input form-control mt-0 p-0" name="ChKSalesLedgerOverdue"
                                       id="ChKSalesLedgerOverdue" checked="@slOverdue" disabled="@(!hasCompletePermission)">
                            </td>
                            <th class="fw-normal" scope="col">@_localizer["TotalValue"]</th>
                            <td>
                                <input class="form-control form-input" name="SalesLedgerOverdueTotalValue"
                                       id="SalesLedgerOverdueTotalValue" value="@slTotalValue" disabled="@(!hasCompletePermission)">
                            </td>
                        </tr>
                    </table>
                </div>
            </section>
            <section class="d-flex mt-2">
                <div class="col-12">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    @_localizer["PaymentAuthorizedBy"]
                                </th>
                                <th>
                                    @_localizer["Countersigned"] (@_localizer["WhereApplicable"])
                                </th>
                                <th>
                                    @_localizer["Date"]
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center justify-content-between">
                                        <p class="m-0">@paymentAuthorizedBy</p>
                                        <input type="hidden" name="PaymentAuthorizedBy" id="PaymentAuthorizedBy" value="@paymentAuthorizedBy">
                                        <input type="hidden" name="EPRCompletedByNo" id="EPRCompletedByNo" value="@eprCompletedByNo">
                                        @if (@canComplete)
                                        {
                                            <button class="btn btn-primary">
                                                <span class="lh-base">@_localizer["Complete"]</span>
                                            </button>
                                        }
                                    </div>
                                </td>
                                <td>
                                    <input class="form-control form-input" name="CounterSignedValue" id="CounterSignedValue"
                                           value="@counterSigned" disabled="@(!hasCompletePermission)">
                                </td>
                                <input type="hidden" name="PaymentAuthorizedDate" id="PaymentAuthorizedDate" value="@paymentAuthorizedDate">
                                <td>@paymentAuthorizedDate</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>

            @if (epr is not null)
            {
                <section class="d-flex mt-2">
                    <div class="col-12">
                        <table>
                            <tbody>
                                <tr>
                                    <th class="d-none"></th>
                                </tr>
                                <tr>
                                    <td class="col-10">
                                        <textarea class="form-control form-textarea height-auto" name="RejectEprReason" id="RejectEprReason" rows="2" color="20" placeholder="Enter Reason of EPR Rejection"></textarea>

                                        @if (showRejectedLog)
                                        {
                                            <span style="padding-top:10px">
                                                <span style="font-weight:bold">No. Of Times EPR Rejected: </span>
                                                <span style="color: red !important;font-weight:bold">@eprRejectedLog?.EPRTotalRejectCounts</span>
                                                <span>
                                                    (@_localizer["FollowThe"]
                                                    <button style="color:#0000ff;text-decoration:underline;padding:0;margin:0"
                                                            id="epr-rejected-log-view-button"
                                                            class="link-button text-break text-wrap text-start">
                                                        <span class="lh-base">logs</span>
                                                    </button>
                                                    @_localizer["ForMoreDetails"])
                                                </span>
                                            </span>
                                        }
                                    </td>
                                    <td class="col-2">
                                        <div class="d-flex align-items-center justify-content-center">
                                            <button style="width: 120px; height: 28px; justify-content: center"
                                                    class="btn btn-primary">
                                                <span class="lh-base">@_localizer["RejectEPR"]</span>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
            }

            @if (showRejectedLog)
            {
                <section class="d-flex mt-2">
                    <span style="font-size: 12px;font-weight:bold">@_localizer["LastEPRRejectedDetails"]:</span>
                    &nbsp;
                    <span style="font-size: 12px;">@(eprRejectedLog?.EPRLastRejectedDetails?.Split("||")[1])</span>
                </section>
            }

            <footer class="my-3">
                <div class="my-3">
                    <div class="d-flex justify-content-center gap-2">
                        <button class="btn btn-primary" id="SaveAndSend">
                            <span class="lh-base">@_localizer["SaveAndSend"]</span>
                        </button>
                        <button class="btn btn-primary" id="SaveAndPrint">
                            <span class="lh-base">@_localizer["SaveAndPrint"]</span>
                        </button>
                        @if (canDelete)
                        {
                            <button class="btn btn-primary" id="SaveAndDelete">
                                <span class="lh-base">@_localizer["DeleteEPR"]</span>
                            </button>
                        }
                    </div>
                </div>

                <div class="my-3 d-none" id="required-error-message">
                    <div style="border: 0; text-align: center; vertical-align: top;">
                        <span style="color: red; display: block; width: 100%;font-weight:bold">
                            There were some problems with your form. Please check above and try again.
                        </span>
                    </div>
                </div>
            </footer>
            @Html.AntiForgeryToken()
        </form>
    </div>

    <script>
        const hasPermission = true;

        const pagePermissions = {
            hasCompletePermission: "@hasCompletePermission",
        }
    </script>
}
else
{
    <script>
        const hasPermission = false
    </script>
}

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/purchase-orders/epr/purchase-orders-epr.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/purchase-order-epr.bundle.js" asp-append-version="true"></script>
    </environment>
}
