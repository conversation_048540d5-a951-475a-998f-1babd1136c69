﻿using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Dto.PurchaseOrderEpr;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprById;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprRejectedLog;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderDetailsById;
using Microsoft.AspNetCore.Http;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.PurchaseOrder.EPR;

[SectionLevelAuthorize(SecurityFunction.OrdersSection_View)]
public class IndexModel : FormPageModel
{
    private readonly ISender _mediator;

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "po")]
    public int? PurchaseOrderId { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "polids")]
    public string? PurchaseOrderLineIdsParams { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "pols")]
    public string? PurchaseOrderLinesSerialNoParams { get; set; }

    [BindProperty(SupportsGet = true)]
    [FromQuery(Name = "epr")]
    public int? EarlyPaymentRequestId { get; set; }

    public bool IsGlobalSalesAccessMember { get; set; }
    public bool IsDifferentClient { get; set; }
    public PurchaseOrderDetailsDto? PurchaseOrderDetails { get; set; }
    public EprDetailsReadModel? EprDetails { get; set; }
    public EprRejectedLogReadModel? EprRejectedLog { get; set; }
    public EprViewModel EprViewModel { get; set; } = new();

    public IndexModel(
        ISender mediator,
        SessionManager sessionManager,
        SecurityManager securityManager,
        IHttpContextAccessor httpContextAccessor) : base(securityManager, sessionManager)
    {
        SiteSection = SiteSection.Orders;
        PageType = SitePage.Orders_PurchaseOrderDetail;
        GeneralPermissions.Add(SecurityFunction.OrdersSection_View);
        SectionLevelPermissions.Add(SecurityFunction.Orders_PurchaseOrder_View);

        _mediator = mediator;
    }

    public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken = default)
    {
        if (!PurchaseOrderId.HasValue)
        {
            HasPermission = false;
            return Page();
        }

        if (!CanViewPageAsync())
        {
            HasPermission = false;
            return Page();
        }

        if (EarlyPaymentRequestId.HasValue)
        {
            await BindEprDetailsAsync(EarlyPaymentRequestId.Value, cancellationToken);
        }
        else if (PurchaseOrderId.HasValue)
        {
            await BindPurchaseOrderDetailsAsync(PurchaseOrderId.Value, cancellationToken);
        }

        var isGSA = !_sessionManager.IsGlobalUser && _sessionManager.IsGSA;
        var isGSAViewingOtherClient = isGSA && IsDifferentClient;
        if (isGSAViewingOtherClient)
        {
            HasPermission = false;
            return Page();
        }

        SetupPermissions();
        return Page();
    }

    private bool CanViewPageAsync()
    {
        if (_securityManager == null) return false;
        if (!_securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_View) && _sessionManager.IsGSA) return false;

        if (_sessionManager.IsGlobalUser) return true;

        return true;
    }

    private void SetupPermissions()
    {
        if (_securityManager == null) return;

        EprViewModel.CanAuthorise = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_EPR_RefAuthorise);
        EprViewModel.CanEditAfterAuthorise = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_EPR_EditAfterAuthorise);
        EprViewModel.CanComplete = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_EPR_Complete);
        EprViewModel.CanDelete = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_PurchaseOrder_EPR_Delete);
    }

    private async Task BindPurchaseOrderDetailsAsync(int purchaseOrderId, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new GetPurchaseOrderDetailsByIdQuery
        {
            PurchaseOrderId = purchaseOrderId,
            ClientNo = _sessionManager.ClientID.GetValueOrDefault()
        }, cancellationToken);

        if (result.Success && result.Data is not null)
        {
            PurchaseOrderDetails = result.Data;
            IsDifferentClient = result.Data.ClientNo != _sessionManager.ClientID;
            return;
        }

        HasPermission = false;
    }

    private async Task BindEprDetailsAsync(int eprId, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new GetEprByIdQuery(eprId), cancellationToken);

        if (result.Success && result.Data is not null)
        {
            EprDetails = result.Data;
        }
        else
        {
            HasPermission = false;
        }

        await BindEprRejectedLogAsync(eprId, cancellationToken);
    }

    private async Task BindEprRejectedLogAsync(int eprId, CancellationToken cancellationToken)
    {
        var result = await _mediator.Send(new GetEprRejectedLogQuery(eprId), cancellationToken);

        if (result.Success && result.Data is not null)
        {
            EprRejectedLog = result.Data;
        }
    }
}
