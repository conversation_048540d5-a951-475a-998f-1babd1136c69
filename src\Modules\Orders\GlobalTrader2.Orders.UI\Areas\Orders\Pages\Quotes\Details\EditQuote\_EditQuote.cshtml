﻿@using GlobalTrader2.Dto.Quote
@using GlobalTrader2.SharedUI.Interfaces
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization
@using Microsoft.Extensions.Hosting
@using GlobalTrader2.Core.Enums

@model EditQuoteRequestDto

@inject IViewLocalizer _localizer
@inject IWebResourceManager WebResourceManager
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject Microsoft.AspNetCore.Hosting.IWebHostEnvironment Env

@if (Env.IsDevelopment())
{
    WebResourceManager.AddScriptModule("/js/modules/orders/quotes/containers/edit-quote.js");
}
else
{
    WebResourceManager.AddScriptModule("/dist/js/orders-edit-quote.bundle.js");
}

<script>
    var editQuoteLocalizedStrings = {
        freightMessage: '@_localizer["Please enter a number from 0.00 to 100,000,000,000,000,000.00"]',
    };
</script>

<div id="edit-quote-dialog" class="dialog-container d-none" title="@_localizer["Main Information"]">
    <script hidden>
        var mainQuoteString = {
            editMainQuote: '@_localizer["Edit Main Quote Information Details"]',
            editMainQuoteMessage: '@_localizer["Enter the changed details for the Main Quote Information and press"]',
        };
    </script>
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5 id="edit-quote-title" class="fw-bold fs-14 text-uppercase">
                @_localizer["Edit Main Quote Information Details"]
            </h5>
            <span>
                <span class="fw-bold required me-1">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>

        <div class="line"></div>

        <div class="mb-2">
            <span id="edit-quote-message">@_localizer["Enter the changed details for the Main Quote Information and press"]</span>
            <b>@_commonLocalizer["Save"]</b>
        </div>

        <div class="form-error-summary" style="display: none;">
            <img src="~/img/icons/x-octagon.svg" alt="Invalid icon" />
            <div>
            </div>
        </div>
    </div>

    <form method="post" id="edit-quote-form" class="row common-form">
        @Html.AntiForgeryToken()

        <input type="number" name="QuoteId" id="QuoteId" hidden aria-label="QuoteId" />

        <div class="col-md-12 form-control-wrapper" id="customer-container">
            <label for="customer" class="form-label">@_localizer["Customer"]</label>
            <p id="Customer" class="form-p"></p>
        </div>

        <div class="col-md-12 form-control-wrapper" id="approved-customer-container">
            <label for="customer" class="form-label">@_localizer["Approved Customer?"]</label>
            <input id="edit-quote-approved-customer" type="checkbox" name="approved-customer" class="form-check-input mt-0 p-0" disabled>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Contact" class="form-label">@_localizer["Buyer"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="contact-dropdown" data-bind-name="Contact" name="Contact">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="Contact-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Salesperson" class="form-label">@_localizer["Salesperson"]</label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="salesman-dropdown" data-bind-name="Salesman" name="Salesman">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="Salesman-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper" id="division-container">
            <label for="division" class="form-label">@_localizer["Division Sales"]</label>
            <p id="DivisionName" class="form-p"></p>
            <input type="number" name="DivisionNo" id="DivisionNo" hidden aria-label="DivisionNo" />
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="DivisionHeader" class="form-label">@_localizer["Division Header"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="division-header-dropdown" data-bind-name="DivisionHeader" name="DivisionHeader">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="DivisionHeader-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper" id="date-quoted-container">
            <label for="date-quoted" class="form-label">@_localizer["Date Quoted"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" data-type="datetime" class="form-control form-input" name="DateQuoted" id="DateQuoted" title="@_localizer["Date Quoted"]" placeholder="@_localizer["DD/MM/YYYY"]" readonly />
            </span>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Terms" class="form-label">@_localizer["Terms"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="terms-dropdown" data-bind-name="Terms" name="Terms">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="Terms-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="CurrencyNo" class="form-label">@_localizer["Currency"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="currency-dropdown" data-bind-name="Currency" name="Currency">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="javascript:void(0)" class="select-menu-gtv2-refresh-button" id="CurrencyNo-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Freight" class="form-label">@_localizer["Estimated Freight"]</label>
            <div class="d-flex align-items-center">
                <input type="text" class="form-control form-input" id="Freight" name="Freight">
                <div style="margin-left: 2px;" id="edit-quote-currency-code" class="d-none"></div>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="Incoterms" class="form-label">@_localizer["Incoterms"]<span class="required"> *</span></label>

            <div class="d-flex justify-content-center align-items-center">
                <select class="form-select" aria-label="agencySO" id="incoterms-dropdown" data-bind-name="Incoterms" name="Incoterms">
                    <option selected value="">@_commonLocalizer["Select"]...</option>
                </select>

                <a href="#" class="select-menu-gtv2-refresh-button" id="Incoterms-refresh-button">
                    <img alt="refresh-button" src="~/img/icons/refresh-button.png">
                </a>
            </div>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="NotesToCustomer" class="form-label d-inline-block">@_localizer["Notes to customer"]</label>

            <textarea id="edit-quote-notes-to-customer" name="Notes" class="form-control form-textarea" data-directive="maxLength" maxlength="2000" style="height: auto;"></textarea>
        </div>

        <div class="col-md-12 form-control-wrapper">
            <label for="InternalNotes" class="form-label d-inline-block">@_localizer["Internal Notes"]</label>

            <textarea id="edit-quote-internal-notes" name="Instructions" class="form-control form-textarea" data-directive="maxLength" maxlength="2000" style="height: auto;"></textarea>
        </div>

        <div class="col-md-12 form-control-wrapper" id="as9120-container">
            <label for="as9120" class="form-label">@_localizer["Source of Supply Required"]</label>
            <input id="edit-quote-as9120" type="checkbox" name="AS9120" class="form-check-input mt-0 p-0">
        </div>

        <div class="col-md-12 form-control-wrapper" id="is-important-container">
            <label for="is-important" class="form-label">@_localizer["Mark as Important"]</label>
            <input id="edit-quote-is-important" type="checkbox" name="IsImportant" class="form-check-input mt-0 p-0">
        </div>

        <div class="col-md-12 form-control-wrapper" id="support-team-member-container">
            <label for="edit-quote-support-team-member-auto-search" class="form-label">@_localizer["Support Team Member to update"]</label>

            <div id="edit-quote-support-team-member-auto-search-wrapper" class="position-relative">
                <input type="text" id="edit-quote-support-team-member-auto-search" class="form-control form-input"
                       data-search-input="single"
                       data-input-value-id="QuoteSupportTeamMemberId"
                       data-api-url="/orders/customer-requirements/auto-search-sale-persion"
                       data-api-key-search="keyword" />

                <input type="number" id="QuoteSupportTeamMemberId" name="SupportTeamMemberNo" hidden aria-label="support-team-member-id" />
            </div>
        </div>
    </form>
</div>