﻿using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.Contact;
using GlobalTrader2.SharedUI.Bases;
using GlobalTrader2.UserAccount.UseCases.RecentlyViewed.Commands.Create;
using Grpc.Core;
using Microsoft.AspNetCore.Http;

namespace GlobalTrader2.Orders.UI.Areas.Orders.Pages.Quotes
{
    public class IndexModel : BasePageModel
    {
        private readonly SessionManager _sessionManager;
        private readonly IMediator _mediator;
        public IndexModel(SecurityManager securityManager, SessionManager sessionManager, IMediator mediator) : base(securityManager)
        {
            _sessionManager = sessionManager;
            _mediator = mediator;
            IsGlobalUser = _sessionManager.IsGlobalUser;
            IsDataHasOtherClient = IsGlobalUser;
            LoginID = _sessionManager.LoginID;
            SiteSection = SiteSection.Orders;
            PageType = SitePage.Orders_QuoteBrowse;
        }

        public bool IsGlobalUser { get; set; } = false;
        public int? LoginID { get; set; }

        public async Task<IActionResult> OnGetAsync(CancellationToken cancellationToken)
        {
            AddBreadCrumbs();
            await UpdateRecentlyView();

            var isHasTabPermission = await CheckPagePermission();
            if (!isHasTabPermission)
            {
                return Redirect(V2Paths.NotFound);
            }

            return Page();
        }

        private void AddBreadCrumbs()
        {
            BreadCrumb.Add(Navigations.Home);
            BreadCrumb.Add(Navigations.Orders);
            BreadCrumb.Add(Navigations.Quotes);          
        }
        private async Task UpdateRecentlyView()
        {
            var lastBreadcrumb = BreadCrumb[BreadCrumb.Count - 1];
            var command = new CreateRecentlyViewedCommand
            {
                LoginNo = HttpContext.Session.GetInt32(SessionKey.LoginID),
                PageTitle = lastBreadcrumb.PageTitle,
                PageUrl = $"{lastBreadcrumb.CtaUri}"
            };
            await _mediator.Send(command);
        }

        private async Task<bool> CheckPagePermission()
        {
            bool hasOrderQuoteViewPermission = _securityManager.CheckSectionLevelPermission(SecurityFunction.Orders_Quote_View);
            if (IsGlobalUser)
            {
                if (hasOrderQuoteViewPermission)
                {
                    return true;
                }
                else
                {
                    IsDataHasOtherClient = false;
                    hasOrderQuoteViewPermission = await _securityManager.CheckFunctionPermissions(LoginID.Value, IsDataHasOtherClient, [SecurityFunction.Orders_Quote_View]);
                }
            }
            if (_securityManager != null && !hasOrderQuoteViewPermission)
            {
                return false;
            }
            var listTabs = await GetVisibleTabSecurityList(SecurityFunction.Orders_Quotes_View);
            if (!listTabs.Contains((int)ViewLevelList.My))
            {
                return false;
            }
            return true;
        }
    }
}
