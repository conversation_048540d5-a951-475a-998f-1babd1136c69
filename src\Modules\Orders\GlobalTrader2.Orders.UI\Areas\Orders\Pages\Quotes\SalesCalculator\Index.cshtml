@page
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.Quotes.SalesCalculator
@model IndexModel
@{
    Layout = "_FormLayout";
}
<div class="sales-calculator-container">
    <div class="sales-calculator-header text-center fw-bold">Sales Calculator</div>
    <div class="sales-calculator-body">
        <form id="sales-calculator-form" class="row common-form">
            <div class="form-control-wrapper">
                <label for="Quantity" class="form-label">Quantity<span class="required"> *</span></label>
                <input class="form-control form-input" id="Quantity" name="Quantity" type="text" value="">
            </div>
            <div class="form-control-wrapper">
                <label for="BuyPrice" class="form-label">Buy Price<span class="required"> *</span></label>
                <input class="form-control form-input" id="BuyPrice" name="BuyPrice" type="text" value="">
            </div>
            <div class="form-control-wrapper">
                <label for="Duty" class="form-label">Duty</label>
                <div class="d-flex gap-1 align-items-center">
                    <input class="form-control form-input" id="Duty" name="Duty" type="text" value="">
                    <span>%</span>
                </div>
            </div>
            <div class="form-control-wrapper">
                <label for="Freight" class="form-label">Freight</label>
                <input class="form-control form-input" id="Freight" name="Freight" type="text" value="">
            </div>
            <div class="form-control-wrapper">
                <label for="MarginReq" class="form-label">Margin Req<span class="required"> *</span></label>
                <div class="d-flex gap-1 align-items-center">
                    <input class="form-control form-input" id="MarginReq" name="MarginReq" type="text" value="">
                    <span>%</span>
                </div>
                <button class="btn btn-primary mt-2" id="calculate-btn">
                    <span class="lh-base">Calculate</span>
                </button>
            </div>
            <div class="form-control-wrapper">
                <label for="SalesPrice" class="form-label">Sales Price</label>
                <input class="form-control form-input" id="SalesPrice" name="SalesPrice" type="text" value="" disabled>
            </div>
            <div class="form-control-wrapper">
                <label for="Profit" class="form-label">Profit</label>
                <input class="form-control form-input" id="Profit" name="Profit" type="text" value="" disabled>
                <button class="btn btn-primary mt-2" id="reset-btn" type="reset">
                    <span class="lh-base">Reset</span>
                </button>
            </div>            
        </form>
    </div>
    <div class="sales-calculator-footer pt-2">
        <p>*All values must be in the same currency</p>
    </div>
</div>

<environment include="Development">
    <script src="/js/modules/orders/quotes/containers/sales-calculator.js" type="module" asp-append-version="true"></script>
</environment>

<environment exclude="Development">
    <script type="module" src="/dist/js/orders-quotes-sales-calculator.bundle.js" asp-append-version="true"></script>
</environment>