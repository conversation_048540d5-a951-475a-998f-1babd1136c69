@using GlobalTrader2.Dto.SalesOrderLine
@using GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details
@using GlobalTrader2.SharedUI.Interfaces

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer


<div class="dialog-container" id="request-approval-dialog" title="@_commonLocalizer["Authorisation Information"]" style="display: none;">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <h5>@_commonLocalizer["SOR APPROVAL"]</h5>
        </div>
        <div class="line"></div>
        <span>@_localizer["Request Approval to this Sales Order"]</span>
    </div>
    <div class="form-error-summary" style="display: none;">
        <img src="~/img/icons/x-octagon.svg" alt="Add icon" />
        <div>
            <p>@_messageLocalizer["There were some problems with your form."]</p>
            <p>@_messageLocalizer["Please check below and try again."]</p>
        </div>
    </div>
    <form class="row common-form" id="request-approval-form" method="post">
        @Html.AntiForgeryToken()
        <div class="form-group text-nowrap mb-10px">
                    <div class="row form-control-wrapper mb-1">
                        <div class="col-3">
                            <label for="notify-salesperson" class="form-label d-inline-block">
                                @_localizer["Notify Salesperson?"]
                            </label>
                        </div>
                        <div class="col-6 d-flex align-items-center gap-2">
                            <input type="checkbox" id="notify-salesperson" name="notify" class="form-control p-0 form-check-input check-sm">
                            <span data-field="salesmanName" id="sales-man" style="display:none;"></span>
                        </div>
                    </div>
                    <div class="row form-control-wrapper mb-1">
                        <div class="col-3">
                            <label for="to" class="form-label d-inline-block">
                                @_localizer["To"]<span class="fw-bold me-1 required">*</span>
                            </label>
                        </div>
                        <div class="col-4">
                                <input type="text" id="approverSearch" class="form-control form-input" />
                                 <input type="hidden" id="approverNo" name="approverNo" />
                        </div>
                    </div>
                    <div class="row form-control-wrapper mb-1">
                        <div class="col-3">
                            <label for="to" class="form-label d-inline-block">
                                @_localizer["Subject"]<span class="fw-bold me-1 required">*</span>
                            </label>
                        </div>
                        <div class="col-6">
                        <input type="text" id="subject" name="subject" maxlength="256" class="form-control form-input" required>
                        </div>
                    </div>
                    <div class="row form-control-wrapper">
                        <div class="col-3">
                             <label for="message" class="form-label d-inline-block">
                                 @_localizer["Message"]
                                 <span class="fw-bold me-1 required">*</span>
                             </label>
                        </div>
                        <div class="col-7">
                        <textarea id="message" name="message" class="form-control form-textarea" rows="10" maxlength="60000" style="height: auto;"></textarea>
                        </div>
               
                    </div>
         </div>
    </form>
</div>