@page
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.DocumentsSection
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.OrderSelectionMenu
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.QuickJump
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SalesOrderQuickBrowse
@using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.SourcingLinks
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@using GlobalTrader2.SharedUI.Services
@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details.Index
@inject SettingManager _settingManager
@inject SessionManager _sessionManager
@{
    ViewData["Title"] = $"Sales Order {Model.SalesOrderGeneralInfo.SalesOrderNo}";
}

@section HeadBlock {
    @await Html.PartialAsync("Partials/_SummerNoteStyleSheet")
    @await Html.PartialAsync("Partials/_ThirdPartyStyleSheetsPartial")
}

@section LeftSidebar {
    @await Component.InvokeAsync(nameof(LeftNugget), new LeftNuggetProps()
{
    IsInitiallyExpanded = true,
    Item = SideBar.QuickJump,
    ChildComponent = nameof(QuickJump),
    ChildComponentParameters = new { options = QuickJumpOptions.OrderQuickJumpOptions }
})
    @await Component.InvokeAsync(nameof(LeftNugget), new LeftNuggetProps()
{
    IsInitiallyExpanded = false,
    Item = SideBar.QuickBrowse,
    ChildComponent = nameof(SalesOrderQuickBrowse),
})
    @await Component.InvokeAsync(nameof(OrderSelectionMenu))
    @await Component.InvokeAsync(nameof(SourcingLinksMenu))
}
<div class="page-content-container mh-1500">
    <div class="d-flex justify-content-between mt-2">
        <h4 class="page-primary-title" id="company-detail-primary-title">
            <span id="company-name-primary-title">Sales Order @Model.SalesOrderGeneralInfo.SalesOrderNo</span>
        </h4>
        <div class="d-flex gap-2">
            @if (Model.CanAddSalesOrder)
            {
                <a href="@RedirectUrlHelper.GetUrlWithParams_SalesOrderAdd(null, null, null, Model.SalesOrderGeneralInfo.CompanyNameRaw)" class="btn btn-primary">
                    <img src="~/img/icons/plus.svg" alt="Add icon" width="18" height="18" />
                    <span class="lh-base text-nowrap">@_commonLocalizer["Add New Sales Order"]</span>
                </a>
            }
        </div>
    </div>
    <div class="mb-3 border-bottom">
        <div class="row m-0 mb-1">
            <div class="col-12 p-0 fs-11 text-primary-bold">
                <span>
                    <a class="dt-hyper-link " href="/Contact/AllCompanies/Details?cm=@Model.SalesOrderGeneralInfo.SalesOrderCompanyId">@Model.SalesOrderGeneralInfo.CompanyName</a>
                </span>
                <span>
                    @if (!string.IsNullOrEmpty(Model.SalesOrderGeneralInfo.AdvisoryNotes))
                    {
                        <img src="/img/icons/circle-exclamation-red.svg" alt="AdvisoryNotes" title="@Model.SalesOrderGeneralInfo.AdvisoryNotes" class="ms-1 rounded-circle bg-white" height="14" data-bs-toggle="tooltip" data-bs-placement="bottom" />
                    }
                </span>
            </div>
        </div>
        <div class="d-flex justify-content-between m-0 mb-1">
            <div class="p-0 fs-11 text-primary-bold d-flex gap-2">
                <span class="fw-bold">@_commonLocalizer["Status"]</span>
                <span class="type-name">@Model.SalesOrderGeneralInfo.Status</span>
            </div>
            <div class="p-0 fs-11 text-primary-bold d-flex gap-2">
                <span class="fw-bold">@_commonLocalizer["Current print set to:"]</span>
                <span class="type-name">@Model.SalesOrderGeneralInfo.CurrentPrintSetTo</span>
            </div>
        </div>
        @if(Model.IsDifferentClient){
            <div class="row m-0 mb-1">
                <div class="col-12 p-0 fs-12 fw-bold">
                    <span style="background-color: yellow;">
                        @Model.SalesOrderGeneralInfo.SOClientName
                    </span>
                </div>
            </div>
        }
    </div>

    <input id="isAllowAction" type="hidden" value="@Model.IsAllowAction.ToString()" hidden />
    @await Html.PartialAsync("MainInfo/_MainInfo.cshtml", Model)
    @await Html.PartialAsync("MainInfo/_SalesOrderPromiseDatePassed.cshtml")
    @await Html.PartialAsync("MainInfo/_CloseSalesOrderDialog.cshtml")
    @await Html.PartialAsync("MainInfo/_ConfirmSOSentDialog.cshtml")
    @await Html.PartialAsync("Authorisation/_Authorisation.cshtml", Model.AuthorisationInfoViewModel)
    @await Html.PartialAsync("SOLine/_SoLine.cshtml", Model)
    @await Html.PartialAsync("ExportApprovalStatus/_ExportApprovalStatus.cshtml", Model.ExportApprovalStatuslineViewModel)
    @await Html.PartialAsync("ExportApprovalStatus/_EditExportApprovalStatusDialog.cshtml", Model.SalesOrderGeneralInfo.SOClientId)
    @await Html.PartialAsync("ExportApprovalStatus/_ApproveExportApprovalStatusDialog.cshtml", Model.SalesOrderGeneralInfo.SOClientId)
    @await Html.PartialAsync("ExportApprovalStatus/_RequestApprovalExportApprovalStatusDialog.cshtml")

    @await Component.InvokeAsync(nameof(DocumentsSection), Model.CustomerPoOnlyPdfViewModel)
    @await Html.PartialAsync("Partials/_CustomerPoOnlyPdfSectionConfig", @Model.CustomerPoOnlyPdfViewModel)

    @await Component.InvokeAsync(nameof(DocumentsSection), Model.SORDocumentsViewModel)
    @await Html.PartialAsync("Partials/_SORDocumentsSectionBoxConfig", @Model.SORDocumentsViewModel)

    @await Component.InvokeAsync(nameof(DocumentsSection), Model.PdfDocumentsViewModel)
    @await Html.PartialAsync("Partials/_PdfDocumentsSectionBoxConfig", @Model.PdfDocumentsViewModel)

    @await Component.InvokeAsync(nameof(DocumentsSection), Model.SOPaymentFilesViewModel)
    @await Html.PartialAsync("Partials/_SOPaymentFilesSectionBoxConfig", @Model.SOPaymentFilesViewModel)

    @await Component.InvokeAsync(nameof(DocumentsSection), Model.ExcelDocumentsViewModel)
    @await Html.PartialAsync("Partials/_ExcelDocumentsSectionBoxConfig", @Model.ExcelDocumentsViewModel)

    @if (@Model.IsAllowAction)
    {
        @await Html.PartialAsync("EndUserUndertakingForm/_EndUserUndertakingForm.cshtml")
    }

</div>
<script>
    const SODetailGeneralInfo = @Json.Serialize(Model.SalesOrderGeneralInfo);
    const salesOrderMainInfoPermission = @Json.Serialize(Model.SalesOrderMainInfoViewModel.SalesOrderMainInfoPermission);

    const localizedStrings = {
        tasks: "@_commonLocalizer["Task(s)"]",
        needToCompleteOpenTasks: "@_messageLocalizer["Need to complete open Tasks"]",
        viewTask: "@_commonLocalizer["ViewTask"]",
        promiseNotInRangeOfThisMonth: "@_messageLocalizer["Date Promised should be between current date and end of the current calendar month"]",
        promiseNotInRangeOfPromiseMonth: "@_messageLocalizer["Date Promised should be between current date and end of promise day month"]",
        orderQuantityCannotBeLessThanShippedQuantity: "@_messageLocalizer["Order quantity cannot be less than shipped quantity"]",
    };

    const salesOrderLinesSection = {
        canEditDateRequired: @Json.Serialize(Model.LinesSectionViewModel.CanEditDateRequired),
        canEditPromiseDateAfterCheck: @Json.Serialize(Model.LinesSectionViewModel.CanEditPromiseDateAfterCheck),
        isAutoAuthorizeSo: @Json.Serialize(Model.LinesSectionViewModel.IsAutoAuthorizeSo),
    };
</script>

@section Scripts {
    @await Html.PartialAsync("_ValidationScriptsPartial")
    @await Html.PartialAsync("Partials/_DataTablesScriptsPartial")
    @await Html.PartialAsync("Partials/_SummerNoteScript")

    <script src="@_settingManager.GetCdnUrl("/js/widgets/loading-spinner.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/dialog-custom-events.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/common-table.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/resize-data-table-extensions.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datatables-detail-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/html-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/form-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/string-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/page-url-function-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/api-client.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/drop-down.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/input-directive.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/directives/custom-input-directive.js")" asp-append-version="true"></script>    
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-date-picker.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/datetime-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/number-validation.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/helper/sort-helper.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/search-select.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/widgets/custom-summer-note.js")" asp-append-version="true"></script>
    <script src="@_settingManager.GetCdnUrl("/js/core/validation/jquery-summernote-validation.js")" asp-append-version="true"></script>
    <environment include="Development">
        <script type="module" src="/js/modules/orders/sales-orders/details/sales-orders-details.js" asp-append-version="true"></script>
    </environment>
    <environment exclude="Development">
        <script type="module" src="/dist/js/orders-sales-orders-details.bundle.js" asp-append-version="true"></script>
    </environment>
}