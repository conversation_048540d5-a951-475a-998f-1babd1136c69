@inject IViewLocalizer _localizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer

@model GlobalTrader2.Orders.UI.Areas.Orders.Pages.SalesOrders.Details.EditAllLinesViewModel

<div id="edit-all-sales-order-lines-dialog" title="@_commonLocalizer["Lines"]" style="display:none" class="fs-12">
    <div class="dialog-description">
        <div class="d-flex justify-content-between">
            <div>
                <h5 class="text-uppercase dialog-title">@_localizer["Edit All Open Sales Order Lines"]&nbsp;</h5>
            </div>
            <span>
                <span class="me-1 required">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>
        <div class="line"></div>
        <span class="mb-1">
            @_localizer["Enter the changed details for the Line and press Save"]
        </span>
    </div>
    <div class="form-error-summary" style="display: none;">
        <img src="~/img/icons/x-octagon.svg" alt="X icon" />
        <div>
            <p>@_messageLocalizer["There were some problems with your form."]</p>
            <p>@_messageLocalizer["Please check below and try again."]</p>
        </div>
    </div>
    <form method="post" id="edit-all-sales-order-lines-form" class="row common-form">
        @Html.AntiForgeryToken()
        <input type="text" class="form-control form-input visually-hidden"
            id="edit-all-sales-order-lines-sales-order-id-hidden-input"
            name="salesOrderId"
            value="@Model.SalesOrderId" 
        >
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Sales Order"]</p>
            <span id="edit-all-sales-order-lines-sales-order-id">
                @Model.SalesOrderNumber
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <p class="form-label">@_localizer["Customer"]</p>
            <span id="edit-all-sales-order-lines-customer-name">
                @Model.Customer
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-all-sales-order-lines-date-promised" class="form-label" >@_localizer["Date Promised"]<span class="required"> *</span></label>
            <span class="d-flex gap-1">
                <input type="text" class="form-control form-input datepicker" style="background: #fafaf4; cursor: pointer;" 
                    placeholder="@_commonLocalizer["DD/MM/YYYY"]" 
                    id="edit-all-sales-order-lines-date-promised" 
                    name="datePromised"
                    readonly>
            </span>
        </div>
        <div class="col-12 form-control-wrapper">
            <label for="edit-all-sales-order-lines-promise-reason" class="form-label">@_localizer["Promise Reason"]<span
                    class="fw-bold ms-1 required"> *</span></label>
            <select id="edit-all-sales-order-lines-promise-reason" class="dropdown" name="promiseReasonNo">
            </select>
        </div>

        <div class="dialog-container" id="edit-all-sales-order-lines-confirm-dialog" title="Edit all sales orders lines confirmation">
            <div class="dialog-description">
                <span>@_messageLocalizer["It will update the Promise dates of all existing open sales order lines. Are you sure want to update?"]</span>
            </div>
        </div>
    </form>
</div>

<script>
    const editAllLinesLocalizer = {
        promiseNotInRangeOfThisMonth: "@_messageLocalizer["Please enter a date between current date and the end of the current calendar month"]",
        promiseNotInRangeOfPromiseMonth: "@_messageLocalizer["Promised date should be between current date and end of promised date month"]",
    }

    const editAllLinesModel = {
        salesOrderId: @Json.Serialize(Model.SalesOrderId),
        customer: @Json.Serialize(Model.Customer),
        allowEditDatePromisedBetweenCurrentMonthAndEnd: @Json.Serialize(Model.AllowEditDatePromisedBetweenCurrentMonthAndEnd),
        isAutoAuthorizeSo: @Json.Serialize(Model.IsAutoAuthorizedSo),
        isAuthorizeSo: @Json.Serialize(Model.IsAuthorizedSo),
    };
</script>