﻿using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Domain.Entities.Imports;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Repository;
using GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Dtos;
using Microsoft.Extensions.Configuration;
using GTExceptions = GlobalTrader2.Core.Exceptions;

namespace GlobalTrader2.Orders.UserCases.Orders.BOM.ImportSourcingResults.Commands.ImportDataFile
{
    public class ImportDataFileHandler : IRequestHandler<ImportDataFileCommand, BaseResponse<ImportDataFileResultDto>>
    {
        private readonly IBlobStorageService _blobStorageService;
        private readonly IImportBaseRepository<BomImportSourcingTemp> _importBaseRepository;
        private readonly IExcelDataReaderService _excelDataReaderService;
        private readonly ICsvFileDataReaderService<ImportSourcingResultModel> _csvFileDataReaderService;
        private readonly int _maxImportRows;

        public ImportDataFileHandler(IBlobStorageService blobStorageService, IImportBaseRepository<BomImportSourcingTemp> importBaseRepository, IConfiguration configuration, IExcelDataReaderService excelDataReaderService, ICsvFileDataReaderService<ImportSourcingResultModel> csvFileDataReaderService)
        {
            _blobStorageService = blobStorageService;
            _importBaseRepository = importBaseRepository;
            _excelDataReaderService = excelDataReaderService;
            _csvFileDataReaderService = csvFileDataReaderService;

            _maxImportRows = int.Parse(configuration.GetSection(AppSettingKeys.ImportData)[AppSettingKeys.ImportDataMaxRows] ?? "25000");
        }
        public async Task<BaseResponse<ImportDataFileResultDto>> Handle(ImportDataFileCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<ImportDataFileResultDto>();

            var downloadedImportDataFile = await _blobStorageService.GetBlobContentsAsync(BlobStorage.DocumentHeadersContainerName, request.GeneratedFileName);

            if (downloadedImportDataFile == null || downloadedImportDataFile.Content == null)
            {
                throw new GTExceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = "GeneratedFileName",
                        ErrorMessage = "The file could not be found or is empty."
                    }
                });
            }

            var fileExtension = Path.GetExtension(request.GeneratedFileName).ToLowerInvariant();
            int importedRowsCount = 0;
            if (fileExtension == FileExtensions.Excel || fileExtension == FileExtensions.Excel2003)
            {
                importedRowsCount = await ImportExcelFileAsync(request, downloadedImportDataFile.Content);
            }
            else if (fileExtension == FileExtensions.Csv)
            {
                importedRowsCount = await ImportCsvFileAsync(request, downloadedImportDataFile.Content);
            }

            response.Success = true;
            response.Data = new ImportDataFileResultDto { ImportedRowsCount = importedRowsCount };

            return response;
        }

        private async Task<int> ImportExcelFileAsync(ImportDataFileCommand request, Stream stream)
        {
            var tempSourcingResults = new List<BomImportSourcingTemp>();
            var tables = _excelDataReaderService.Read(stream);
 
            await ValidateAndRemoveInvalidFile(tables, request.GeneratedFileName);
            var table = tables[0];
            var rowsCount = table.Rows.Count;

            foreach (DataRow row in table.Rows)
            {
                 if(row?.ItemArray == null || 
                    row.ItemArray.All(field => field == null || 
                                        field == DBNull.Value || 
                                        string.IsNullOrWhiteSpace(field.ToString())))
                    continue;
                tempSourcingResults.Add(new BomImportSourcingTemp
                {
                    BOMNo = request.BomNo,
                    CustomerRequirementNumber = CleanTextHelper(row["REQUIREMENT"]?.ToString()),
                    Manufacturer = CleanTextHelper(row["*MANUFACTURER"]?.ToString()),
                    SupplierPart = CleanTextHelper(row["*SUPPLIER PART NO."]?.ToString()),
                    OfferedQuantity = CleanTextHelper(row["*OFFERED QTY."]?.ToString()),
                    SupplierCost = CleanTextHelper(row["*Supplier Cost"]?.ToString()),
                    SPQ = CleanTextHelper(row["SPQ"]?.ToString()),
                    MOQ = CleanTextHelper(row["MOQ"]?.ToString()),
                    SupplierName = CleanTextHelper(row["*SUPPLIER NAME"]?.ToString()),
                    MSL = CleanTextHelper(row["MSL"]?.ToString()),
                    Notes = CleanTextHelper(row["NOTES"]?.ToString()),
                    DateCode = CleanTextHelper(row["DATE CODE"]?.ToString()),
                    QtyInStock = CleanTextHelper(row["QTY IN STOCK"]?.ToString()),
                    OfferStatus = CleanTextHelper(row["OFFER STATUS"]?.ToString()),
                    BuyPrice = CleanTextHelper(row["*BUY PRICE"]?.ToString()),
                    SellPrice = CleanTextHelper(row["*SELL PRICE"]?.ToString()),
                    ShippingCost = CleanTextHelper(row["SHIPPING COST"]?.ToString()),
                    Package = CleanTextHelper(row["PACKAGE"]?.ToString()),
                    ROHS = CleanTextHelper(row["ROHS"]?.ToString()),
                    Currency = CleanTextHelper(row["*CURRENCY"]?.ToString()),
                    FactorySealed = CleanTextHelper(row["FACTORY SEALED"]?.ToString()),
                    Region = CleanTextHelper(row["REGION"]?.ToString()),
                    LeadTime = CleanTextHelper(row["LEADTIME"]?.ToString()),
                    LastTimeBuy = CleanTextHelper(row["LAST TIME BUY"]?.ToString()),
                    DeliveryDate = CleanTextHelper(row["DELIVERY DATE"]?.ToString()),
                    CustomerRefNo = CleanTextHelper(row["CUSTOMER REF NO."]?.ToString()),
                    OriginalFilename = request.OriginalFileName,
                    GeneratedFilename = request.GeneratedFileName,
                    CreatedBy = request.CreatedBy,
                    DLUP = DateTime.Now,
                });
            }

            await SaveDatabase(request.CreatedBy, request.BomNo, tempSourcingResults);

            return rowsCount;
        }

        private async Task<int> ImportCsvFileAsync(ImportDataFileCommand request, Stream stream)
        {
            var tempSourcingResults = new List<BomImportSourcingTemp>();
            var sourcingResultsToImport = _csvFileDataReaderService.Read(stream, request.HasColumnHeaders);
            await ValidateAndRemoveInvalidFile(sourcingResultsToImport.Count, request.GeneratedFileName);

            foreach (var resourcingResultImportItem in sourcingResultsToImport)
            {
                if(resourcingResultImportItem.GetType().GetProperties()
                    .Where(p => p.PropertyType == typeof(string))
                    .All(p => string.IsNullOrWhiteSpace((string?)p.GetValue(resourcingResultImportItem))))
                    continue;
                tempSourcingResults.Add(new BomImportSourcingTemp
                {
                    BOMNo = request.BomNo,
                    CustomerRequirementNumber = resourcingResultImportItem.Requirement,
                    Manufacturer = CleanTextHelper(resourcingResultImportItem.Manufacturer),
                    SupplierPart = CleanTextHelper(resourcingResultImportItem.SupplierPartNo),
                    OfferedQuantity = CleanTextHelper(resourcingResultImportItem.OfferedQty),
                    SupplierCost = CleanTextHelper(resourcingResultImportItem.SupplierCost),
                    SPQ = CleanTextHelper(resourcingResultImportItem.Spq),
                    MOQ = CleanTextHelper(resourcingResultImportItem.Moq),
                    SupplierName = CleanTextHelper(resourcingResultImportItem.SupplierName),
                    MSL = CleanTextHelper(resourcingResultImportItem.Msl),
                    Notes = CleanTextHelper(resourcingResultImportItem.Notes),
                    DateCode = CleanTextHelper(resourcingResultImportItem.DateCode),
                    QtyInStock = CleanTextHelper(resourcingResultImportItem.QtyInStock),
                    OfferStatus = CleanTextHelper(resourcingResultImportItem.OfferStatus),
                    BuyPrice = CleanTextHelper(resourcingResultImportItem.BuyPrice),
                    SellPrice = CleanTextHelper(resourcingResultImportItem.SellPrice),
                    ShippingCost = CleanTextHelper(resourcingResultImportItem.ShippingCost),
                    Package = CleanTextHelper(resourcingResultImportItem.Package),
                    ROHS = CleanTextHelper(resourcingResultImportItem.RoHS),
                    Currency = CleanTextHelper(resourcingResultImportItem.Currency),
                    FactorySealed = CleanTextHelper(resourcingResultImportItem.FactorySealed),
                    Region = CleanTextHelper(resourcingResultImportItem.Region),
                    LeadTime = CleanTextHelper(resourcingResultImportItem.LeadTime),
                    LastTimeBuy = CleanTextHelper(resourcingResultImportItem.LastTimeBuy),
                    DeliveryDate = CleanTextHelper(resourcingResultImportItem.DeliveryDate),
                    CustomerRefNo = CleanTextHelper(resourcingResultImportItem.CustomerRefNo),
                    OriginalFilename = request.OriginalFileName,
                    GeneratedFilename = request.GeneratedFileName,
                    CreatedBy = request.CreatedBy,
                    DLUP = DateTime.Now,
                });
            }

            await SaveDatabase(request.CreatedBy, request.BomNo, tempSourcingResults);
            
            return tempSourcingResults.Count;
        }

        private async Task<bool> ValidateAndRemoveInvalidFile(DataTableCollection? dataTables, string blobName)
        {
            if (dataTables == null || dataTables.Count == 0)
            {
                await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

                throw new GTExceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = "File",
                        ErrorMessage = $"File is empty."
                    }
                });
            }

            await ValidateAndRemoveInvalidFile(dataTables[0].Rows.Count, blobName);

            return true;
        }

        private async Task<bool> ValidateAndRemoveInvalidFile(int rowCount, string blobName)
        {
            if (rowCount == 0)
            {
                await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

                throw new GTExceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = "File",
                        ErrorMessage = $"File is empty."
                    }
                });
            }
            else if (rowCount > _maxImportRows)
            {
                await _blobStorageService.DeleteBlobAsync(BlobStorage.DocumentHeadersContainerName, blobName);

                throw new GTExceptions.ValidationException(new List<BaseError>() {
                    new BaseError()
                    {
                        PropertyMessage = "File",
                        ErrorMessage = $"Uploaded file exceeds {_maxImportRows} rows. Please reduce the number of rows and try again."
                    }
                });
            }

            return true;
        }

        private async Task<bool> SaveDatabase(int createdBy, int BOMNo, List<BomImportSourcingTemp> tempSourcingResults)
        {

            await _importBaseRepository.ExecuteSqlRawAsync(
                $"DELETE FROM {EfCoreHelper.GetTableName<BomImportSourcingTemp>()} WHERE CreatedBy = @CreatedBy AND BOMNo = @BOMNo",
                [
                    new SqlParameter("@CreatedBy", SqlDbType.Int) { Value = createdBy },
                    new SqlParameter("@BOMNo", SqlDbType.Int) { Value = BOMNo }
                ]);
            await _importBaseRepository.BulkInsertAsync(tempSourcingResults);

            return true;
        }

        private static string? CleanTextHelper(string? text) {
            var cleanedText = StringHelper.CleanJunkChars(StringHelper.CleanCarriageReturnTabAndNewLineCharacter(text?.Trim()));
            return string.IsNullOrWhiteSpace(cleanedText) ? null : cleanedText;
        }
    }
}
