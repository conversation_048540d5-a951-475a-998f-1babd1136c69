﻿namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.CreateEpr;

public class CreateEprCommandValidator : AbstractValidator<CreateEprCommand>
{
    public CreateEprCommandValidator()
    {
        RuleFor(x => x.Data.PurchaseOrderNumber)
            .NotEmpty().WithMessage("Purchase order number must not be empty.")
            .NotNull().WithMessage("Purchase order number must not be null.");

        RuleFor(x => x.Data.CompanyName)
            .MaximumLength(250).WithMessage("Company name cannot be longer than 250 characters.");

        RuleFor(x => x.Data.OrderValue)
            .PrecisionScale(18, 4, false).WithMessage("Invalid order sales value");

        RuleFor(x => x.Data.CurrencyCode)
            .MaximumLength(50).WithMessage("Currency code cannot be longer than 50 characters.");

        RuleFor(x => x.Data.DeliveryDate)
            .NotEmpty().WithMessage("Delivery date cannot be empty.")
            .NotNull().WithMessage("Delivery date cannot be null.");

        RuleFor(x => x.Data.Name)
            .MaximumLength(250).WithMessage("Name cannot be longer than 250 characters.");

        RuleFor(x => x.Data.Address)
            .MaximumLength(500).WithMessage("Addess cannot be longer than 500 characters.");

        RuleFor(x => x.Data.Tel)
            .MaximumLength(50).WithMessage("Tel cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Fax)
            .MaximumLength(50).WithMessage("Fax cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Email)
            .EmailAddress().WithMessage("Invalid email.").When(x => !string.IsNullOrWhiteSpace(x.Data.Email1))
            .MaximumLength(250).WithMessage("Email cannot be longer than 250 characters.");

        RuleFor(x => x.Data.Name1)
            .MaximumLength(250).WithMessage("Name cannot be longer than 250 characters.");

        RuleFor(x => x.Data.Address1)
            .MaximumLength(500).WithMessage("Addess cannot be longer than 500 characters.");

        RuleFor(x => x.Data.Tel1)
            .MaximumLength(50).WithMessage("Tel cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Fax1)
            .MaximumLength(50).WithMessage("Fax cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Email1)
            .EmailAddress().WithMessage("Invalid email.").When(x => !string.IsNullOrWhiteSpace(x.Data.Email1))
            .MaximumLength(250).WithMessage("Email cannot be longer than 250 characters.");

        RuleFor(x => x.Data.Name2)
            .MaximumLength(250).WithMessage("Name cannot be longer than 250 characters.");

        RuleFor(x => x.Data.Address2)
            .MaximumLength(500).WithMessage("Addess cannot be longer than 500 characters.");

        RuleFor(x => x.Data.Tel2)
            .MaximumLength(50).WithMessage("Tel cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Fax2)
            .MaximumLength(50).WithMessage("Fax cannot be longer than 50 characters.");

        RuleFor(x => x.Data.Email2)
            .EmailAddress().WithMessage("Invalid email.").When(x => !string.IsNullOrWhiteSpace(x.Data.Email1))
            .MaximumLength(250).WithMessage("Email cannot be longer than 250 characters.");

        RuleFor(x => x.Data.RaisedByDate)
            .NotEmpty().WithMessage("Raise by date must not be empty.")
            .NotNull().WithMessage("Raise by date must not be null.");

        RuleFor(x => x.Data.Authorized)
            .MaximumLength(250).WithMessage("Authorized cannot be longer than 250 characters.");

        RuleFor(x => x.Data.SLTerms)
            .MaximumLength(500).WithMessage("SLTerms cannot be longer than 500 characters.");

        RuleFor(x => x.Data.PaymentAuthorizedBy)
            .MaximumLength(250).WithMessage("Payment authorized by cannot be longer than 250 character.");

        RuleFor(x => x.Data.Countersigned)
            .MaximumLength(250).WithMessage("Counter signed by cannot be longer than 250 character.");

        RuleFor(x => x.Data.SupplierCode)
            .MaximumLength(250).WithMessage("Supplier code by cannot be longer than 250 character.");
    }
}
