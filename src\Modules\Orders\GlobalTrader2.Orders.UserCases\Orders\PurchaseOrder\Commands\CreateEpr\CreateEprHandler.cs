﻿using GlobalTrader2.Dto.PurchaseOrderEpr;
using System.Text;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.CreateEpr;

public class CreateEprHandler : IRequestHandler<CreateEprCommand, BaseResponse<int>>
{
    private readonly IBaseRepository<CreateEprReadModel> _repository;

    public CreateEprHandler(IBaseRepository<CreateEprReadModel> repository)
    {
        _repository = repository;
    }

    public async Task<BaseResponse<int>> Handle(CreateEprCommand request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<int>();
        var data = request.Data;

        var outputParameter = new SqlParameter("@EPRId", SqlDbType.Int) { Direction = ParameterDirection.Output };
        var parameters = new List<SqlParameter>
        {
            new SqlParameter("@PurchaseOrderId", SqlDbType.Int) { Value = request.PurchaseOrderId },
            new SqlParameter("@PurchaseOrderNumber", SqlDbType.Int) { Value = (object?)data.PurchaseOrderNumber ?? DBNull.Value },
            new SqlParameter("@IsNew", SqlDbType.Bit) { Value = data.IsNew },
            new SqlParameter("@CompanyName", SqlDbType.VarChar, 250) { Value = (object?)data.CompanyName ?? DBNull.Value },
            new SqlParameter("@OrderValue", SqlDbType.Decimal) { Precision = 18, Scale = 4, Value = data.OrderValue },
            new SqlParameter("@CurrencyCode", SqlDbType.VarChar, 50) { Value = (object?)data.CurrencyCode ?? DBNull.Value },
            new SqlParameter("@DeliveryDate", SqlDbType.DateTime) { Value = data.DeliveryDate },

            new SqlParameter("@InAdvance", SqlDbType.Bit) { Value = data.InAdvance },
            new SqlParameter("@UponReceipt", SqlDbType.Bit) { Value = data.UponReceipt },
            new SqlParameter("@NetSpecify", SqlDbType.Int) { Value = (object?)data.NetSpecify ?? DBNull.Value },
            new SqlParameter("@OtherSpecify", SqlDbType.Text) { Value = (object?)data.OtherSpecify ?? DBNull.Value },

            new SqlParameter("@TT", SqlDbType.Bit) { Value = data.TT },
            new SqlParameter("@Cheque", SqlDbType.Bit) { Value = data.Cheque },
            new SqlParameter("@CreditCard", SqlDbType.Bit) { Value = data.CreditCard },
            new SqlParameter("@Comments", SqlDbType.Text) { Value = (object?)data.Comments ?? DBNull.Value },

            new SqlParameter("@Name", SqlDbType.VarChar, 250) { Value = (object?)data.Name ?? DBNull.Value },
            new SqlParameter("@Address", SqlDbType.VarChar, 500) { Value = (object?)data.Address ?? DBNull.Value },
            new SqlParameter("@Tel", SqlDbType.VarChar, 50) { Value = (object?)data.Tel ?? DBNull.Value },
            new SqlParameter("@Fax", SqlDbType.VarChar, 50) { Value = (object?)data.Fax ?? DBNull.Value },
            new SqlParameter("@Email", SqlDbType.VarChar, 250) { Value = (object?)data.Email ?? DBNull.Value },

            new SqlParameter("@Name1", SqlDbType.VarChar, 250) { Value = (object?)data.Name1 ?? DBNull.Value },
            new SqlParameter("@Address1", SqlDbType.VarChar, 500) { Value = (object?)data.Address1 ?? DBNull.Value },
            new SqlParameter("@Tel1", SqlDbType.VarChar, 50) { Value = (object?)data.Tel1 ?? DBNull.Value },
            new SqlParameter("@Fax1", SqlDbType.VarChar, 50) { Value = (object?)data.Fax1 ?? DBNull.Value },
            new SqlParameter("@Email1", SqlDbType.VarChar, 250) { Value = (object?)data.Email1 ?? DBNull.Value },
            new SqlParameter("@Comment", SqlDbType.Text) { Value = (object?)data.Comment ?? DBNull.Value },

            new SqlParameter("@Name2", SqlDbType.VarChar, 250) { Value = (object?)data.Name2 ?? DBNull.Value },
            new SqlParameter("@Address2", SqlDbType.VarChar, 500) { Value = (object?)data.Address2 ?? DBNull.Value },
            new SqlParameter("@Tel2", SqlDbType.VarChar, 50) { Value = (object?)data.Tel2 ?? DBNull.Value },
            new SqlParameter("@Fax2", SqlDbType.VarChar, 50) { Value = (object?)data.Fax2 ?? DBNull.Value },
            new SqlParameter("@Email2", SqlDbType.VarChar, 250) { Value = (object?)data.Email2 ?? DBNull.Value },

            new SqlParameter("@ProFormaAttached", SqlDbType.Bit) { Value = data.ProFormaAttached },
            new SqlParameter("@RaisedByNo", SqlDbType.Int) { Value = (object?)data.RaisedByNo ?? DBNull.Value },
            new SqlParameter("@RaisedByDate", SqlDbType.DateTime) { Value = (object?)data.RaisedByDate ?? DBNull.Value },

            new SqlParameter("@SORSigned", SqlDbType.Bit) { Value = data.SORSigned },
            new SqlParameter("@ForStock", SqlDbType.Bit) { Value = data.ForStock },
            new SqlParameter("@ValuesCorrect", SqlDbType.Bit) { Value = data.ValuesCorrect },
            new SqlParameter("@Authorized", SqlDbType.VarChar, 250) { Value = (object?)data.Authorized ?? DBNull.Value },
            new SqlParameter("@AuthorizedDate", SqlDbType.DateTime) { Value = (object?)data.AuthorizedDate ?? DBNull.Value },

            new SqlParameter("@ERAIMember", SqlDbType.Bit) { Value = data.ERAIMember },
            new SqlParameter("@ERAIReported", SqlDbType.Bit) { Value = data.ERAIReported },
            new SqlParameter("@DebitNotes", SqlDbType.Bit) { Value = data.DebitNotes },
            new SqlParameter("@APOpenOrders", SqlDbType.Bit) { Value = data.APOpenOrders },
            new SqlParameter("@ACTotalValue", SqlDbType.Decimal) { Precision = 18, Scale = 4, Value = data.ACTotalValue },
            new SqlParameter("@ACTotalValue1", SqlDbType.Decimal) { Precision = 18, Scale = 4, Value = data.ACTotalValue1 },
            new SqlParameter("@SLComment", SqlDbType.Text) { Value = (object?)data.SLComment ?? DBNull.Value },
            new SqlParameter("@SLTerms", SqlDbType.VarChar, 500) { Value = (object?)data.SLTerms ?? DBNull.Value },
            new SqlParameter("@SLOverdue", SqlDbType.Bit) { Value = data.SLOverdue },
            new SqlParameter("@SLTotalValue", SqlDbType.Decimal) { Precision = 18, Scale = 4, Value = data.SLTotalValue },
            new SqlParameter("@PaymentAuthorizedBy", SqlDbType.VarChar, 250) { Value = (object?)data.PaymentAuthorizedBy ?? DBNull.Value },
            new SqlParameter("@Countersigned", SqlDbType.VarChar, 250) { Value = (object?)data.Countersigned ?? DBNull.Value },
            new SqlParameter("@PaymentAuthorizedDate", SqlDbType.DateTime) { Value = (object?)data.PaymentAuthorizedDate ?? DBNull.Value },
            new SqlParameter("@SupplierCode", SqlDbType.VarChar, 50) { Value = (object?)data.SupplierCode ?? DBNull.Value },

            new SqlParameter("@EPRCompletedByNo", SqlDbType.Int) { Value = (object?)data.EPRCompletedByNo ?? DBNull.Value },
            new SqlParameter("@ChangeLog", SqlDbType.NVarChar, -1) { Value = FormatChangeLog(request.Data) },
            new SqlParameter("@UpdatedBy", SqlDbType.Int) { Value = (object?)request.UpdatedBy ?? DBNull.Value },
            new SqlParameter("@POLineIds", SqlDbType.Xml) { Value = (object?)FormatPOLineIds(data.POLineIds, request.UpdatedBy.ToString()!) ?? DBNull.Value },

            outputParameter
        };

        var result = await _repository.SqlQueryRawReturnValueAsync(
            sql: $"""
                  {StoredProcedures.Insert_EPR}
                  @PurchaseOrderId, @PurchaseOrderNumber, @IsNew, @CompanyName, @OrderValue, 
                  @CurrencyCode, @DeliveryDate, @InAdvance, @UponReceipt, @NetSpecify, 
                  @OtherSpecify, @TT, @Cheque, @CreditCard, @Comments, @Name, @Address, 
                  @Tel, @Fax, @Email, @Name1, @Address1, @Tel1, @Fax1, @Email1, @Comment, 
                  @Name2, @Address2, @Tel2, @Fax2, @Email2, @ProFormaAttached, @RaisedByNo, 
                  @RaisedByDate, @SORSigned, @ForStock, @ValuesCorrect, @Authorized, 
                  @AuthorizedDate, @ERAIMember, @ERAIReported, @DebitNotes, @APOpenOrders, 
                  @ACTotalValue, @ACTotalValue1, @SLComment, @SLTerms, @SLOverdue, 
                  @SLTotalValue, @PaymentAuthorizedBy, @Countersigned, @PaymentAuthorizedDate, 
                  @SupplierCode, @EPRCompletedByNo, @ChangeLog, @UpdatedBy, @POLineIds, @EPRId OUTPUT
                  """,
            parameters: parameters.ToArray()
        );

        if (result.EPRId > 0)
        {
            response.Success = true;
            response.Data = result.EPRId;
        }

        return response;
    }

    private static string FormatPOLineIds(string poLineIds, string createdBy)
    {
        StringBuilder poLineXML = new StringBuilder();

        if (!string.IsNullOrEmpty(poLineIds))
        {
            string[] arrIds = poLineIds.Split(',');
            poLineXML.Append("<POLineEPRDetails>");

            foreach (string id in arrIds)
            {
                poLineXML.Append("<POLineEPRDetail>");
                poLineXML.Append("<POLineID>" + id + "</POLineID>");
                poLineXML.Append("<CreatedBy>" + createdBy + "</CreatedBy>");
                poLineXML.Append("</POLineEPRDetail>");
            }

            poLineXML.Append("</POLineEPRDetails>");
        }

        return poLineXML.ToString();
    }

    private static string FormatChangeLog(CreateEprRequest request)
    {
        StringBuilder log = new StringBuilder();
        log.Append("NEWEPR||");

        if (request.IsNew) log.Append("SupplierNew||");

        log.Append("ValueCurrency||"); // required field
        log.Append("DeliveryDateEPR||"); // required field

        if (request.InAdvance) log.Append("InAdvance||");
        if (request.UponReceipt) log.Append("UponReceipt||");
        if (request.NetSpecify is not null) log.Append("NetSpecify||");
        if (!string.IsNullOrEmpty(request.OtherSpecify)) log.Append("OtherSpecify||");
        if (request.TT) log.Append("TT||");
        if (request.Cheque) log.Append("Cheque||");
        if (request.CreditCard) log.Append("CeditCard||");
        if (!string.IsNullOrEmpty(request.Comments)) log.Append("PaymentComment||");
        if (!string.IsNullOrEmpty(request.Name)) log.Append("RefName||");
        if (!string.IsNullOrEmpty(request.Address)) log.Append("RefAddress||");
        if (!string.IsNullOrEmpty(request.Tel)) log.Append("RefTEL||");
        if (!string.IsNullOrEmpty(request.Fax)) log.Append("RefFax||");
        if (!string.IsNullOrEmpty(request.Email)) log.Append("RefEmail||");
        if (!string.IsNullOrEmpty(request.Name1)) log.Append("RefName1||");
        if (!string.IsNullOrEmpty(request.Address1)) log.Append("RefAddress1||");
        if (!string.IsNullOrEmpty(request.Tel1)) log.Append("RefTel1||");
        if (!string.IsNullOrEmpty(request.Fax1)) log.Append("RefFax1||");
        if (!string.IsNullOrEmpty(request.Email1)) log.Append("RefEmail1||");
        if (!string.IsNullOrEmpty(request.Name2)) log.Append("RefName2||");
        if (!string.IsNullOrEmpty(request.Address2)) log.Append("RefAddress2||");
        if (!string.IsNullOrEmpty(request.Tel2)) log.Append("RefTel2||");
        if (!string.IsNullOrEmpty(request.Fax2)) log.Append("RefFax2||");
        if (!string.IsNullOrEmpty(request.Email2)) log.Append("RefEmail2||");
        if (!string.IsNullOrEmpty(request.Comment)) log.Append("RefComment||");
        if (request.ProFormaAttached) log.Append("Sales_FormaAttached||");
        if (request.RaisedByNo.HasValue) log.Append("Sales_RaisedBy||");
        if (request.RaisedByDate.HasValue) log.Append("Sales_Date||");
        if (request.SORSigned) log.Append("Manager_SORSigned||");
        if (request.ForStock) log.Append("Manager_FORStock||");
        if (!string.IsNullOrEmpty(request.Authorized)) log.Append("Manager_Authorized||");
        if (request.AuthorizedDate.HasValue) log.Append("Manager_Date||");
        if (request.ERAIMember) log.Append("Manager_EARIMember||");
        if (request.ERAIReported) log.Append("Manager_EARIReported||");
        if (request.DebitNotes) log.Append("Manager_OutStanding||");
        if (request.ACTotalValue != decimal.MinValue) log.Append("Manager_TotalValue||");
        if (request.APOpenOrders) log.Append("Manager_AdvancePay||");
        if (request.ACTotalValue1 != decimal.MinValue) log.Append("Manager_TotalValue1||");
        if (!string.IsNullOrEmpty(request.SLComment)) log.Append("Manager_Comments||");
        if (!string.IsNullOrEmpty(request.SLTerms)) log.Append("Manager_SalesLedger||");
        if (request.SLOverdue) log.Append("Manager_SalesOverdue||");
        if (request.SLTotalValue != decimal.MinValue) log.Append("Sales_TotalValue||");
        if (request.EPRCompletedByNo.HasValue) log.Append("Manager_PayAuthBy||");
        if (!string.IsNullOrEmpty(request.Countersigned)) log.Append("Manager_Countersign||");
        if (request.PaymentAuthorizedDate is not null) log.Append("PaymentAuthDate||");
        if (!string.IsNullOrEmpty(request.POLineIds)) log.Append("POLineSerialNo||");

        return log.ToString();
    }
}
