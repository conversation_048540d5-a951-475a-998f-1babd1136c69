﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Commands.UpdatePurchaseOrderApproval
{
    public class UpdatePurchaseOrderApprovalHandler : IRequestHandler<UpdatePurchaseOrderApprovalCommand, BaseResponse<bool>>
    {
        private readonly IBaseRepository<AffectedRows> _baseRepository;

        public UpdatePurchaseOrderApprovalHandler(IBaseRepository<AffectedRows> baseRepository)
        {
            _baseRepository = baseRepository;
        }

        public async Task<BaseResponse<bool>> Handle(UpdatePurchaseOrderApprovalCommand request, CancellationToken cancellationToken)
        {
            var parameters = new List<SqlParameter> {
                new SqlParameter("@PurchaseOrderId", SqlDbType.Int){ Value = request.PurchaseOrderId },
                new SqlParameter("@ApprovedBy", SqlDbType.VarChar, 100){ Value = request.ApprovedBy },
                new SqlParameter("@Approve", SqlDbType.Bit){ Value = request.IsApprove },
                new SqlParameter("@AffectedRows", SqlDbType.Int) { Direction = ParameterDirection.Output }
            };

            var result = await _baseRepository.ExecuteSqlRawAsync(
                sql: $"{StoredProcedures.Update_PurchaseOrder_Approval} @PurchaseOrderId, @ApprovedBy, @Approve, @AffectedRows OUT",
                parameters: parameters.ToArray()
                );

            return new BaseResponse<bool>
            {
                Data = result > 0,
                Success = result > 0,
                Message = result > 0 ? "Purchase order approval updated successfully." : "Failed to update purchase order approval."
            };
        }
    }
}
