﻿using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Dto.MailMessages.ResponseModel;
using GlobalTrader2.Dto.Templates;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetEprById;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetNotifyEprMessageTemplate;

public class GetNotifyEprMessageTemplateHandler : IRequestHandler<GetNotifyEprMessageTemplateQuery, BaseResponse<SubjectAndMessageDto>>
{
    private readonly ISender _mediator;
    private readonly IRazorViewToStringService _razorViewToStringService;

    public GetNotifyEprMessageTemplateHandler(
        ISender mediator,
        IRazorViewToStringService razorViewToStringService)
    {
        _mediator = mediator;
        _razorViewToStringService = razorViewToStringService;
    }

    public async Task<BaseResponse<SubjectAndMessageDto>> Handle(GetNotifyEprMessageTemplateQuery request, CancellationToken cancellationToken)
    {
        var response = new BaseResponse<SubjectAndMessageDto>();
        var eprCommand = await _mediator.Send(new GetEprByIdQuery(request.EprId));
        var epr = eprCommand.Data;

        if (epr is null)
        {
            response.Success = true;
            response.Data = null;
            return response;
        }
        
        var message = await _razorViewToStringService.RenderViewToStringAsync(
            "Templates/_NotifyEprTemplate",
            new NotifyEprTemplate
            {
                PurchaseOrderEpr = $"{epr.PurchaseOrderNumber}-{epr.EPRId}",
                Supplier = $"{epr.CompanyName}",
                ClientName = $"{request.ClientName}"
            }
        );

        var subject = $"EPR {epr.PurchaseOrderNumber}-{epr.EPRId} Notification";

        response.Data = new SubjectAndMessageDto
        {
            Subject = subject,
            Message = message
        };
        response.Success = true;
        return response;
    }
}
