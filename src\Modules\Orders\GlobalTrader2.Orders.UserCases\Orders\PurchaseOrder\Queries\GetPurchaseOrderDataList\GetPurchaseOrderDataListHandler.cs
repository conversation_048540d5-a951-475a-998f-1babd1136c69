﻿using GlobalTrader2.Core.Enums;
using GlobalTrader2.Core.Helpers;
using GlobalTrader2.Dto.PurchaseOrder;

namespace GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderDataList
{
    public class GetPurchaseOrderDataListHandler : IRequestHandler<GetPurchaseOrderDataListQuery, BaseResponse<List<PurchaseOrderDataListDto>>>
    {
        private readonly IBaseRepository<PurchaseOrderDataListReadModel> _purchaseOrderRepository;
        private const int _timeoutSecond = 60;
        public GetPurchaseOrderDataListHandler(
              IBaseRepository<PurchaseOrderDataListReadModel> purchaseOrderRepository)
        {
            _purchaseOrderRepository = purchaseOrderRepository;
        }

        public async Task<BaseResponse<List<PurchaseOrderDataListDto>>> Handle(GetPurchaseOrderDataListQuery request, CancellationToken cancellationToken)
        {
            var viewLevel = (ViewLevelList)request.Data.ViewLevel;
            var parameters = BuildParameters(request, viewLevel);
            var queryResult = await _purchaseOrderRepository
                .SqlQueryRawAsync($"{StoredProcedures.Get_datalistnugget_PurchaseOrderLine} {BuildSqlParametersString(parameters)}"
                , parameters.ToArray(), commandTimeout: _timeoutSecond);
            bool isMakeYellow = false;
            var selectedclientNo = request.Filters?.Client;
            if (request.Data.IsGSA  && !request.Data.IsGlobalUser && selectedclientNo != null)
            {
                isMakeYellow = true;


            }
            var purchaseOrderList = queryResult.Select(z => new PurchaseOrderDataListDto()
            {
                PurchaseOrderId = z.PurchaseOrderId,
                PurchaseOrderLineId = z.PurchaseOrderLineId,
                PurchaseOrderNumber = z.PurchaseOrderNumber,
                Part = z.Part,
                Price = Functions.FormatCurrency(Convert.ToDecimal(z.Price ?? 0), z.CurrencyCode),
                QuantityOrdered = Functions.FormatNumeric(z.QuantityOrdered,request.CultureInfo),
                QuantityOutstanding = Functions.FormatNumeric(z.QuantityOutstanding, request.CultureInfo),
                DeliveryDate = Functions.FormatDate(z.DeliveryDate),
                CompanyName = z.CompanyName,
                CompanyNo = z.CompanyNo,
                ContactName = z.ContactName,
                ContactNo = z.ContactNo,
                ROHS = z.ROHS,
                ManufacturerCode = z.ManufacturerCode,
                ManufacturerNo = z.ManufacturerNo,
                Status = ((PurchaseOrderStatus?)z.Status).ToString(),
                SupplierMessage = Functions.ReplaceLineBreaks(z.SupplierMessage),
                ClientName = z.ClientName,
                InternalPurchaseOrderId = z.InternalPurchaseOrderId,
                InternalPurchaseOrderNumber = z.InternalPurchaseOrderNumber,
                PoClientName = z.PoClientName,
                DeliveryStatus = z.DeliveryStatus,
                RowCSS = z.RowCSS,
                RowNum = z.RowNum,
                AS6081 = z.AS6081,
                RowCnt = z.RowCnt,
                IsMakeYellow = isMakeYellow

            }).ToList();
            return new BaseResponse<List<PurchaseOrderDataListDto>>()
            {
                Success = true,
                Data = purchaseOrderList
            };
        }
        private static List<SqlParameter> BuildParameters(GetPurchaseOrderDataListQuery request, ViewLevelList viewLevel)
        {   bool? AS6081 =  null;
            if (request.Filters?.AS6081 != null && request.Filters?.AS6081 == "1" )
            {
                AS6081 = true;
            }
            else if(request.Filters?.AS6081 != null && request.Filters?.AS6081 != "1")
            {
                AS6081 = false;
            }
            bool poHubOnly = false;
            if(request.IsPOHub && request.Filters?.PohubOnly == true)
            {
                poHubOnly = true;
            }
            var parameters = new List<SqlParameter>()
            {
                   new("@ClientId", SqlDbType.Int){Value = request.Data.ClientId ?? (object)DBNull.Value},
                   new("@TeamId", SqlDbType.Int){Value = viewLevel == ViewLevelList.Team && request.Data.TeamId.HasValue ? request.Data.TeamId : (object)DBNull.Value},
                   new("@DivisionId", SqlDbType.Int){Value = viewLevel == ViewLevelList.Division && request.Data.DivisionId.HasValue ? request.Data.DivisionId : (object)DBNull.Value},
                   new("@LoginId", SqlDbType.Int){Value = viewLevel == ViewLevelList.My && request.Data.LoginId.HasValue ? request.Data.LoginId : (object)DBNull.Value},
                   new("@PageIndex", SqlDbType.Int){Value = request.Data.PageIndex},
                   new("@PageSize", SqlDbType.Int){Value = request.Data.PageSize},
                   new("@OrderBy", SqlDbType.Int){Value = request.Data.OrderBy + 1},
                   new("@SortDir", SqlDbType.Int){Value = request.Data.SortDir},
                   new("@PartSearch", SqlDbType.NVarChar){Value = request.Filters?.Part ?? (object)DBNull.Value},
                   new("@ContactSearch", SqlDbType.NVarChar){Value = request.Filters?.Contact ?? (object)DBNull.Value},
                   new("@CMSearch", SqlDbType.NVarChar){Value = request.Filters?.CMName ?? (object)DBNull.Value},
                   new("@BuyerSearch", SqlDbType.Int){Value = request.Filters?.BuyerName ?? (object)DBNull.Value},
                   new("@CountrySearch", SqlDbType.Int){Value = request.Filters?.Country ?? (object)DBNull.Value},
                   new("@IncludeClosed", SqlDbType.Bit){Value = request.Filters?.IncludeClosed ?? false},
                   new("@PurchaseOrderNoLo", SqlDbType.Int){Value = request.Filters?.PONoLo ??(object) DBNull.Value},
                   new("@PurchaseOrderNoHi", SqlDbType.Int){Value = request.Filters?.PONoHi ??(object) DBNull.Value},
                   new("@DateOrderedFrom", SqlDbType.DateTime){Value = request.Filters?.DateOrderedFrom ??(object) DBNull.Value},
                   new("@DateOrderedTo", SqlDbType.DateTime){Value = request.Filters?.DateOrderedTo ??(object) DBNull.Value},
                   new("@ExpediteDateFrom", SqlDbType.DateTime){Value = request.Filters?.ExpediteDateFrom ??(object) DBNull.Value},
                   new("@ExpediteDateTo", SqlDbType.DateTime){Value = request.Filters?.ExpediteDateTo ??(object) DBNull.Value},
                   new("@DeliveryDateFrom", SqlDbType.DateTime){Value = request.Filters?.DeliveryDateFrom ??(object) DBNull.Value},
                   new("@DeliveryDateTo", SqlDbType.DateTime){Value = request.Filters?.DeliveryDateTo ??(object) DBNull.Value},
                   new("@RecentOnly", SqlDbType.Bit){Value = request.Filters?.RecentOnly ?? false},
                   new("@InternalPurchaseOrderNoLo", SqlDbType.Int){Value = request.Filters?.IPONoLo ??(object) DBNull.Value},
                   new("@InternalPurchaseOrderNoHi", SqlDbType.Int){Value = request.Filters?.IPONoHi ??(object) DBNull.Value},
                   new("@ClientSearch", SqlDbType.Int){Value = request.Filters?.Client ?? (object)DBNull.Value},
                   new("@IsPoHub", SqlDbType.Int){Value = request.IsPOHub? 1 : 0},
                   new("@PoHubOnly", SqlDbType.Bit){Value = poHubOnly},
                   new("@IsGlobalLogin", SqlDbType.Bit){Value = request.Data?.IsGlobalUser ?? false},
                   new("@SOCheckedStatus", SqlDbType.Int){Value = request.Filters?.SOStatus ?? (object)DBNull.Value},
                   new("@SOStatus", SqlDbType.Int){Value = request.Filters?.PurchaseOrderStatus ?? (object)DBNull.Value},
                   new("@SupplierApprovalStatus", SqlDbType.Int){Value = request.Filters?.SupplierApprovalStatus ?? (object)DBNull.Value},
                   new("@AS6081", SqlDbType.Bit){Value = AS6081 ?? (object)DBNull.Value },
                   new("@SelectedLoginId", SqlDbType.Int){Value = request.Data?.LoginId ?? (object)DBNull.Value},
            };
            return parameters;
        }
        public static string BuildSqlParametersString(List<SqlParameter> parameters)
        {
            return string.Join(", ", parameters.Select(x => x.ParameterName));
        }
    }
}
