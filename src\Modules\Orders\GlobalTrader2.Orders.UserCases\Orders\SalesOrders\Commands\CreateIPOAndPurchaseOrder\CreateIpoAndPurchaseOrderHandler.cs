﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Domain.Entities;
using GlobalTrader2.Dto.PurchaseOrder;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.ECCN.Queries.GetECCNRestrictedMailGroup;
using GlobalTrader2.Orders.UserCases.Orders.PurchaseOrder.Queries.GetPurchaseOrderById;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.CreateIPOAndPurchaseOrder
{
    public class CreateIpoAndPurchaseOrderHandler : IRequestHandler<CreateIpoAndPurchaseOrderCommand, BaseResponse<CreateIpoAndPurchaseOrderResult>>
    {
        private readonly IBaseRepository<IsIpoExistReadModel> _isIpoExistRepository;
        private readonly IBaseRepository<SalesOrderLineReadModel> _solRepository;
        private readonly IBaseRepository<object> _baseRepository;
        private readonly IMediator _mediator;

        public CreateIpoAndPurchaseOrderHandler(IBaseRepository<IsIpoExistReadModel> isIpoExistRepository, IBaseRepository<SalesOrderLineReadModel> solRepository, IBaseRepository<object> baseRepository, IMediator mediator)
        {
            _isIpoExistRepository = isIpoExistRepository;
            _solRepository = solRepository;
            _baseRepository = baseRepository;
            _mediator = mediator;
        }

        public async Task<BaseResponse<CreateIpoAndPurchaseOrderResult>> Handle(CreateIpoAndPurchaseOrderCommand request, CancellationToken cancellationToken)
        {
            int result = -1;
            var notifyObjects = new List<CreateIpoNotifyOject>();
            StringBuilder serialMessage = new StringBuilder("");
            string solIds = string.Empty;
            var response = new BaseResponse<CreateIpoAndPurchaseOrderResult>() {
                Success = true,
                Data = new CreateIpoAndPurchaseOrderResult()
                {
                    Result = result,
                    NotifyObjects = notifyObjects,
                    SerialMessage = serialMessage.ToString(),
                    SolIdS = solIds
                }
            };
            int PurchaseOrderNo = default(int);
            int InternalPurchaseOrderNo = default(int);

            #region get SO line from sales order id then filter lines from solines when IPO created from lines
            var salesOrderLines = await GetSOLine(request.SalesOrderId);

            if (request.CreateFrom == 2 && request.SoLineNumbers.Any())
            {
                salesOrderLines = salesOrderLines.Where(l => request.SoLineNumbers.Contains(l.SalesOrderLineId)).ToList();
            }
            #endregion

            #region Check whether IPO and Po created already from lines
            var filteredSalesOrderLines = new List<SalesOrderLineReadModel>();
            foreach (var line in salesOrderLines)
            {
                if (!await IsIPOExist(line.SalesOrderLineId))
                {
                    filteredSalesOrderLines.Add(line);
                }
            }
            salesOrderLines = filteredSalesOrderLines;
            #endregion

            #region Get different supplier and different lines
            var currencyGroups = (from sol in salesOrderLines
                                  group sol by new { sol.POHubSupplierNo, sol.ClientCurrencyNo, sol.ActualCurrencyNo, sol.CurrencyNo, sol.LinkMultiCurrencyNo } into grp
                                  select new
                                  {
                                      HubSupplierNo = grp.Key.POHubSupplierNo,
                                      ClientCurrencyNo = grp.Key.ClientCurrencyNo,
                                      PurchaseBuyCurrencyNo = grp.Key.ActualCurrencyNo,
                                      CurrencyNo = grp.Key.CurrencyNo,
                                      LinkMultiCurrencyNo = grp.Key.LinkMultiCurrencyNo,
                                      count = grp.Count(),
                                  }).ToList();
            #endregion

            #region Create IPO,PO, Allocation, Stock and Notify
            result = currencyGroups.Count == 0 ? 2 : result;
            int numberStockExceedAvailable = await CheckStockFromSO(request.SalesOrderId);
            if (numberStockExceedAvailable == 0)
            {
                foreach (var item in currencyGroups)
                {
                    var linesInIPO = salesOrderLines.Where(
                        x => x.POHubSupplierNo == item.HubSupplierNo
                        && x.ClientCurrencyNo == item.ClientCurrencyNo
                        && x.ActualCurrencyNo == item.PurchaseBuyCurrencyNo
                        && x.CurrencyNo == item.CurrencyNo
                        && x.LinkMultiCurrencyNo == item.LinkMultiCurrencyNo).ToList();
                    string soLineIds = string.Join(",", linesInIPO.Select(l => l.SalesOrderLineId));
                    int poHubSupplierNo = item.HubSupplierNo ?? 0;
                    int sourcingResultAttachedBy = linesInIPO[0].SourceingResultAttachedBy ?? 0;
                    int sourcingResultNo = linesInIPO[0].SourcingResultNo;
                    double? estimatedShippingCost = null;
                    DateTime? deliveryDate = DateTime.Now;

                    int clientCurrencyNo = item.ClientCurrencyNo ?? 0;
                    int poBuyCurrencyNo = item.PurchaseBuyCurrencyNo ?? 0;

                    int currencyNo = item.CurrencyNo;
                    int linkMultiCurrencyNo = item.LinkMultiCurrencyNo ?? 0;

                    double convertedEstimatedCost = 0;

                    var createResult = await CreateIpoAndPurchaseOrder(new CreateIpoAndPurchaseOrderObject()
                    {
                        SalesOrderNo = request.SalesOrderId,
                        SalesOrderLineIds = soLineIds,
                        POHubSupplierNo = poHubSupplierNo,
                        ClientNo = request.GlobalClientNo,
                        WarehouseNo = request.WarehouseNo,
                        SourcingResultAttachedBy = sourcingResultAttachedBy,
                        SourcingResultNo = sourcingResultNo,
                        EstimatedShippingCost = estimatedShippingCost,
                        DeliveryDate = deliveryDate,
                        ConvertedEstimatedCost = convertedEstimatedCost,
                        ClientCurrencyNo = clientCurrencyNo,
                        POBuyCurrencyNo = poBuyCurrencyNo,
                        CurrencyNo = currencyNo,
                        LinkMultiCurrencyNo = linkMultiCurrencyNo,
                        InternalPurchaseOrderNo = InternalPurchaseOrderNo,
                        PurchaseOrderNo = PurchaseOrderNo,
                        SolIds = solIds
                    });

                    InternalPurchaseOrderNo = createResult.InternalPurchaseOrderNo;
                    PurchaseOrderNo = createResult.PurcharseOrderNo;
                    solIds = createResult.SOL;
                    result = createResult.Result;

                    serialMessage.Append(solIds);
                    serialMessage.Append(",");
                    #region get notify information to notiy to user ECCN Used
                    if (result == 1) {
                        var purchaseOrder = await GetPurchaseOrder(PurchaseOrderNo, request.GlobalClientNo);
                        if (purchaseOrder != null) {
                            notifyObjects.Add(new CreateIpoNotifyOject()
                            {
                                PurchaseOrderId = purchaseOrder.PurchaseOrderId,
                                PurchaseOrderNumber = purchaseOrder.PurchaseOrderNumber,
                                SourcingResultAttachedBy = sourcingResultAttachedBy,
                            });
                        }
                    }
                    #endregion
                }
            }
            else
            {
                result = 4;
            }
            #endregion
            response.Data.SolIdS = solIds;
            response.Data.Result = result;
            response.Data.NotifyObjects = notifyObjects;
            response.Data.SerialMessage = serialMessage.ToString().TrimEnd(',');
            return response;
        }

        private async Task<List<SalesOrderLineReadModel>> GetSOLine(int salesOrderId)
        {
            var sqlParameter = new List<SqlParameter>()
                {
                    new SqlParameter("@SoId", SqlDbType.Int) { Value = salesOrderId }
                };
            var solines = await _solRepository.SqlQueryRawAsync($"{StoredProcedures.Get_SOLine} @SoId", sqlParameter.ToArray());
            return solines.ToList();
        }

        private async Task<bool> IsIPOExist(int salesOrderLineId)
        {
            var isIPOExistParameters = new List<SqlParameter>()
                        {
                            new SqlParameter("@SoLineId", SqlDbType.Int){ Value = salesOrderLineId },
                            new SqlParameter("@IsIPOExist", SqlDbType.Int) { Direction = ParameterDirection.Output },
                        };

            var result = await _isIpoExistRepository.SqlQueryRawReturnValueAsync(
                sql: $"{StoredProcedures.IsIPOAlreadyCreated} @SoLineId, @IsIPOExist Output",
                parameters: isIPOExistParameters.ToArray());
            return result.IsIPOExist == 1;
        }

        private async Task<int> CheckStockFromSO(int salesOrderId)
        {
            var result = new SqlParameter("@Result", SqlDbType.Int) { Direction = ParameterDirection.Output, Value = 0 };
            var sqlParameters = new List<SqlParameter>()
            {
                new SqlParameter("@SoId", SqlDbType.Int) { Value = salesOrderId },
                result,
            };

            await _baseRepository.SqlQueryRawReturnValueAsync(
                 sql: $"{StoredProcedures.Get_CheckStockFromSO} @SoId, @Result Output",
                 parameters: sqlParameters.ToArray());
            return (int)result.Value;
        }

        private async Task<CreateIpoAndPurchaseOrderStoreProcResult> CreateIpoAndPurchaseOrder(CreateIpoAndPurchaseOrderObject createObject)
        {
            var internalPurchaseOrderNo = new SqlParameter("@InternalPurchaseOrderNo", SqlDbType.Int) { Direction = ParameterDirection.Output };
            var purchaseOrderNo = new SqlParameter("@PurchaseOrderNo", SqlDbType.Int) { Direction = ParameterDirection.Output };
            var result = new SqlParameter("@Result", SqlDbType.Int) { Direction = ParameterDirection.Output };
            var sol = new SqlParameter("@SOL", SqlDbType.NVarChar) { Direction = ParameterDirection.Output, Size = -1 };
            var sqlParameter = new List<SqlParameter>() {
                new SqlParameter("@SalesOrderNo", SqlDbType.Int) {Value = createObject.SalesOrderNo },
                new SqlParameter("@SalesOrderLines", SqlDbType.VarChar){Value = createObject.SalesOrderLineIds, Size = -1},
                new SqlParameter("@POHubSupplierNo", SqlDbType.Int){Value = createObject.POHubSupplierNo},
                new SqlParameter("@ClientNo", SqlDbType.Int){Value = createObject.ClientNo},
                new SqlParameter("@WarehouseNo", SqlDbType.Int){Value = createObject.WarehouseNo},
                new SqlParameter("@SourceingResultAttachedBy", SqlDbType.Int){Value = createObject.SourcingResultAttachedBy},
                new SqlParameter("@SourceingResultNo", SqlDbType.Int){Value = createObject.SourcingResultNo },
                new SqlParameter("@IPOClientCurrencyNo", SqlDbType.Int){Value = createObject.ClientCurrencyNo},
                new SqlParameter("@POBuyCurrencyNo", SqlDbType.Int){Value = createObject.POBuyCurrencyNo},
                new SqlParameter("@SrCurrencyNo", SqlDbType.Int){Value = createObject.CurrencyNo},
                new SqlParameter("@LinkMultiCurrencyNo", SqlDbType.Int) { Value = createObject.LinkMultiCurrencyNo },
                internalPurchaseOrderNo,
                purchaseOrderNo,
                result,
                sol
            };

            await _baseRepository.ExecuteSqlRawAsync
                ($"{StoredProcedures.Insert_InternalPurchaseOrder} @SalesOrderNo, @SalesOrderLines, @POHubSupplierNo,@ClientNo,@WarehouseNo, @SourceingResultAttachedBy, @SourceingResultNo, @IPOClientCurrencyNo, @POBuyCurrencyNo, @SrCurrencyNo, @LinkMultiCurrencyNo, @InternalPurchaseOrderNo OUTPUT, @PurchaseOrderNo OUTPUT, @Result OUTPUT, @SOL OUTPUT"
                , sqlParameter.ToArray());

            return new CreateIpoAndPurchaseOrderStoreProcResult(
               internalPurchaseOrderNo.Value == DBNull.Value || internalPurchaseOrderNo.Value == null ? 0 : (int)internalPurchaseOrderNo.Value,
               purchaseOrderNo.Value == DBNull.Value || purchaseOrderNo.Value == null ? 0 : (int)purchaseOrderNo.Value,
               result.Value == DBNull.Value || result.Value == null ? 0 : (int)result.Value,
               sol.Value == DBNull.Value || sol.Value == null ? string.Empty : sol.Value.ToString()!);
        }

        private async Task<PurchaseOrderDetailsReadModel?> GetPurchaseOrder(int purchaseOrderId, int clientId) { 
            var query = new GetPurcharseOrderByIdQuery()
            { 
                PurchaseOrderId = purchaseOrderId,
                ClientId = clientId
            };
            var result = await _mediator.Send(query);
            return result.Data;
        }

        public record CreateIpoAndPurchaseOrderStoreProcResult(int InternalPurchaseOrderNo, int PurcharseOrderNo, int Result, string SOL);
    }
}




