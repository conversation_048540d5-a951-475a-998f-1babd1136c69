﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Azure.Core;
using GlobalTrader2.Core.Constants;
using GlobalTrader2.Core.Interfaces;
using GlobalTrader2.Core.Models.PowerAutomate;
using GlobalTrader2.Dto.MailGroupMembers;
using GlobalTrader2.Dto.PowerAppToken;
using GlobalTrader2.Dto.SalesOrderLine;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetExportApprovalDataById;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetOgelEccnMemberEmail;
using GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPowerAppTokenInfo;
using Microsoft.IdentityModel.Protocols;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Commands.RequestApprovalExportApproval
{
    public class RequestApprovalExportApprovalHandler : IRequestHandler<RequestApprovalExportApprovalCommand, BaseResponse<bool>>
    {
        private readonly IMediator _mediator;
        private readonly IPowerAutomateApiClient _powerAutomateApiClient;
        public RequestApprovalExportApprovalHandler(IMediator mediator, IPowerAutomateApiClient powerAutomateApiClient)
        {
            _mediator = mediator;
            _powerAutomateApiClient = powerAutomateApiClient;
        }

        public async Task<BaseResponse<bool>> Handle(RequestApprovalExportApprovalCommand request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<bool>();
            var mailGroupMemberEmails = await GetOgelEccnMailGroupMemberEmailsAsync(request.ClientId);
            var toEmails = string.Join(";", mailGroupMemberEmails.Select(x => x.Email));
            var exportApprovalData = await GetExportApprovalDataAsync(request.ExportApprovalId);
            var powerAppToken = await GetPowerAppTokenAsync(request.LoginId, PowerAutomateFlow.Export_Approval);

            if (toEmails.Any() && exportApprovalData != null && powerAppToken != null)
            {
                var redirectSODetailUrl = request.RedirectBaseUrlV2 + request.SODetailUrl;
                var approvalNotificationInfo = new ApproveExportApprovalNotificationInfo()
                {
                    RedirectBaseUrlV1 = request.RedirectBaseUrlV1,
                    RedirectSoDetailUrl = redirectSODetailUrl,
                    ApprovalId = request.ExportApprovalId,
                    EuuFormName = exportApprovalData.EUUFormName,
                    SalesOrderNumber = exportApprovalData.SalesOrderNumber,
                    SoReportFileName = exportApprovalData.EUUUPDFploadName,
                    RequestId = powerAppToken.RequestId,
                    Subject = request.Subject,
                    ToAddress = toEmails,
                    TokenValue = powerAppToken.TokenValue,
                    Uri = exportApprovalData.PowerExportUrl,
                    UserId = request.LoginId
                };
                await SendSOApprovalToTeam(approvalNotificationInfo,exportApprovalData).ConfigureAwait(false);
            }
            response.Success = true;
            return response;
        }

        private async Task<IEnumerable<OgelEccnGroupMemberEmailDto>> GetOgelEccnMailGroupMemberEmailsAsync(int clientId)
        {
            return (await _mediator.Send(new GetOgelEccnMemberEmailQuery { ClientId = clientId })).Data ?? [];
        }

        private async Task<ExportApprovalDataDto?> GetExportApprovalDataAsync(int exportApprovalId)
        {
            return (await _mediator.Send(new GetExportApprovalDataByIdQuery { ExportApprovalId = exportApprovalId })).Data;
        }

        private async Task<PowerAppTokenDto?> GetPowerAppTokenAsync(int loginId, string workFlowType)
        {
            return (await _mediator.Send(new GetPowerAppTokenInfoQuery(loginId, workFlowType,true))).Data;
        }

        private async Task SendSOApprovalToTeam(ApproveExportApprovalNotificationInfo info, ExportApprovalDataDto sor)
        {
            try
            {
                string poReport = "";
                string soReport = "";
                string soReportAttachment = "";
                string euuFormUrl = "";

                if (info.EuuFormName != "File Not Uploaded")
                {
                    soReport = $"[{info.EuuFormName}]({info.RedirectBaseUrlV1}/PowerApp.ashx?RequestType=EUUForm&EUUFileName={info.SoReportFileName})";
                    euuFormUrl = $"{info.RedirectBaseUrlV1}/PowerApp.ashx?RequestType=EUUForm&EUUFileName={info.SoReportFileName}";
                }
                else
                {
                    soReport = info.EuuFormName;
                }

                var requestBody = new RequestExportApprovalRequest()
                {
                    Email = info.ToAddress,
                    UserId = info.UserId.ToString(),
                    Subject = info.Subject,
                    ExportApprovalId = info.ApprovalId.ToString(),
                    SOReportUrl = soReport,
                    POReportUrl = poReport,
                    SOReportAttachment = soReportAttachment,
                    EUUFornName = info.EuuFormName,
                    RequestId = info.RequestId.ToString(),
                    TokenValue = info.TokenValue,
                    SalesPerson = sor.SalesPersonName,
                    SalesOrderNumber = info.SalesOrderNumber.ToString(),
                    SOLineNumber = sor.lineNumber.ToString(),
                    Customer = sor.CustomerName,
                    PartNumber = sor.Part,
                    ShipFromWarehouse = sor.ShipFromWarehouse,
                    ShipFromCountry = sor.ShipFromCountry,
                    ShipToCust = sor.ShipToCustomerName,
                    ShipToCustCountry = sor.ShipToCustomerCountry,
                    CommodityCode = sor.CommodityCode,
                    ECCN = sor.ECCN,
                    DestinationCountry = sor.DestinationCountry,
                    MilitaryUse = sor.MilitaryUse,
                    EndUser = sor.EndUser,
                    PartApplication = sor.PartApplication,
                    ExportControl = (sor.ExportControl == 0) ? "No" : "Yes",
                    AerospaceUse = (sor.AerospaceUse == 0) ? "No" : "Yes",
                    PartsTest = sor.PartTested,
                    SODetailURL = info.RedirectSoDetailUrl,
                    EUUFormUrl = euuFormUrl,
                    CountryOfOrigin = sor.CountryOfOrigin,
                };
                await _powerAutomateApiClient.SendRequestApprovalExportApprovalNotificationAsync(info.Uri ?? string.Empty, requestBody);
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                throw;
            }
        }
    }
}
