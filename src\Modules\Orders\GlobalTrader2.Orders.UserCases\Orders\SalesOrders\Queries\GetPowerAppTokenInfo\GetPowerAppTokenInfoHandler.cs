﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.Dto.PowerAppToken;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPowerAppTokenInfo
{
    public class GetPowerAppTokenInfoHandler : IRequestHandler<GetPowerAppTokenInfoQuery, BaseResponse<PowerAppTokenDto>>
    {
        private readonly IBaseRepository<PowerAppTokenInfoReadModel> _repository;
        private readonly IMapper _mapper;

        public GetPowerAppTokenInfoHandler(IBaseRepository<PowerAppTokenInfoReadModel> repository, IMapper mapper)
        {
            _repository = repository;
            _mapper = mapper;
        }

        public async Task<BaseResponse<PowerAppTokenDto>> Handle(GetPowerAppTokenInfoQuery request, CancellationToken cancellationToken)
        {
            var response = new BaseResponse<PowerAppTokenDto>()
            {
                Success = true,
            };
            var sqlParameters = new List<SqlParameter>() {
                new SqlParameter("@LoginId", SqlDbType.Int){ Value = request.LoginId},
                new SqlParameter("@WorkFlowType", SqlDbType.NVarChar){ Value = request.WorkFlowType},
                new SqlParameter("@IsNotifySO", SqlDbType.Bit){ Value = request.IsNotifySO},
            };
            var tokenReadModel = await _repository.SqlQueryRawAsync(
                sql:$"{StoredProcedures.Get_PowerApp_TokenInfo} @LoginId, @WorkFlowType, @IsNotifySO",                    
                parameters: sqlParameters.ToArray());
            if(tokenReadModel.Count == 0)
                return response;
            response.Data = _mapper.Map<PowerAppTokenDto>(tokenReadModel[0]);
            return response;
        }
    }
}
