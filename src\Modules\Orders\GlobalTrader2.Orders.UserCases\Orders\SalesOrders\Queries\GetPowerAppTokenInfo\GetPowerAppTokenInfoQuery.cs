﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using GlobalTrader2.Dto.PowerAppToken;

namespace GlobalTrader2.Orders.UserCases.Orders.SalesOrders.Queries.GetPowerAppTokenInfo
{
    public class GetPowerAppTokenInfoQuery : IRequest<BaseResponse<PowerAppTokenDto>>
    {
        public GetPowerAppTokenInfoQuery(int loginId, string? workFlowType, bool isNotifySO)
        {
            LoginId = loginId;
            WorkFlowType = workFlowType;
            IsNotifySO = isNotifySO;
        }

        public int LoginId { get; set; }
        public string? WorkFlowType { get; set; }
        public bool IsNotifySO { get; set; }
    }
}
