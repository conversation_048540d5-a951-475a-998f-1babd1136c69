﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace GlobalTrader2.Dto.PurchaseOrderEpr;

public class CreateEprRequest
{
    // Purchase order details
    [Required]
    public int? PurchaseOrderNumber { get; set; }

    // Order details
    public bool IsNew { get; set; }
    [MaxLength(250)]
    public string? CompanyName { get; set; }
    [Required]
    public decimal OrderValue { get; set; }
    [MaxLength(50)]
    public string? CurrencyCode { get; set; }
    [Required]
    public DateTime DeliveryDate { get; set; }

    // Payment terms
    public bool InAdvance { get; set; }
    public bool UponReceipt { get; set; }
    public int? NetSpecify { get; set; }
    public string? OtherSpecify { get; set; }

    // Payment method
    public bool TT { get; set; }
    public bool Cheque { get; set; }
    public bool CreditCard { get; set; }
    public string? Comments { get; set; }

    // Referenecs
    [MaxLength(250)]
    public string? Name { get; set; }
    [MaxLength(500)]
    public string? Address { get; set; }
    [MaxLength(50)]
    public string? Tel { get; set; }
    [MaxLength(50)]
    public string? Fax { get; set; }
    [MaxLength(250)]
    public string? Email { get; set; }
    [MaxLength(250)]
    public string? Name1 { get; set; }
    [MaxLength(500)]
    public string? Address1 { get; set; }
    [MaxLength(50)]
    public string? Tel1 { get; set; }
    [MaxLength(50)]
    public string? Fax1 { get; set; }
    [MaxLength(250)]
    public string? Email1 { get; set; }
    public string? Comment { get; set; }
    [MaxLength(250)]
    public string? Name2 { get; set; }
    [MaxLength(500)]
    public string? Address2 { get; set; }
    [MaxLength(50)]
    public string? Tel2 { get; set; }
    [MaxLength(50)]
    public string? Fax2 { get; set; }
    [MaxLength(250)]
    public string? Email2 { get; set; }

    // References sales
    public bool ProFormaAttached { get; set; }
    public int? RaisedByNo { get; set; }
    [Required]
    public DateTime? RaisedByDate { get; set; }

    // Referenecs manager
    public bool SORSigned { get; set; }
    public bool ForStock { get; set; }
    public bool ValuesCorrect { get; set; }
    [MaxLength(250)]
    public string? Authorized { get; set; }
    public DateTime? AuthorizedDate { get; set; }

    // Accounts only
    public bool ERAIMember { get; set; }
    public bool ERAIReported { get; set; }
    public bool DebitNotes { get; set; }
    public bool APOpenOrders { get; set; }
    public decimal ACTotalValue { get; set; }
    public decimal ACTotalValue1 { get; set; }
    public string? SLComment { get; set; }
    [MaxLength(500)]
    public string? SLTerms { get; set; }
    public bool SLOverdue { get; set; }
    public decimal SLTotalValue { get; set; }
    [MaxLength(250)]
    public string? PaymentAuthorizedBy { get; set; }
    [MaxLength(250)]
    public string? Countersigned { get; set; }
    public DateTime? PaymentAuthorizedDate { get; set; }
    [MaxLength(250)]
    public string? SupplierCode { get; set; }
    public int? EPRCompletedByNo { get; set; }

    public string POLineIds { get; set; } = string.Empty;
}
