﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GlobalTrader2.Dto.Quote
{
    public class GetQuoteListDto
    {
        public int? QuoteId { get; set; }
        public int? QuoteNumber { get; set; }
        public string? Part { get; set; }
        public string? Price { get; set; }
        public string? CurrencyCode { get; set; }
        public int? Quantity { get; set; }
        public string? DateQuoted { get; set; }
        public string? CompanyName { get; set; }
        public int? CompanyNo { get; set; }
        public string? ContactName { get; set; }
        public int? ContactNo { get; set; }
        public byte? ROHS { get; set; }
        public string? TotalInBase { get; set; }
        public string? TotalValue { get; set; }
        public int? CurrencyNo { get; set; }
        public int? Salesman { get; set; }
        public string? SalesmanName { get; set; }
        public string? QuoteStatusName { get; set; }
        public string? OfferProfit { get; set; }
        public long? RowNum { get; set; }
        public bool? AS6081 { get; set; }
        public string? QuoteOfferedDate { get; set; }
        public string? DateOfferStatus { get; set; }
        public int? TaskCount { get; set; }
        public int? HasUnFinishedTask { get; set; }
        public int? RowCnt { get; set; }
    }
}
