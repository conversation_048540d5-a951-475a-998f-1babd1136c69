using GlobalTrader2.Core.Enums;
using GlobalTrader2.Dto.Companies;
using GlobalTrader2.Dto.DataListNugget;
using GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.Base;
using GlobalTrader2.SharedUI.Constants;
using GlobalTrader2.SharedUI.Enums;
using GlobalTrader2.SharedUI.Models;
using GlobalTrader2.SharedUI.Services;
using GlobalTrader2.UserAccount.UseCases.FilterState.Queries.GetFilterStates;
using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace GlobalTrader2.SharedUI.Areas.Common.Pages.Shared.Components.CompanyQuickBrowse
{
    public class CompanyQuickBrowse : QuickBrowseBaseViewComponent
    {
        public CompanyQuickBrowse(ISender mediator, SecurityManager securityManager, SessionManager sessionManager) : base(securityManager, sessionManager, mediator)
        {
        }
        
        public async Task<IViewComponentResult> InvokeAsync(CompanyListType companyListType)
        {
            var loginId = HttpContext.Session.GetInt32(SessionKey.LoginID);
            var defaultListPageView = _sessionManager.DefaultListPageView;
            
            Enums.SecurityFunction enmPageId = 0;
            switch (companyListType)
            {
                case CompanyListType.AllCompanies:
                    enmPageId = SecurityFunction.Contact_AllCompanies_View;
                    break;
                case CompanyListType.Customers:
                    enmPageId = SecurityFunction.Contact_Customers_View;
                    break;
                case CompanyListType.Suppliers:
                    enmPageId = SecurityFunction.Contact_Suppliers_View;
                    break;
                case CompanyListType.Prospects:
                    enmPageId = SecurityFunction.Contact_Prospects_View;
                    break;
            }
            var viewLevels = await GetViewLevels((int)enmPageId);
            var dataListNuggetState = await GetDataListNuggetState((int)DataListNuggets.Companies, ((int)companyListType).ToString(), loginId.GetValueOrDefault());
            var viewModel = new CompanyQuickBrowseViewModel
            {
                DataListNuggetState = dataListNuggetState,
                Config = new CompanyQuickBrowseConfig
                {
                    EnmPageId = (int)enmPageId,
                    InitViewLevel = viewLevels.Any(x => x.Id == (int)defaultListPageView) ? (int)defaultListPageView : (int)ViewLevelList.My,
                    CanViewAllCompanyStatus = _securityManager.CheckSectionPermission(SecurityFunction.Contact_Company_View_Active_Inactive),
                    InitSaveDataListState = _sessionManager.GetBool(SessionKey.SaveDataListNuggetStateByDefault),
                    IsGlobalLogin = _sessionManager.IsGlobalUser,
                    IsGSA = _sessionManager.IsGSA,
                    CurrentClientId = _sessionManager.ClientID.GetValueOrDefault(),
                }
            };
            return View(viewModel);
        }

        private async Task<DataListNuggetState<CompanyFilter>> GetDataListNuggetState(int intDataListNuggetID, string strDataListNuggetSubType, int userId)
        {
            var dataListNuggetState = await _mediator.Send(new GetFilterStatesQuery
            {
                DataListNuggetNo = intDataListNuggetID,
                LoginNo = userId,
                SubType = strDataListNuggetSubType,
            });

            if (dataListNuggetState.Success && dataListNuggetState.Data != null)
            {
                return DataListNuggetState<CompanyFilter>.Deserialize(dataListNuggetState.Data.StateText);
            }

            return new DataListNuggetState<CompanyFilter>();
        }
    }

    public class CompanyQuickBrowseViewModel
    {
        public DataListNuggetState<CompanyFilter> DataListNuggetState { get; set; } = new DataListNuggetState<CompanyFilter>();
        public CompanyQuickBrowseConfig Config { get; set; } = new CompanyQuickBrowseConfig();
    }

    public class CompanyQuickBrowseConfig
    {
        public int EnmPageId { get; set; }
        public int InitViewLevel { get; set; }
        public bool CanViewAllCompanyStatus { get; set; }
        public bool InitSaveDataListState { get; set; }
        public bool IsGlobalLogin { get; set; }
        public bool IsGSA { get; set; }
        public int CurrentClientId { get; set; }
    }
}
