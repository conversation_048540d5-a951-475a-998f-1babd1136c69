﻿@using GlobalTrader2.SharedUI.Areas.Containers.Pages.Shared.Components.CollapsibleFieldSetLite
@model CollapsibleFieldSetLiteModel

<fieldset id="@Model.CollapsibleId-container" class="collapsible-fieldset" style="background: @Model.BackgroundColor; border-color: @Model.BorderColor; display: @(Model.IsShow ? "" : "none")">
    <legend id="<EMAIL>" style="border-color: @Model.BorderColor">
        <span class="d-flex align-items-center gap-1">
            @if (@Model.IsOpen)
            {
                <button class="btn btn-outline-primary fieldset-expand-button" aria-expanded="true" data-bs-toggle="collapse" data-bs-target="#@Model.CollapsibleId">
                    <img class="fieldset-expand-icon" src="~/img/icons/green-plus.svg" alt="Edit icon" width="18" height="18" />
                    <img class="fieldset-collapse-icon" src="~/img/icons/green-minus.svg" alt="Edit icon" width="18" height="18" />
                </button>
            } else
            {
                <button class="btn btn-outline-primary fieldset-expand-button collapsed" aria-expanded="false" data-bs-toggle="collapse" data-bs-target="#@Model.CollapsibleId">
                    <img class="fieldset-expand-icon" src="~/img/icons/green-plus.svg" alt="Edit icon" width="18" height="18" />
                    <img class="fieldset-collapse-icon" src="~/img/icons/green-minus.svg" alt="Edit icon" width="18" height="18" />
                </button>
            }
            <span class="collapsible-title">@Model.Title (<span class="rowsCount">0</span>)</span>
            <a tabindex="0" id="@Model.CollapsibleId-refresh-button" class="select-menu-gtv2-refresh-button collapsible-box-refresh-button">
                <img alt="refresh-button" src="/img/icons/refresh-button.png">
            </a>
        </span>
    </legend>
    <div class="collapse @(Model.IsOpen ? "show" : "")" id="@Model.CollapsibleId">
        @await Component.InvokeAsync(Model.ChildViewComponentName, Model.ChildComponentViewModel)
    </div>
</fieldset>