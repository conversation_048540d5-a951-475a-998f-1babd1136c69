﻿@using GlobalTrader2.SharedUI.Interfaces
@using Microsoft.AspNetCore.Mvc.Localization
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<GlobalTrader2.SharedUI.CommonResources> _commonLocalizer
@inject IStringLocalizer<GlobalTrader2.SharedUI.MessageResources> _messageLocalizer
@inject IViewLocalizer _localizer
@model GlobalTrader2.SharedUI.Models.NotifyEmailModel

<div class="dialog-container" id="@Model.ContainerId" title="@Model.Title" style="display: none;">
    <div class="dialog-description mb-2">
        <div class="d-flex justify-content-between">
            <div class="text-uppercz`ase">
                <h5 class="text-uppercase">@Model.Description</h5>
            </div>
            <span>
                <span class="me-1 required">*</span>@_commonLocalizer["denotes a required field"]
            </span>
        </div>
        <div class="line"></div>
        <span class="mb-2">
            @_localizer["Notify others about this EPR"]
        </span>
    </div>
    <div class="form-error-summary" style="display: none;">
        <img src="~/img/icons/x-octagon.svg" alt="Add icon" />
        <div>
            <p>@_messageLocalizer["There were some problems with your form."]</p>
            <p>@_messageLocalizer["Please check below and try again."]</p>
        </div>
    </div>
    <form class="row common-form" id="@Model.FormId" method="post">
        @Html.AntiForgeryToken()
        <div class="row form-control-wrapper mb-1">
            <div class="col-3">
                <label for="to-input" class="form-label d-inline-block">
                    @_localizer["To"]
                    <span class="fw-bold me-1 required">*</span>
                </label>
            </div>
            <div class="col-4">
                <input type="text" id="to-input-search" class="form-control form-input" />
                <input type="text" id="to-input" name="To" hidden />
            </div>
        </div>
        <div class="row form-control-wrapper mb-1">
            <div class="col-3">
                <label for="to" class="form-label d-inline-block">
                    @_localizer["Subject"]
                    <span class="fw-bold me-1 required">*</span>
                </label>
            </div>
            <div class="col-9">
                <input type="text" name="Subject" maxlength="256" class="form-control form-input" required>
            </div>
        </div>
        <div class="row form-control-wrapper">
            <div class="col-3">
                <label for="message" class="form-label d-inline-block">
                    @_localizer["Message"]
                    <span class="fw-bold me-1 required">*</span>
                </label>
            </div>
            <div class="col-9">
                <textarea id="message-input" name="Message" class="form-control form-textarea" rows="8" maxlength="60000" style="height: auto;"></textarea>
            </div>
        </div>
    </form>
</div>
