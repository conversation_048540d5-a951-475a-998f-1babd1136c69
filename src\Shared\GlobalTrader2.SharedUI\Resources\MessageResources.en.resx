﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="Login_Error_Message" xml:space="preserve">
    <value>An error has occurred. Please contact the Administrator for assistance.</value>
  </data>
  <data name="Login_Error_Title" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Your changes were saved" xml:space="preserve">
    <value>Your changes were saved</value>
  </data>
  <data name="Not all changes were added successfully" xml:space="preserve">
    <value>Not all changes were added successfully</value>
  </data>
  <data name="Fail to save your changes" xml:space="preserve">
    <value>Fail to save your changes</value>
  </data>
  <data name="Restricted Manufacturer Message" xml:space="preserve">
    <value>Unable to add selected offer due to the manufacturer being restricted. Please select an alternative.</value>
  </data>
  <data name="Some offers failed to be added" xml:space="preserve">
    <value>The offer you have tried to add is already released on another requirement. Please select a different offer and reattach.</value>
  </data>
  <data name="Add To Requrement Reverse Logistics Message" xml:space="preserve">
    <value>The offer you have tried to add requires Reverse Logistics authorisation to be added to the HUBRFQ. Please send HUBRFQ to DMCC for them to approve/confirm the offer before proceeding.</value>
  </data>
  <data name="Parts added successfully:" xml:space="preserve">
    <value>Parts added successfully:</value>
  </data>
  <data name="Parts not added:" xml:space="preserve">
    <value>Parts not added:</value>
  </data>
  <data name="Reason:" xml:space="preserve">
    <value>Reason:</value>
  </data>
  <data name="Below offers cannot be added to requirement" xml:space="preserve">
    <value>Below offers cannot be added to requirement</value>
  </data>
  <data name="Part Numbers:" xml:space="preserve">
    <value>Part Numbers:</value>
  </data>
  <data name="Requirement Number:" xml:space="preserve">
    <value>Requirement Number:</value>
  </data>
  <data name="Please enter a value" xml:space="preserve">
    <value>Please enter a value</value>
  </data>
  <data name="RequiredField" xml:space="preserve">
    <value>Please enter a value</value>
  </data>
  <data name="Please enter a number from" xml:space="preserve">
    <value>Please enter a number from</value>
  </data>
  <data name="Please check below and try again." xml:space="preserve">
    <value>Please check below and try again.</value>
  </data>
  <data name="There were some problems with your form." xml:space="preserve">
    <value>There were some problems with your form.</value>
  </data>
  <data name="NoResult" xml:space="preserve">
    <value>No results found</value>
  </data>
  <data name="ErrorSummary" xml:space="preserve">
    <value>There were some problems with your form. Please check below and try again.</value>
  </data>
  <data name="No_Data_Received" xml:space="preserve">
    <value>An unexpected error occurred. No data received.</value>
  </data>
  <data name="Unexpected_Error" xml:space="preserve">
    <value>An unexpected error occurred</value>
  </data>
  <data name="The Login Name you entered has already been taken" xml:space="preserve">
    <value>The Login Name you entered has already been taken</value>
  </data>
  <data name="The AD Email you entered has already been taken" xml:space="preserve">
    <value>The AD Email you entered has already been taken</value>
  </data>
  <data name="The Security Group no longer exists." xml:space="preserve">
    <value>The Security Group no longer exists.</value>
  </data>
  <data name="You cannot delete a locked security group. Please try again." xml:space="preserve">
    <value>You cannot delete a locked security group. Please try again.</value>
  </data>
  <data name="The Security Group still has members. Please remove the users and try again." xml:space="preserve">
    <value>The Security Group still has members. Please remove the users and try again.</value>
  </data>
  <data name="The Security Group is locked and cannot be deleted." xml:space="preserve">
    <value>The Security Group is locked and cannot be deleted.</value>
  </data>
  <data name="The Security Group does not exist. Please try again." xml:space="preserve">
    <value>The Security Group does not exist. Please try again.</value>
  </data>
  <data name="NoDataFound" xml:space="preserve">
    <value>Sorry, no data was found</value>
  </data>
  <data name="There were some problems with your form. Please check below and try again. The Administrators Group must retain at least one member." xml:space="preserve">
    <value>There were some problems with your form. Please check below and try again. The Administrators Group must retain at least one member.</value>
  </data>
  <data name="The Security Group is a system group and cannot be deleted." xml:space="preserve">
    <value>The Security Group is a system group and cannot be deleted.</value>
  </data>
  <data name="to" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="Sorry, there was a problem with your entries. It may be that the username you have entered has already been taken. Please try again." xml:space="preserve">
    <value>Sorry, there was a problem with your entries. It may be that the username you have entered has already been taken. Please try again.</value>
  </data>
  <data name="The Session has been terminated" xml:space="preserve">
    <value>The Session has been terminated.</value>
  </data>
  <data name="The file you have selected is not an allowed type" xml:space="preserve">
    <value>The file you have selected is not an allowed type</value>
  </data>
  <data name="The file you have selected is too large. Please select a file smaller than {0} MB" xml:space="preserve">
    <value>The file you have selected is too large. Please select a file smaller than {0} MB</value>
  </data>
  <data name="Please choose a file" xml:space="preserve">
    <value>Please choose a file</value>
  </data>
  <data name="UseCompanyHeaderForInvoice" xml:space="preserve">
    <value>Revert to company header on invoice?</value>
  </data>
  <data name="ApplyOnCI" xml:space="preserve">
    <value> Apply On Client Invoice</value>
  </data>
  <data name="ApplyOnPO" xml:space="preserve">
    <value>Apply On PO</value>
  </data>
  <data name="Click to be reminded again in" xml:space="preserve">
    <value>Click to be reminded again in</value>
  </data>
  <data name="YourMessageHasBeenSent" xml:space="preserve">
    <value>Your message has been sent</value>
  </data>
  <data name="NextNumberTooSmall" xml:space="preserve">
    <value>The current number is {0}. You cannot enter a sequence number lower than this.</value>
  </data>
  <data name="NoCommunicationLogItems" xml:space="preserve">
    <value>No communication log items</value>
  </data>
  <data name="Restricted Manufacturer Warning" xml:space="preserve">
    <value>The manufacturer you have selected is subject to global sanctions and cannot be used.</value>
  </data>
  <data name="FileNotFound" xml:space="preserve">
    <value>The file {0} is no longer available. Please check with IT support.</value>
  </data>
  <data name="CannotUploadMoreThan" xml:space="preserve">
    <value>Can't upload more than {0} file(s)</value>
  </data>
  <data name="CannotUploadMoreThanImages" xml:space="preserve">
    <value>Can't upload more than {0} image(s)</value>
  </data>
  <data name="NoMultipleFileUploadMessage" xml:space="preserve">
    <value>Multiple file uploads are not supported. Please upload one file at a time</value>
  </data>
  <data name="PageNotFound" xml:space="preserve">
    <value>Page Not Found</value>
  </data>
  <data name="ApplicationSavedSuccessfully" xml:space="preserve">
    <value>Application Saved Successfully</value>
  </data>
  <data name="IHSServiceUnavailable" xml:space="preserve">
    <value>Sorry, the IHS part lookup service is not currently available.</value>
  </data>
  <data name="ApplicationAuthorizedSuccessfully" xml:space="preserve">
    <value>Application Authorized Successfully</value>
  </data>
  <data name="ApplicationRejectedSuccessfully" xml:space="preserve">
    <value>Application Rejected Successfully</value>
  </data>
  <data name="ApplicationUpdatedSuccessfully" xml:space="preserve">
    <value />
  </data>
  <data name="Sorry, the database call has timed out" xml:space="preserve">
    <value>Sorry, the database call has timed out</value>
  </data>
  <data name="No global sales access users selected" xml:space="preserve">
    <value>No global sales access users selected</value>
  </data>
  <data name="PurchaseRequest" xml:space="preserve">
    <value>HUBRFQ {0} sent for Price Request</value>
  </data>
  <data name="Please click on refresh button to view record" xml:space="preserve">
    <value>Please click on refresh button to view record</value>
  </data>
  <data name="Expiry Date must be later than or equal to the Start Date." xml:space="preserve">
    <value>Expiry Date must be later than or equal to the Start Date.</value>
  </data>
  <data name="Start Date must be earlier than or equal to the Expiry Date." xml:space="preserve">
    <value>Start Date must be earlier than or equal to the Expiry Date.</value>
  </data>
  <data name="No Sourcing Links" xml:space="preserve">
    <value>No Sourcing Links</value>
  </data>
  <data name="HUBRFQRaised" xml:space="preserve">
    <value>HUBRFQ Raised {0} : Reverse Logistic Offer Match</value>
  </data>
  <data name="RLStockSubject1" xml:space="preserve">
    <value>A HUBRFQ {0} raised for RL In-Stock</value>
  </data>
  <assembly alias="System.Windows.Forms" name="System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
  <data name="PartWatchMatchSubject" type="System.Resources.ResXNullRef, System.Windows.Forms">
    <value />
  </data>
  <data name="HUBRFQNoBid" xml:space="preserve">
    <value>HUBRFQ No-Bid</value>
  </data>
  <data name="HUBRFQReleased" xml:space="preserve">
    <value>HUBRFQ Released</value>
  </data>
  <data name="SORRequestSubject" xml:space="preserve">
    <value>Sales Order ~SONO~ Approval Request</value>
  </data>
  <data name="OGEL Approval Status" xml:space="preserve">
    <value>OGEL Approval Status</value>
  </data>
  <data name="Need to complete open Tasks" xml:space="preserve">
    <value>Need to complete open Tasks</value>
  </data>
  <data name="SendExportApproval" xml:space="preserve">
    <value>Sales Order {0} Export Approval Notification</value>
  </data>
  <data name="Date Promised should be between current date and end of the current calendar month" xml:space="preserve">
    <value>Date Promised should be between current date and end of the current calendar month</value>
  </data>
  <data name="Date Promised should be between current date and end of promise day month" xml:space="preserve">
    <value>Date Promised should be between current date and end of promise day month</value>
  </data>
  <data name="Order quantity cannot be less than shipped quantity" xml:space="preserve">
    <value>Order quantity cannot be less than shipped quantity</value>
  </data>
  <data name="Notify Sales Order" xml:space="preserve">
    <value>Sales Order {0} Notification</value>
  </data>
  <data name="Part Closed Cannot Clone Or Add To Requirement" xml:space="preserve">
    <value>The selected requirement is closed, so no additional offers can be added.</value>
  </data>
  <data name="SOAuthorise" xml:space="preserve">
    <value>Sales order has been checked: {0}</value>
  </data>
  <data name="CreateIPOSubject" xml:space="preserve">
    <value>New IPO created</value>
  </data>
  <data name="CreateIPOSuccess" xml:space="preserve">
    <value>Thank you for your order. Please note your order will not be processed until your Sales Order is checked</value>
  </data>
  <data name="SOLRecalled" xml:space="preserve">
    <value>Sourcing Result provided by PO HUb for this salesorder line(s) ( {0} ) are recalled. Please contact your buyer</value>
  </data>
  <data name="CreateIPOAlreadyExist" xml:space="preserve">
    <value>IPO already created from sales order.</value>
  </data>
  <data name="CreateIPOFailedNotEnoughStock" xml:space="preserve">
    <value>Available Stock is running out, please check the associated Stock or update SO's QTY</value>
  </data>
  <data name="CreateIPOFailed" xml:space="preserve">
    <value>Some error occured while processing your request.</value>
  </data>
  <data name="SupplierERAIReported" xml:space="preserve">
    <value>Supplier Is Erai Reported</value>
  </data>
  <data name="PartIsERAIReported" xml:space="preserve">
    <value>Product Is Erai High Risk</value>
  </data>
  <data name="EccnDifferMessage" xml:space="preserve">
    <value>There is a difference between SO Line's ECCN Code and PO Line's ECCN Code</value>
  </data>
</root>